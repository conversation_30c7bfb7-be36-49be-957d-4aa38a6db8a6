import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class QuickMenuList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            userId: "",
            userName: "",
            menuParentId: "",
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { userId, userName, menuParentId } = route.params;
            if (userId) {
                console.log('componentWillMount==userId:', userId);
                this.setState({
                    userId: userId
                })
            }
            if (userName) {
                this.setState({
                    userName: userName
                })
            }
            if (menuParentId != null) {
                console.log('componentWillMount==menuParentId:', menuParentId);
                this.setState({
                    menuParentId: menuParentId
                })
                this.loadQuickMenuList(userId, menuParentId)
            }
        }
    }

    loadQuickMenuList = (userId, menuParentId) => {
        console.log('====loadQuickMenuList==userId:', userId, "=menuParentId:", (menuParentId ? menuParentId : this.state.menuParentId));
        let loadTypeUrl;
        let loadRequest;
        loadTypeUrl = "/biz/portal/user/getMenuList";
        loadRequest = {
            'userId': userId ? userId : this.state.userId,
            "menuClass": "C",
            'menuId': 0
        };
        httpPost(loadTypeUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data && response.data.menuDTOList) {
                this.setState({
                    dataSource: response.data.menuDTOList
                })
            }
        });
    }

    loadQuickMenuListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteQuickMenu = (userId, menuId) => {
        console.log("=======delete=userId=menuId", userId, menuId);
        let url = "/biz/portal/user/user_delete_menu";
        let requestParams = { 'userId': userId, 'menuId': menuId };
        console.log("=======delete=requestParams:", requestParams);
        httpDelete(url, requestParams, this.deleteQuickMenuCallBack);
    }

    // 删除操作的回调操作
    deleteQuickMenuCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.loadQuickMenuList(this.state.userId, this.state.menuParentId);
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    renderRow = (item, index) => {
        return (
            <View key={item.menuId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>菜单名称：{item.menuName}</Text>
                </View>
                <View style={[{ flexDirection: 'row', flexWrap: 'wrap', width: screenWidth * 0.95, justifyContent: 'flex-start' }]}>
                    {item.menuDTOList.map((item, key) => {
                        return (
                            <Text style={[styles.titleTextStyle, { margin: 5, backgroundColor: '#F5F5F5', borderRadius: 4, padding: 5 }]}>{item.menuName}</Text>
                        )
                    })}
                </View>

                <View style={CommonStyle.itemBottomBtnStyle}>
                    <TouchableOpacity onPress={() => {
                        Alert.alert('确认', '您确定要删除【' + item.menuName + '】菜单吗？', [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteQuickMenu(this.state.userId, item.menuId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 90, flexDirection: 'row' }
                        ]}>
                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    {
                        item.menuName === "工作台" ? <TouchableOpacity onPress={() => {
                            this.props.navigation.navigate("QuickMenuAdd",
                                {
                                    // 传递参数
                                    userId: this.state.userId,
                                    menuParentId: item.menuId,
                                    // 传递回调函数
                                    refresh: this.loadQuickMenuList
                                })
                        }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle, { width: 90, flexDirection: 'row' }
                            ]}>
                                <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>子菜单</Text>
                            </View>
                        </TouchableOpacity> : <View />
                    }

                </View>
            </View>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("QuickMenuAdd",
                    {
                        userId: this.state.userId,
                        menuParentId: this.state.menuParentId,
                        // 传递回调函数
                        refresh: this.loadQuickMenuList
                    })
            }}>
                <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }
    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={false} />
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={this.state.userName + '首页快捷菜单管理'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});
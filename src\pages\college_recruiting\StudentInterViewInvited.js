import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class StudentInterViewInvited extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            inviteStaff:"Y",
            qryPosition:"Y"
        }
    }


    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadInterviewList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/collection/staff/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "inviteStaff":this.state.inviteStaff,
            "staffId": constants.loginUser.staffId,
            "qryPosition":this.state.qryPosition
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/collection/staff/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "inviteStaff":this.state.inviteStaff,
            "staffId": constants.loginUser.staffId,
            "qryPosition":this.state.qryPosition
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadInterviewList();
    }

    loadInterviewList=()=>{
        let url= "/biz/collection/staff/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "inviteStaff":this.state.inviteStaff,
            "staffId": constants.loginUser.staffId,
            "qryPosition":this.state.qryPosition
        };
        httpPost(url, loadRequest, this.loadInterviewListCallBack);
    }

    loadInterviewListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    acceptItem=(item)=>{
        if(item.staffAttitude == 'N'){
            return;
        }else{
            let url= "/biz/collection/staff/modify";
            let requestParams={
                collectionId:item.collectionId,
                staffAttitude:item.staffAttitude == 'Y' ? "I":"Y",
            };
            httpPost(url, requestParams, this.saveCollectionCallBack);
        }
    }

    refuseItem=(item)=>{
        if(item.staffAttitude == 'Y'){
            return;
        }else{
            let url= "/biz/collection/staff/modify";
            let requestParams={
                collectionId:item.collectionId,
                staffAttitude:item.staffAttitude == 'N' ? "I":"N",
            };
            httpPost(url, requestParams, this.saveCollectionCallBack);
        }
    }

    // 保存回调函数
    saveCollectionCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                this.callBackFunction();
                toastOpts = getSuccessToastOpts('操作成功');
                WToast.show(toastOpts);
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    renderRow=(item)=>{
        return (
            <View key={item.collectionId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>企业名称：{item.enterpriseName}</Text>
                </View>
                <View style={[styles.titleViewStyle, { justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                    <Text style={styles.titleTextStyle}>招聘岗位：</Text>
                    {item.positionDTOList.map((elem, key) => {
                        if ((key + 1) === item.positionDTOList.length) {
                            return (
                                <TouchableOpacity onPress={()=>{this.props.navigation.navigate("EnterprisecrHiringPositionDetail", 
                                {
                                    // 传递回调函数
                                    positionId: elem.positionId,
                                    refresh: this.callBackFunction,
                                    
                                })}}>
                                    <Text style={[styles.titleTextStyle, { marginLeft: 5, marginBottom: 5, padding: 2, backgroundColor: (key % 2 == 0 ? 'rgba(255,0,0,0.1)' : 'rgba(0,255,0,0.1)') }]}>{elem.positionName}</Text>
                                </TouchableOpacity>
                            )
                        }
                        else {
                            return (
                                <TouchableOpacity onPress={()=>{this.props.navigation.navigate("EnterprisecrHiringPositionDetail", 
                                {
                                    // 传递回调函数
                                    positionId: elem.positionId,
                                    refresh: this.callBackFunction,
                                    
                                })}}>
                                    <Text style={[styles.titleTextStyle, { marginLeft: (key == 0 ? -5 : 5), marginBottom: 5, padding: 2, backgroundColor: (key % 2 == 0 ? 'rgba(255,0,0,0.1)' : 'rgba(0,255,0,0.1)') }]}>{elem.positionName}</Text>
                                </TouchableOpacity>
                            )
                        }
                    })}
                </View>
                <View style={styles.titleViewStyle}>
                {
                    item.staffAttitude == 'Y' ?
                    <Text style={{fontSize:14,color:'#FF8C28'}}>已接受该面试邀请</Text>
                    :
                    (
                        item.staffAttitude == 'N' ?
                        <Text style={{fontSize:14,color:'#FD4246'}}>已拒绝该面试邀请</Text>
                        :
                        null
                    )
                }
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>              
                    <TouchableOpacity onPress={()=>{this.acceptItem(item)}}>
                        {
                            item.staffAttitude === "N" ? 
                            <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle,{width: 80,borderColor:'rgba(145, 147, 152, 0.5)',flexDirection:"row"}]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/closeGrey.png')}></Image> */}
                                <Text style={[{ color: 'rgba(145, 147, 152, 0.5)', fontSize: 14, lineHeight: 20 }]}>接受邀请</Text>
                            </View>
                            :
                            <View style={[{
                                width: 80,height: 28,
                                margin: 10, marginRight: 0,
                                borderColor: '#FF8C28',borderWidth: 0.85,borderRadius: 6,
                                flexDirection: "row",
                                justifyContent: 'center',alignItems: 'center',
                            }]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                <Text style={[{ color: '#FF8C28', fontSize: 14, lineHeight: 20 }]}>{item.staffAttitude == 'Y' ? "取消接受" : "接受邀请"}</Text>
                            </View>  
                        }
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{this.refuseItem(item)}}>
                        {
                            item.staffAttitude === "Y" ?
                            <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle,{marginRight:15,width: 80,borderColor:'rgba(145, 147, 152, 0.5)',flexDirection:"row"}]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/closeGrey.png')}></Image> */}
                                <Text style={[{ color: 'rgba(145, 147, 152, 0.5)', fontSize: 14, lineHeight: 20 }]}>拒绝邀请</Text>
                            </View>
                            :
                            <View style={[{
                                width: 80,
                                height: 28,
                                flexDirection: "row",
                                justifyContent: 'center',
                                alignItems: 'center',
                                margin: 10,
                                marginRight: 15,
                                borderColor: '#FD4246',
                                borderWidth: 0.85,
                                borderRadius: 6
                            }]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                <Text style={[{ color: '#FD4246', fontSize: 14, lineHeight: 20 }]}>{item.staffAttitude == 'N' ? "取消拒绝" : "拒绝邀请"}</Text>
                            </View>
                        }
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='面试邀请'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        ItemSeparatorComponent={this.space}
                        ListEmptyComponent={this.emptyComponent}
                        renderItem={({item}) => this.renderRow(item)}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0
    },
    innerViewStyle: {
        marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 14,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
})
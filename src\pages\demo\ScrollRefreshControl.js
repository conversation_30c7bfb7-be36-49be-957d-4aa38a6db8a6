import React, { Component } from 'react';

import {
    StyleSheet,
    ScrollView,
    Text,
    RefreshControl
} from 'react-native';

export default class ScrollRefreshControl extends Component{

    //state数据
    state = { text: '初始状态', refreshing: false };

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);

            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    render(){
        return (
            <ScrollView
                style={[styles.flex,styles.bgColor]}
                contentContainerStyle={{flex: 1, alignItems: 'center',justifyContent: 'center'}}
                indicatorStyle={'black'}
                showsHorizontalScrollIndicator={true}
                bounces={true}
                refreshControl={
                    <RefreshControl
                        tintColor={'red'}
                        titleColor={'brown'}
                        title={'正在刷新......'}
                        refreshing={this.state.refreshing}
                        onRefresh={this._onRefresh.bind(this)}
                    />
                }
            >
                <Text>{this.state.text}</Text>
            </ScrollView>
        )
    }
}

const styles = StyleSheet.create({
    flex: {
        flex: 1
    },
    bgColor: {
        backgroundColor:'#EEE'
    }
});
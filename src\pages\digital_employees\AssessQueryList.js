import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Linking, Clipboard,
    FlatList, RefreshControl, TextInput, Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class AssessQueryList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate: "",
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,

            topBlockLayoutHeight: 0,
            selAssessRecordStateCode: 'all',

            showSearchItemBlock: false,

            departmentDataSource: null,

            selDepartmentId: null,
            selDepartmentName: null,
            selDepartmentStaffList: null,
            selStaffId: null,
            selStaffName: null,

            qryStartTime: null,
            selectedQryStartDate: [],
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }
    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }
    initqryStartTime = () => {
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 1);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _qryStartTime = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectedQryStartDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            qryStartTime: _qryStartTime
        })
        return _qryStartTime;
    }
    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        var _qryStartTime = this.initqryStartTime();
        console.log('componentWillMount==_qryStartTime', _qryStartTime);
        let loadTypeUrl = "/biz/department/list_for_tenant";
        let loadRequest = { "qryAll_NoPower": "Y", "currentPage": 1, "pageSize": 1000 };
        httpPost(loadTypeUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    departmentDataSource: response.data,
                })
            }
        });

        let assessRecordState = [
            {
                stateCode: 'all',
                stateName: '全部',
            },
            {
                stateCode: '0AA',
                stateName: '未审批',
            },
            {
                stateCode: '0AB',
                stateName: '已审批',
            }
        ]
        this.setState({
            assessRecordState: assessRecordState,
        })

        this.loadAssessRecordList(_qryStartTime);
        // const { route, navigation } = this.props;
        // if (route && route.params) {
        //     const { selStaffId } = route.params;
        //     if (selStaffId) {
        //         console.log("=============selStaffId" + selStaffId + "");
        //         this.setState({
        //             selStaffId: selStaffId,
        //         })
        //         this.loadAssessRecordList(_qryStartTime, selStaffId);
        //     }
        //     else {
        //         // this.loadAssessRecordList(_qryStartTime);
        //     }
        // }
        // else {
        //     this.loadAssessRecordList(_qryStartTime);
        // }
    }
    // 回调函数
    callBackFunction = () => {
        let url = "/biz/assess/record/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,
            "departmentId": this.state.selDepartmentId,
            "applyUserId": this.state.selStaffId,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/assess/record/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,
            "departmentId": this.state.selDepartmentId,
            "applyUserId": this.state.selStaffId,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadAssessRecordList();
    }

    loadAssessRecordList = (_qryStartTime, selStaffId) => {
        let url = "/biz/assess/record/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,
            "qryStartTime": _qryStartTime ? _qryStartTime : this.state.qryStartTime,
            "departmentId": this.state.selDepartmentId,
            "applyUserId": selStaffId ? selStaffId : this.state.selStaffId,
        };
        httpPost(url, loadRequest, this.loadAssessRecordListCallBack);
    }

    loadAssessRecordListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log("response.data.dataList=============", response.data.dataList)
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    deleteAssessRecoed = (assessRecordId) => {
        console.log("=======delete=assessRecordId", assessRecordId);
        let url = "/biz/assess/record/delete";
        let requestParams = { 'assessRecordId': assessRecordId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }
    renderAssessRecordStateRow = (item, index) => {
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={() => {
                    let selAssessRecordStateCode = item.stateCode;
                    this.setState({
                        "selAssessRecordStateCode": selAssessRecordStateCode
                    })
                    let loadUrl = "/biz/assess/record/list";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "assessRecordState": selAssessRecordStateCode === 'all' ? null : selAssessRecordStateCode,
                        "qryStartTime": this.state.qryStartTime,
                        "departmentId": this.state.selDepartmentId,
                        "applyUserId": this.state.selStaffId,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.stateCode} style={[CommonStyle.tabItemViewStyle]}>
                        <Text style={[item.stateCode === this.state.selAssessRecordStateCode ?
                            [CommonStyle.selectedtabItemTextStyle]
                            :
                            [CommonStyle.tabItemTextStyle]
                        ]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow = (item, index) => {
        return (
            <View key={item.planId} style={[CommonStyle.innerViewStyle]}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>申请日期：{item.applyDate}</Text>
                    {
                        item.assessRecordState ==="0AB" ? 
                        <View>
                            <Text style={{color:"#3ab240"}}>已审批</Text>
                        </View>
                        :
                        <View>
                            <Text style={{color:'rgba(255,0,0,0.4)'}}>未审批</Text>
                        </View>
                    }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>申请人：{item.applyUserName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核标题：{item.assessTitle}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核内容：{item.assessContent}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核类别：{item.assessClassName}</Text>
                </View>
                {
                    !item.assessDifficulty ?
                        <View />
                        :
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>考核难度：{item.assessDifficultyName}</Text>
                        </View>
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核人：{item.assessUserName}</Text>
                </View>
                {
                    item.assessRecordState=='0AB'?
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>预计考核日期：{item.expectAssessDate}</Text>
                    </View>
                     :
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle,{marginBottom:10}]}>预计考核日期：{item.expectAssessDate}</Text>
                    </View>
                }
                {
                    item.assessRecordState=='0AB'?
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>实际考核日期：{item.actualAssessDate}</Text>
                        </View>
                        :
                        <View />
                }
                {
                    item.assessRecordState=='0AB'?
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>考核结果：{item.assessResultName}</Text>
                        </View>
                        :
                        <View />
                }
                {
                    item.assessRecordState=='0AB'?
                        <View style={styles.titleViewStyle}>
                            <Text style={[styles.titleTextStyle,{marginBottom:10}]}>考核意见：{item.assessOpinion? item.assessOpinion:"无"}</Text>
                        </View>
                        :
                        <View />
                }
            </View>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 部门
    renderDepartmentRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selDepartmentId: item.departmentId,
                    selDepartmentName: item.departmentName,
                    selDepartmentStaffDataSource: item.departmentUserDTOList,
                    selStaffId: null,
                    selStaffName: null,
                })
            }}>
                <View key={"department_" + item.departmentId} style={[item.departmentId === this.state.selDepartmentId ?
                    {
                        backgroundColor: 'rgba(83,100,255,0.1)'
                    }
                    :
                    {
                        backgroundColor: "rgba(246,246,246,1)"
                    }
                    ,
                {
                    //外边距
                    marginLeft: 6,
                    marginRight: 6,
                    marginTop: 8,
                    marginBottom: 0,
                    //内边距
                    paddingTop: 5, paddingBottom: 5,
                    paddingLeft: 15, paddingRight: 15,
                    borderRadius: 4,
                    justifyContent: 'center',
                    height: 30,
                    borderRadius: 4
                }
                ]}>
                    <Text style={[item.departmentId === this.state.selDepartmentId ?
                        {
                            color: 'rgba(30,110,250,1)'
                        }
                        :
                        {
                            color: 'rgba(0,10,32,0.45)'
                        }
                        ,
                    {
                        fontSize: 16
                    }
                    ]}>
                        {item.departmentName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    renderDepartmentStaffRow = (item, index) => {
        return (
            <View key={item.jobUserId} >
                <TouchableOpacity onPress={() => {
                    this.setState({
                        selStaffId: item.userId,
                        selStaffName: item.staffName,
                    })
                    console.log(item)
                }}>
                    <View key={"jobuser_" + item.jobUserId} style={[item.userId === this.state.selStaffId ?
                        {
                            backgroundColor: 'rgba(83,100,255,0.1)'
                        }
                        :
                        {
                            backgroundColor: "rgba(246,246,246,1)"
                        }
                        ,
                    {
                        //外边距
                        marginLeft: 6,
                        marginRight: 6,
                        marginTop: 8,
                        marginBottom: 0,
                        //内边距
                        paddingTop: 5, paddingBottom: 5,
                        paddingLeft: 15, paddingRight: 15,
                        borderRadius: 4,
                        justifyContent: 'center',
                        height: 30,
                        borderRadius: 4
                    }
                    ]}>
                        <Text style={[item.userId === this.state.selStaffId ?
                            {
                                color: 'rgba(30,110,250,1)'
                            }
                            :
                            {
                                color: 'rgba(0,10,32,0.45)'
                            }
                            ,
                        {
                            fontSize: 16
                        }
                        ]}>
                            {item.staffName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                Alert.alert('确认', '您确定要导出PDF文件吗？', [
                    {
                        text: "取消", onPress: () => {
                            WToast.show({ data: '点击了取消' });
                        }
                    },
                    {
                        text: "确定", onPress: () => {
                            WToast.show({ data: '点击了确定' });
                            this.exportPdfFile()
                        }
                    }
                ]);
            }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 23, height: 23 }} source={require('../../assets/icon/iconfont/outputBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }
    // 显示搜索项目
    showSearchItemSelect() {
        if (!this.state.departmentDataSource || this.state.departmentDataSource.length < 1) {
            WToast.show({ data: "请先添加部门" });
            return
        }
        this.setState({
            showSearchItemBlock: true,
        })
    }
    exportPdfFile = () => {
        console.log("=======exportPdfFile");
        let url= "/biz/generate/pdf/assess_query";

        // // if(this.state.selDepartmentId == null) {
        // //     WToast.show({data:'请选择部门'});
        // //     return ;
        // // }
        // // if(this.state.selStaffId == null) {
        // //     WToast.show({data:'请选择提交人'});
        // //     return ;
        // // }
        let requestParams={
            "applyUserId":this.state.selStaffId,
            "qryStartTime": this.state.qryStartTime,
            "currentPage": 1,
            "pageSize": 1000,
            "departmentId":this.state.selDepartmentId,
            "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data); 
                WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
                Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                    {
                        text:"不打开", onPress:()=>{
                        WToast.show({data:'点击了不打开'});
                        }
                    },
                    {
                        text:"打开", onPress:()=>{
                            WToast.show({data:'点击了打开'});
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }
    openQryStartDate() {
        this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate)
    }

    callBackSelectQryStartDateValue(value) {
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryStartDate: value
        })
        if (value && value.length) {
            var qryStartTime = "";
            var vartime;
            for (var index = 0; index < value.length; index++) {
                vartime = value[index];
                if (index === 0) {
                    qryStartTime += vartime;
                }
                else {
                    qryStartTime += "-" + vartime;
                }
            }
            this.setState({
                qryStartTime: qryStartTime
            })
            let loadUrl = "/biz/assess/record/list";
            let loadRequest = {
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "departmentId": this.state.selDepartmentId,
                "applyUserId": this.state.selStaffId,
                "qryStartTime": qryStartTime,
                "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 25, height: 25 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='考核查询'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <View style={[CommonStyle.rightTop50FloatingBlockView,
                {
                    height: 32,
                    width: 110,
                    opacity: 0.6,
                    borderRadius: 8,
                    backgroundColor: "rgba(242, 245, 252, 1)"
                }]}>
                    <TouchableOpacity onPress={() => this.openQryStartDate()}>
                        <Text style={{ color: 'rgba(0,10,32,0.85)', fontSize: 14 }}>
                            {!this.state.qryStartTime ? "时间" : this.state.qryStartTime}
                        </Text>
                    </TouchableOpacity>
                </View>

                <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                    {
                        (this.state.assessRecordState && this.state.assessRecordState.length > 0)
                            ?
                            this.state.assessRecordState.map((item, index) => {
                                return this.renderAssessRecordStateRow(item)
                            })
                            : <View />
                    }
                    </View>
                    <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                        <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                        {
                                        this.state.showSearchItemBlock ?
                                            <View style={[CommonStyle.choseToSearchViewStyle]}>
                                                <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                                    {this.state.selDepartmentId && this.state.selDepartmentName ? (this.state.selDepartmentName) : "选择部门"}
                                                </Text>
                                                <Image style={[CommonStyle.choseToSearchClosedIconSize]} source={require('../../assets/icon/iconfont/arrow-up.png')}></Image>
                                            </View>
                                            :
                                            <View style={[CommonStyle.choseToSearchViewStyle]}>
                                                <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                                    {this.state.selDepartmentId && this.state.selDepartmentName ? (this.state.selDepartmentName) : "选择部门"}
                                                </Text>
                                                <Image style={[CommonStyle.choseToSearchOpenedIconSize]} source={require('../../assets/icon/iconfont/arrow_down.png')}></Image>
                                            </View>
                                    }
                        </TouchableOpacity>
                        {
                            this.state.selStaffId && this.state.selStaffName
                                ? <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                                    {
                                        this.state.showSearchItemBlock ?
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                        <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                            {this.state.selStaffName}
                                        </Text>
                                    </View>
                                    :
                                    <View style={[CommonStyle.choseToSearchViewStyle]}>
                                        <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                            {this.state.selStaffName}
                                        </Text>
                                    </View>
                                    }
                                </TouchableOpacity>
                                : null
                        }
                    </View>
                </View>

                <View>
                {
                        this.state.showSearchItemBlock ?
                            <View style={[CommonStyle.choseToSearchBigBoxViewStyle,{height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                            <View style={CommonStyle.heightLimited}>
                            <ScrollView>
                                
                                <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                    <View style={[{backgroundColor: 'rgba(255,255,255,1)'},CommonStyle.choseToSearchItemsViewSize]}>
                                        <Text style={{ fontSize: 16, fontWeight: 'bold' }}>部门：</Text>
                                    </View>
                                    {
                                        (this.state.departmentDataSource && this.state.departmentDataSource.length > 0)
                                            ?
                                            this.state.departmentDataSource.map((item, index) => {
                                                return this.renderDepartmentRow(item)
                                            })
                                            : null
                                    }
                                </View>
                                {
                                    (this.state.selDepartmentStaffDataSource && this.state.selDepartmentStaffDataSource.length > 0)
                                        ?
                                        <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                            <View style={[{backgroundColor: 'rgba(255,255,255,1)'},CommonStyle.choseToSearchItemsViewSize]}>
                                                <Text style={{ fontSize: 16, fontWeight: 'bold' }}>提交人：</Text>
                                            </View>
                                            {
                                                this.state.selDepartmentStaffDataSource.map((item, index) => {
                                                    return this.renderDepartmentStaffRow(item)
                                                })
                                            }
                                        </View>
                                        : null
                                }
                            </ScrollView> 
                            </View>
                                <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                <TouchableOpacity onPress={() => {
                                    let loadUrl = "/biz/assess/record/list";
                                    let loadRequest = {
                                        "currentPage": 1,
                                        "pageSize": this.state.pageSize,
                                        "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,
                                        "departmentId": this.state.selDepartmentId,
                                        "applyUserId": this.state.selStaffId,
                                        "qryStartTime": this.state.qryStartTime,
                                    };
                                    console.log("选择的部门=====" + this.state.selDepartmentId)
                                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                    this.setState({
                                        showSearchItemBlock: false,
                                    })
                                }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                        :
                        null
                }
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
                </View>
                <BottomScrollSelect
                    ref={'SelectQryStartDate'}
                    callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerHeadViewStyle:{
        borderColor:"#ffffff",
        borderWidth:4,
        backgroundColor:"#ffffff"
    },
    // 分段器样式
    blockItemViewStyle: {
        margin: 5,
        width: 60, 
        borderRadius: 0,
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        width: 60, borderRadius: 0, 
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: "#FFFFFF", 
    },
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});
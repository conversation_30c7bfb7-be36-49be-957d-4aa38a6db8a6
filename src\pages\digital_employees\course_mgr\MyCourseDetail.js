import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Linking, Clipboard,
    FlatList, RefreshControl, Image, Modal, TextInput,ImageBackground,ScrollView,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../../utils/CameraRollUtils';
import moment from 'moment';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;

export default class MyCourseDetail extends Component{
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,

            topBlockLayoutHeight:0,

            syCourseDto:{},


            courseSort:"0",
            courseName:"无",
            taskState:"",
            courseTypeName:"无",
            courseContent:"暂无",
            coursePhoto:null,//课程封面

            selTaskStateCode:'c',
            taskStateDataSource:[
                {
                    stateCode: 'a',
                    stateName: '视频',
                    lenths:0,
                },
                {
                    stateCode: 'b',
                    stateName: '文档',
                    lenths:0,
                },
                {
                    stateCode: 'c',
                    stateName: '进展',
                    lenths:0,
                }
            ],
            vidioDataSource:[
            ],
            documentDataSource:[
            ],
            trackingDataSource:[
            ],
            maxlen:0,
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { courseTaskId ,courseId} = route.params;
            this.loadCourse(courseTaskId,courseId,constants.loginUser.userId);
        }
    }

    loadCourse(courseTaskId,courseId,userId){
        console.log("入参",courseTaskId,courseId,userId)
        let url = "/biz/course/get";
        let loadRequest = {
            "courseTaskId": courseTaskId,
            "courseId": courseId,
            "userId": userId
        };
        httpPost(url, loadRequest, this._loadCourseTaskCallBack);
    }
    _loadCourseTaskCallBack=(response)=>{    
        console.log(response.data)    
        if (response.code == 200 && response.data ) {

            console.log("课程详情"  +JSON.stringify(response.data, null, 6) );

            var syCourseDto=response.data

        //     {
        //         "courseId": 121,
        //         "courseName": "CSS中级",
        //         "courseContent": "",
        //         "coursePhoto": "image_a/72/2024-04/rn_image_picker_lib_temp_ed16305f-7d53-48ea-b80c-3529f36013fb_1838423842_crop.jpg",
        //         "courseSort": 1,
        //         "orCourseSort": null,
        //         "courseLevel": 1,
        //         "courseState": "0AA",
        //         "tenantId": 72,
        //         "gmtCreated": "2024-04-16 19:04:15",
        //         "gmtModified": "2024-04-19 18:38:44",
        //         "version": 2,
        //         "scene": "D",
        //         "courseLevelId": 23,
        //         "courseDuration": 10,
        //         "courseTypeId": 116,
        //         "courseTypeName": "专业基础课程",
        //         "courseLevelName": "P2",
        //         "taskState": "0AA",
        //         "checkOutUserId": 349,
        //         "planCompletionTime": "2024-04-29",
        //         "actualCompletionTime": null,
        //         "taskGmtCreated": "2024-04-19 17:09:17",
        //         "courseTaskId": 139,
        //         "portalTrackDetailDTOList": null,
        //         "userId": null,
        //         "excelRowIndex": null,
        //         "taskNum": null,
        //         "syDocumentDTOList": null,
        //         "syCourseTaskDTO": {
        //               "courseTaskId": 139,
        //               "courseId": 121,
        //               "coursePhoto": "image_a/72/2024-04/rn_image_picker_lib_temp_ed16305f-7d53-48ea-b80c-3529f36013fb_1838423842_crop.jpg",
        //               "checkOutUserId": 349,
        //               "checkInUserId": null,
        //               "planCompletionTime": "2024-04-29",
        //               "actualCompletionTime": null,
        //               "taskState": "0AA",
        //               "gmtCreated": "2024-04-19 17:09:17",
        //               "gmtModified": null,
        //               "version": 0,
        //               "tenantId": 72,
        //               "checkOutUserName": "积分1",
        //               "checkInUserName": null,
        //               "checkOutUserPhoto": "image_a/72/2024-04/rn_image_picker_lib_temp_5b1d4fec-2eb8-4ed1-b75d-aa12e5d6f620_1353275327_small.jpg",
        //               "checkInUserPhoto": null,
        //               "courseName": "CSS中级",
        //               "courseContent": "",
        //               "courseDurations": 10,
        //               "courseSort": 1,
        //               "courseTypeName": "专业基础课程",
        //               "courseTypeId": null,
        //               "courseLevelId": null,
        //               "courseLevelName": "P2",
        //               "portalTrackDetailDTOList": [
        //                     {
        //                           "trackId": 122,
        //                           "trackType": "CT",
        //                           "trackFkId": 139,
        //                           "trackRemark": "进展1",
        //                           "operator": "积分1",
        //                           "trackState": "0AA",
        //                           "tenantId": 72,
        //                           "gmtCreated": "2024-04-20 11:03:05",
        //                           "gmtModified": null,
        //                           "version": 0,
        //                           "userId": 349
        //                     }
        //               ],
        //               "documentTypeList": null,
        //               "syDocumentDTOList": null
        //         }
        //   }
        let a=syCourseDto.syCourseTaskDTO!=null?syCourseDto.syCourseTaskDTO.syDocumentDTOList.filter((item) => item.documentType === 'TV').length:0
        let b=syCourseDto.syCourseTaskDTO!=null?syCourseDto.syCourseTaskDTO.syDocumentDTOList.filter((item) => item.documentType === 'TD').length:0
        let c=syCourseDto.syCourseTaskDTO!=null&&syCourseDto.syCourseTaskDTO.portalTrackDetailDTOList!=null?syCourseDto.syCourseTaskDTO.portalTrackDetailDTOList.length:0

            this.setState({

                courseId:syCourseDto.courseId,//课程id
                courseDuration:syCourseDto.courseDuration,//课程持续时间

                courseSort:syCourseDto.courseSort,
                courseName:syCourseDto.courseName,
                taskState:(syCourseDto.syCourseTaskDTO?syCourseDto.syCourseTaskDTO.taskState:null),
                courseTypeName:syCourseDto.courseTypeName,
                courseContent:syCourseDto.courseContent,
                coursePhoto:syCourseDto.coursePhoto,
                // checkOutUserPhoto:syCourseDto.syCourseTaskDTO.checkOutUserPhoto,
                // checkOutUserName:syCourseDto.syCourseTaskDTO.checkOutUserName,

                documentDataSource: syCourseDto.syCourseTaskDTO!=null?syCourseDto.syCourseTaskDTO.syDocumentDTOList.filter((item) => item.documentType === 'TD'):[],
                vidioDataSource: syCourseDto.syCourseTaskDTO!=null?syCourseDto.syCourseTaskDTO.syDocumentDTOList.filter((item) => item.documentType === 'TV'):[],

                trackingDataSource: syCourseDto.syCourseTaskDTO!=null&&syCourseDto.syCourseTaskDTO.portalTrackDetailDTOList!=null?syCourseDto.syCourseTaskDTO.portalTrackDetailDTOList:[],

                taskStateDataSource:[
                    {
                        stateCode: 'a',
                        stateName: '视频',
                        lenths:syCourseDto.syCourseTaskDTO!=null?syCourseDto.syCourseTaskDTO.syDocumentDTOList.filter((item) => item.documentType === 'TV').length:0,
                    },
                    {
                        stateCode: 'b',
                        stateName: '文档',
                        lenths:syCourseDto.syCourseTaskDTO!=null?syCourseDto.syCourseTaskDTO.syDocumentDTOList.filter((item) => item.documentType === 'TD').length:0,
                    },
                    {
                        stateCode: 'c',
                        stateName: '进展',
                        lenths:syCourseDto.syCourseTaskDTO!=null&&syCourseDto.syCourseTaskDTO.portalTrackDetailDTOList!=null?syCourseDto.syCourseTaskDTO.portalTrackDetailDTOList.length:0,
                    }
                ],
                maxlen:a>b?(a>c?a:c):(b>c?b:c)
            })
            console.log(a>b?(a>c?a:c):(b>c?b:c))
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    addCourseTask = () => {
        var dateTime = new Date();
        console.log("dateTime1=====" + dateTime);
        dateTime = dateTime.setHours(dateTime.getHours() + 8);
        dateTime = new Date(dateTime);
        dateTime = dateTime.setDate(dateTime.getDate() + this.state.courseDuration);
        dateTime = new Date(dateTime);
        console.log("dateTime3=====" + dateTime);
        let Y = dateTime.getFullYear() + '-';
        let M = (dateTime.getMonth() + 1 < 10 ? '0' + (dateTime.getMonth() + 1) : dateTime.getMonth() + 1) + '-';
        let D = (dateTime.getDate() < 10 ? '0' + (dateTime.getDate()) : dateTime.getDate());
        let date = Y + M + D;
        // console.log("dateTime4=====", date);
        let requestUrl = "/biz/course/task/add";
        let requestParams = {
            'courseId': this.state.courseId,
            'planCompletionTime': date,
            'checkOutUserId': constants.loginUser.userId,

        };
        httpPost(requestUrl, requestParams, this.addCourseTaskCallBack)

    }
    // 开始学习按操作的回调
    addCourseTaskCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "该课程学习任务已开始" });
            this.setState({taskState:"0AA"})
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            if (response.message == "重复检验失败") {
                response.message = "任务已开始，无需重复点击";
                WToast.show({ data: response.message });
            }
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }
    renderTaskStateRow = (item, index) => {
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={() => {
                    let selTaskStateCode = item.stateCode;
                    this.setState({
                        "selTaskStateCode": selTaskStateCode
                    })
                }}>
                    <View key={item.stateCode} style={CommonStyle.tabItemViewStyle}>
                        <Text style={[item.stateCode === this.state.selTaskStateCode ?
                            [CommonStyle.selectedtabItemTextStyle]
                            :
                            [CommonStyle.tabItemTextStyle]
                        ]}>
                            {item.stateName+"("+item.lenths+")"}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    vidioRenderRow = (item, index) => {
        return (
            <View>
            <View key={item.vidioId+"vidioId"} style={{
                backgroundColor:"white",
                // backgroundColor:"red",
                marginTop:2,
                height:80,
                flexDirection: 'row',
                alignItems:"center"
                }}>
                <View style={{
                    // backgroundColor:"green",
                    //外边距
                    marginLeft: 14,
                    marginRight: 0,
                    marginTop: 0,
                    marginBottom: 0,
                    height:48,
                    width:48,
                    justifyContent: 'center',
                    alignItems:"center"
                }}>
                    <Text>{index+1}</Text>
                </View>
                <View style={{
                    // backgroundColor:"red",
                    //外边距
                    marginLeft: 11,
                    marginRight: 0,
                    marginTop: 0,
                    marginBottom: 0,
                    height:48,
                    width:screenWidth-48-14-11-16-24,
                    justifyContent: 'space-between',
                }}>
                    <View style={{
                        // backgroundColor:"green",
                        height:23,justifyContent: 'center',
                    }}>
                        <Text style={{fontSize:16}}>{item.documentName}</Text>
                    </View>
                    <View style={{
                        // backgroundColor:"green",
                        height:23,flexDirection: 'row',alignItems:"center"
                    }}>
                        <View style={{
                            width:42,
                            height:20,
                            borderColor:"#ECEEF2",
                            borderRadius:2,
                            borderWidth:1,
                            justifyContent: 'center',
                            alignItems:"center"
                        }}>
                            <Text style={{ fontSize: 12}}>视频</Text>
                        </View>
                        {/* <Image style={{ height: 13 , width: 12, marginLeft: 10}} source={require('../../../assets/icon/iconfont/clock.png')}></Image>
                        <Text style={{ fontSize:12,marginLeft: 10}}>45分     已学完</Text> */}
                    </View>
                </View>
                <TouchableOpacity onPress={
                    ()=>{
                        console.log("播放",JSON.stringify(item, null, 6))
                        this.props.navigation.navigate("CourseVidioList", {
                            dataItem:{...item,vidioDataSource:this.state.vidioDataSource},
                            // 传递回调函数
                            refresh: this.callBackFunction
                        })
                    }
                }>
                    <View style={{
                        // backgroundColor:"green",
                        //外边距
                        marginLeft: 0,
                        marginRight: 16,
                        marginTop: 0,
                        marginBottom: 0,
                        height:24,
                        width:24
                    }}>
                        <Image style={{ height: 24 , width: 24}} source={require('../../../assets/icon/iconfont/play_new.png')}></Image>
                    </View>
                </TouchableOpacity>
                 
            </View>
            <View style={styles.lineViewStyle}/>
            </View>
        )
    }
    doucumentRenderRow = (item, index) => {
        return (
            <View>
            <View key={item.vidioId+"vidioId"} style={{
                backgroundColor:"white",
                marginTop:2,
                height:80,
                flexDirection: 'row',
                alignItems:"center"
                }}>
                <View style={{
                    // backgroundColor:"green",
                    //外边距
                    marginLeft: 14,
                    marginRight: 0,
                    marginTop: 0,
                    marginBottom: 0,
                    height:48,
                    width:48,
                    justifyContent: 'center',
                    alignItems:"center"
                }}>
                    <Text>{index+1}</Text>
                </View>
                <View style={{
                    // backgroundColor:"red",
                    //外边距
                    marginLeft: 11,
                    marginRight: 0,
                    marginTop: 0,
                    marginBottom: 0,
                    height:48,
                    width:screenWidth-48-14-11-16-24,
                    justifyContent: 'space-between',
                }}>
                    <View style={{
                        // backgroundColor:"green",
                        height:23,justifyContent: 'center',
                    }}>
                        <Text style={{fontSize:16}}>{item.documentName}</Text>
                    </View>
                    <View style={{
                        // backgroundColor:"green",
                        height:23,flexDirection: 'row',alignItems:"center"
                    }}>
                        <View style={{
                            width:42,
                            height:20,
                            borderColor:"#ECEEF2",
                            borderRadius:2,
                            borderWidth:1,
                            justifyContent: 'center',
                            alignItems:"center"
                        }}>
                            <Text style={{ fontSize: 12}}>文档</Text>
                        </View>
                        {/* <Image style={{ height: 13 , width: 12, marginLeft: 10}} source={require('../../../assets/icon/iconfont/clock.png')}></Image>
                        <Text style={{ fontSize:12,marginLeft: 10}}>45分     已学完</Text> */}
                    </View>
                </View>

                <TouchableOpacity onPress={()=>{
                        this.props.navigation.navigate("ViewDocument", { documentId: item.documentId, documentName: item.documentName })
                }}>
                    <View style={{
                        // backgroundColor:"green",
                        //外边距
                        marginLeft: 0,
                        marginRight: 16,
                        marginTop: 0,
                        marginBottom: 0,
                        height:24,
                        width:24
                    }}>
                        <Image style={{ height: 24 , width: 24}} source={require('../../../assets/icon/iconfont/preview_new.png')}></Image>
                    </View>
                </TouchableOpacity>
            </View>
            <View style={styles.lineViewStyle}/>
            </View>
        )
    }
    trackingRenderRow = (item, index) => {
        return (
            <View key={item.trackingId+"trackingId"} style={{
                backgroundColor:"white",
                marginTop:2}}>
                {/* 成果顶部信息 */}
                <View style={{flexDirection: 'row', marginLeft: 14,marginRight:11, marginTop: 11,alignItems:"center",
                    // backgroundColor:"red"
                }}>
                    {
                        constants.loginUser.userPhoto ?
                            <Image source={{ uri:constants.image_addr + '/'+ constants.loginUser.userPhoto }} style={{ height: 32, width: 32, borderRadius: 50}} />
                            :
                            <ImageBackground source={require('../../../assets/icon/iconfont/profilePicture.png')} style={{ width: 32, height: 32}}>
                                <View style={{height: 32,width:32,justifyContent: "center",alignItems: "center"}}>
                                    {
                                        constants.loginUser.userName&&constants.loginUser.userName <= 2 ? 
                                        <Text style={{color:'#FFFFFF',fontSize:12,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {constants.loginUser.userName}
                                        </Text>
                                        :
                                        <Text style={{color:'#FFFFFF',fontSize:12,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {constants.loginUser.userName?constants.loginUser.userName.slice(-2):""}
                                        </Text>
                                    }
                                </View>
                            </ImageBackground>
                    }
                    <View style={{marginLeft:11, flexDirection: 'row' ,width:screenWidth-14*2-32-35-11,
                    // backgroundColor:"white",
                    alignItems:"center"}}>
                        <Text style={{ fontSize: 16 }}>{constants.loginUser.userName}</Text>

                            <Image style={{ height: 13 , width: 12, marginLeft: 9}} source={require('../../../assets/icon/iconfont/clock.png')}></Image>
                            <Text style={{ fontSize: 13 ,marginLeft: 3}}>{item.gmtCreated}</Text>
                    </View>
                </View>
                <View style={{marginLeft: 55,marginRight: 11 ,marginBottom:17,
                    // backgroundColor:"green"
                    }}>
                    <Text style={{fontSize:12,color:'rgba(0, 10, 32, 0.65)'}}>{item.trackRemark}</Text>
                </View>
                <View style={styles.lineViewStyle}/> 
            </View>
        )
    }

    renderTitleItem=()=>{
        return (
            <Text style={styles.headCenterTitleText}>{'第' + this.state.courseSort+ '课 ' + this.state.courseName}
            </Text>
        )
    }

    render(){

        return(
            <View >
                <CommonHeadScreen
                    titleItem={() =>this.renderTitleItem()}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                {/* 封面 */}
                <View style={{         
                    //外边距
                    // justifyContent: 'center',
                    // alignItems:"center"
                }}>
                    {
                    this.state.coursePhoto?
                        <Image style={{ width: screenWidth, height: screenWidth*0.637}} source={{uri:constants.image_addr + '/' + this.state.coursePhoto }}/>
                        :
                        <View>
                            <View style={{         
                                //外边距
                                position: 'absolute',
                                // backgroundColor:"white",
                                height:143,width:313,
                                // justifyContent: 'center',
                                alignItems:"center",
                                zIndex: 1,
                                top: (screenWidth*0.637-143)/2,
                                left: (screenWidth-313)/2,
                            }}>
                                <Text style={{fontSize:30,color:"white"}}>{'第' + this.state.courseSort + '课'}</Text>
                                <Text style={{fontSize:30,color:"white"}}>{this.state.courseName}</Text>                        
                            </View>
                            <Image style={{ width: screenWidth, height: screenWidth*0.637}} source={require('../../../assets/image/defaultCover.png')}/>                        
                        </View>
                    }
                </View>
                {/* 详情 */}
                <View style={{position: 'relative',top:-28,zIndex: 2}}>
                    {/* 标题与头部 */}
                    <View style={{ width: screenWidth,backgroundColor:"white",borderTopLeftRadius:10,borderTopRightRadius:10 ,
                    }}>
                        <View style={{ flexDirection: 'row' ,height:23+8,borderTopLeftRadius:10,borderTopRightRadius:10}}>
                            <View style={{         
                                //外边距
                                marginLeft: 13,
                                marginRight: 0,
                                marginTop: 8,
                                marginBottom: 0,
                                width: screenWidth-55-13
                            }}
                            >
                                <Text>{'第' + this.state.courseSort + '课 ' + this.state.courseName}</Text>
                            </View>

                            {
                                this.state.taskState?
                                    (
                                    this.state.taskState === '0AA' ?
                                        <View style={{         
                                            //外边距
                                            marginLeft: 0,
                                            marginRight: 0,
                                            marginTop: 0,
                                            marginBottom: 0,
                                            borderTopRightRadius:10,
                                            borderBottomLeftRadius:10,
                                            backgroundColor:"#1E6EFA",
                                            height:19,width:55
                                        }}
                                        >
                                            <Text style={{color:"white",marginLeft:5}}>学习中</Text>
                                        </View>
                                        :
                                        (
                                            this.state.taskState === '0BB' ?
                                                <View style={{         
                                                    //外边距
                                                    marginLeft: 0,
                                                    marginRight: 0,
                                                    marginTop: 0,
                                                    marginBottom: 0,
                                                    borderTopRightRadius:10,
                                                    borderBottomLeftRadius:10,
                                                    backgroundColor:"#FD4246",
                                                    height:19,width:55
                                                }}
                                                >
                                                    <Text style={{color:"white",marginLeft:5}}>已超期</Text>
                                                </View> 
                                                :
                                                (
                                                    this.state.taskState === '0CC' ?
                                                    <View style={{         
                                                        //外边距
                                                        marginLeft: 0,
                                                        marginRight: 0,
                                                        marginTop: 0,
                                                        marginBottom: 0,
                                                        borderTopRightRadius:10,
                                                        borderBottomLeftRadius:10,
                                                        backgroundColor:"green",
                                                        height:19,width:55
                                                    }}
                                                    >
                                                        <Text style={{color:"white",marginLeft:5}}>已完成</Text>
                                                    </View>
                                                        :
                                                        <Text />
                                                )
                                        )
                                    )                  
                                :
                                <View/>
                            }                
                        </View>
                        <View style={{         
                                //外边距
                                // backgroundColor:"red",
                                marginLeft: 14,
                                marginRight: 14,
                                // paddingTop: 7,
                                // paddingBottom: 7,
                                height:20+7+7,
                                flexDirection:"row",
                                justifyContent:"space-between",
                                alignItems:"center"
                            }}>
                            <View  style={{         
                                    //外边距
                                    borderRadius:10,
                                    backgroundColor:'rgba(27,188,130,0.2)',
                                    height:20,
                                    paddingLeft:10,paddingRight:10,
                                    justifyContent: 'center',
                                    alignItems:"center"
                                }}>
                                <Text
                                    style={{fontSize:12,color:"#1BBC82"}}
                                >{this.state.courseTypeName}</Text>
                            </View>

                            {
                                (this.state.taskState==null||this.state.taskState=="0XX")
                                &&  this.state.courseSort==1
                                ?
                                <TouchableOpacity onPress={
                                    ()=>{
                                        console.log("开始学习")
                                        this.addCourseTask()
                                    }
                                }>
                                    <View  style={{         
                                            //外边距
                                            borderRadius:20,
                                            backgroundColor:'rgba(30, 110, 250, 1)',
                                            height:32,
                                            paddingLeft:20,paddingRight:20,
                                            justifyContent: 'center',
                                            alignItems:"center"
                                        }}>
                                        <Text
                                            style={{fontSize:12,color:"white"}}
                                        >{"开始学习"}</Text>
                                    </View>  
                                </TouchableOpacity>  
                                :
                                <View/>                              
                            }
                    
                        </View>

                        <View  style={{         
                                //外边距
                                marginLeft: 14,
                                marginRight: 14,
                                marginTop: 14,
                                marginBottom: 14,
                                borderRadius:4,
                                backgroundColor:'#F8F9FC',
                                // backgroundColor:"green",
                                width:screenWidth-25
                            }}>
                                <Text
                                    style={{fontSize:12,color:"rgba(0,10,32,0.85)",margin:5}}
                                >{"课程内容："+(this.state.courseContent?this.state.courseContent:"无")}</Text>
                        </View>

                        <View style={styles.lineViewStyle}/> 
                    </View>
                    {/* tab分类与列表 */}
                    <View style={{
                    // borderColor:"blue",borderWidth:10,
                    // height:"100%",
                    }}>
                        {/* tab分类 */}
                        <View style={[CommonStyle.headViewStyle]}>
                            <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                                {
                                    (this.state.taskStateDataSource && this.state.taskStateDataSource.length > 0)
                                        ?
                                        this.state.taskStateDataSource.map((item, index) => {
                                            return this.renderTaskStateRow(item)
                                        })
                                        : <View />
                                }
                            </View>
                        </View>
                        {/* 列表 */}
                        <View style={{height:this.state.maxlen*(82)+28+28}}>
                            {
                                this.state.selTaskStateCode==='a'?
                                <FlatList
                                    data={this.state.vidioDataSource}
                                    renderItem={({ item, index }) => this.vidioRenderRow(item, index)}
                                    ListEmptyComponent={this.emptyComponent}
                                />
                                :
                                <View></View>                            
                            }
                            {
                                this.state.selTaskStateCode==='b'?
                                <FlatList
                                    data={this.state.documentDataSource}
                                    renderItem={({ item, index }) => this.doucumentRenderRow(item, index)}
                                    ListEmptyComponent={this.emptyComponent}
                                />
                                :
                                <View></View>                            
                            }
                            {
                                this.state.selTaskStateCode==='c'?
                                <FlatList
                                    data={this.state.trackingDataSource}
                                    renderItem={({ item, index }) => this.trackingRenderRow(item, index)}
                                    ListEmptyComponent={this.emptyComponent}
                                />
                                :
                                <View></View>                            
                            }
                        </View>
                    </View>
                </View>
                </ScrollView>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    headCenterTitleText:{
        fontSize:20,
        color:'#000000',
        fontWeight:'600',
     },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentTextStyle: {
        marginLeft: 12,
        marginRight: 16,
        marginTop: 3,
        lineHeight: 24,
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },    
    lineViewStyle:{
        // height:1,
        marginLeft: 13,
        marginRight: 13,
        // marginTop: 15,
        // marginBottom: 6,
        borderBottomWidth: 1,
        // borderColor:'red',
        borderColor:'#E8E9EC'
    },
});

import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Linking,
    FlatList, RefreshControl, Clipboard, Image,ImageBackground,Modal,TextInput,
    KeyboardAvoidingView,Platform
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;

var screenHeight = Dimensions.get('window').height;
export default class HarvestMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            standardType: "E",
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            qryStartTime: null,
            selectedQryStartDate: [],
            currentTime: "",
            harvestItem: {},
            deleteModal: false,
            editModal:false,
            discussModal:false,
            userPhotoUrl: constants.image_addr + '/' + constants.loginUser.userPhoto,
            userPhoto: "",
            exportPdfModal:false,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    initGmtCreated = () => {
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var currentHour = ("0" + (currentDate.getHours() + 8)).slice(-2);
        var currentMinute = ("0" + currentDate.getMinutes()).slice(-2);
        var currentSecond = ("0" + currentDate.getSeconds()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay + " " + currentHour + ":" + currentMinute + ":" + currentSecond;
        return _gmtCreated;
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        var currentTime = this.initGmtCreated();
        this.setState({
            currentTime: currentTime
        })
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectedQryStartDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            // qryStartTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })
        httpPost("/biz/portal/user/get", { "userId": constants.loginUser.userId }, (response) => {
            if (response.code === 200) {
                let userPhoto = response.data.userPhoto;
                this.setState({
                    userPhotoUrl: constants.image_addr + '/' + userPhoto,
                    userPhoto: userPhoto
                })
                console.log("=========userPhoto===",userPhoto)
            }
        });
        this.loadHarvestList();
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/harvest/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/harvest/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    // 保存留言
    saveMessage =()=> {
        console.log("=======saveMessage");
        let toastOpts;
        if (!this.state.messageContent) {
            toastOpts = getFailToastOpts("请输入留言内容");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/portal/message/board/add";
        let requestParams={
            messageContent:this.state.messageContent,
            messageFkId: this.state.harvestItem.harvestId,
            parentMessageId: this.state.parentMessageId,
            messageFkType:"C"
        };
        httpPost(url, requestParams, this.saveMessageCallBack);
    }
    
    // 保存留言的回调函数
    saveMessageCallBack=(response)=>{
        this.setState({
            messageContent: ""
        })
        let toastOpts;
        switch (response.code) {
            case 200:
                WToast.show({ data: "留言发送成功" });
                this.callBackFunction();
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadHarvestList();
    }

    loadHarvestList = () => {
        let url = "/biz/harvest/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this.loadHarvestListCallBack);
    }

    loadHarvestListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteHarvest = (harvestId) => {
        console.log("=======delete=harvestId", harvestId);
        let url = "/biz/harvest/delete";
        let requestParams = { 'harvestId': harvestId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    renderRow = (item, index) => {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HarvestDetail", {
                    // 传递参数
                    harvestId: item.harvestId,
                    userName: item.userName,
                    // 传递回调函数
                    refresh: this.callBackFunction
                })
            }}>
            <View key={item.harvestId} style={[CommonStyle.innerViewStyle]}>
                {/* 成果顶部信息 */}
                <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
                    {
                        this.state.userPhoto ?
                            <Image source={{ uri: this.state.userPhotoUrl }} style={{ height: 48, width: 48, borderRadius: 50}} />
                            :
                            <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                    {
                                        item.userName.length <= 2 ? 
                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {item.userName}
                                        </Text>
                                        :
                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {item.userName.slice(-2)}
                                        </Text>
                                    }
                                </View>
                            </ImageBackground>
                    }
                 
                    <View style={{marginLeft:11, flexDirection: 'column'}}>
                        <View style={{flexDirection: 'row', marginTop: 4 }}>
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={{ fontSize: 16 }}>{item.userName}的成果</Text>
                            </View>
                            {/* 草稿,优秀,私密,无 */}
                            {
                                    item.harvestState === "0BB" ?
                                    <View style={{ width: 38, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#E63633' }}>
                                        <Text style={{fontSize: 13, color: '#FFFFFF' }}>草稿</Text>
                                    </View>
                                    :
                                    <View></View>
                                }
                                {
                                    item.harvestState === "0AA" && item.visible === 'N' ?
                                    <View style={{ width: 38, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#B0B9BF' }}>
                                        <Text style={{fontSize: 13, color: '#FFFFFF' }}>私密</Text>
                                    </View>
                                        :
                                    <View></View>
                                }
                                {
                                    item.harvestState === "0AA" && item.visible === 'Y' && item.score ===1 ?
                                    <View style={{ width: 58, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#E63633' }}>
                                        <Image style={{ width: 16, height: 18, marginRight: 2, marginRight: 3 }} source={require('../../assets/icon/good.png')}></Image>
                                        <Text style={{fontSize: 13, color: '#FFFFFF' }}>优秀</Text>
                                    </View>
                                        :
                                        <View></View>
                                }
                        </View>
                        <View style={{flexDirection: 'row'}}>
                            <Image style={{ height: 13 , width: 12, marginTop: 5,marginLeft: 1, marginRight: 5}} source={require('../../assets/icon/iconfont/clock.png')}></Image> 
                            {
                                item.gmtModified == null ?
                                <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                                    <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)' }]}>{item.gmtCreated.slice(0,10)} 提交</Text>
                                </View>
                                :
                                <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                                    <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)' }]}>{item.gmtModified.slice(0,10)} 提交</Text>
                                </View>
                            }
                        </View>
                    </View>
                    <View style={{ position:'absolute', right: 13, top: 13}}>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                editModal: true,
                                harvestItem: item
                            })
                        }}>
                            <View style={[{width: 35, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                                <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
                {/* 分隔线 */}
                <View style={styles.lineViewStyle}/>
                <View style={styles.titleViewStyle}>
                    <Text style={{fontSize:16}}>标题</Text>
                </View>
                <View style={styles.itemContentTextStyle}>
                    <Text style={[styles.itemContentStyle]}>{item.harvestTitle}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>内容</Text>
                </View>
                <View style={styles.itemContentTextStyle}>
                    <Text style={styles.itemContentStyle}>{item.harvestContent}</Text>
                </View>
                
                {/* <View style={{
                    width: 40, height: 40,
                    backgroundColor: 'rgba(255,0,0,0.0)',
                    position: 'absolute',
                    alignItems: 'center',
                    justifyContent: 'center',
                    right: 10, top: 5,
                }}>
                    <TouchableOpacity onPress={() => {
                        // this.setGood(item)
                    }}>
                        {
                            item.score ?
                                <Image style={{ width: 50, height: 50 }} source={require('../../assets/icon/iconfont/good-red.png')}></Image>
                                :
                                <View />
                        }
                    </TouchableOpacity>
                </View> */}
                {
                    item.auditScore ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>审核得分：{item.auditScore}</Text>
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>审核意见：</Text>
                            </View>
                            <View style={styles.itemContentTextStyle}>
                                <Text style={styles.itemContentStyle}>{item.auditOpinion}</Text>
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>审核人：</Text>
                                <Text style={styles.itemContentStyle}>{item.auditOperator}</Text>
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>审核时间：</Text>
                                <Text style={styles.itemContentStyle}>{item.auditTime}</Text>
                            </View>
                        </View> :
                        <View></View>
                }

                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                    {/* <TouchableOpacity onPress={() => {
                        if (dateDiffHours(this.state.currentTime, item.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                            return;
                        }
                        Alert.alert('确认', '您确定要删除吗？', [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                    // this在这里可用，传到方法里还有问题
                                    // this.props.navigation.goBack();
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteHarvest(item.harvestId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle, { width: 64 }
                            , (item.auditScore || dateDiffHours(this.state.currentTime, item.dailyDate) > constants.loginUser.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""]}>
                            <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                        </View>
                    </TouchableOpacity> */}
                    {/* {
                        item.harvestState === "0BB" ?
                            <TouchableOpacity onPress={() => {
                                if (dateDiffHours(this.state.currentTime, item.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                    return;
                                }
                                this.props.navigation.navigate("HarvestMgrAdd",
                                    {
                                        // 传递参数
                                        harvestId: item.harvestId,
                                        // 传递回调函数
                                        refresh: this.callBackFunction
                                    })
                            }}>
                                <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64 }
                                    , (item.auditScore || dateDiffHours(this.state.currentTime, item.dailyDate) > constants.loginUser.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""]}>
                                    <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                    <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }}>编辑</Text>
                                </View>
                            </TouchableOpacity>
                            :
                            null
                    } */}
                    {/* {
                        item.harvestState === "0BB" ?
                            <View />
                            :
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.props.navigation.navigate("HarvestDiscussList",
                                        {
                                            // 传递参数
                                            harvestId: item.harvestId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }}>
                                    <View style={[{
                                        width: 64,
                                        height: 28,
                                        flexDirection: "row",
                                        // justifyContent: 'center',
                                        alignItems: 'center',
                                        margin: 10,
                                        marginRight: 15,
                                        borderColor: 'rgba(30, 110, 250, 1)',
                                        borderWidth: 0.85,
                                        borderRadius: 6
                                    }]}>
                                        <Image style={{ width: 24, height: 24, marginRight: 6, marginLeft: 5 }} source={require('../../assets/icon/iconfont/newMessageBlack.png')}></Image>
                                        <Text style={[{ color: 'rgba(83, 106, 247, 1)', fontSize: 14 }]}>{item.messageNum}</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                    } */}
                </View>
                {/* 留言 按钮*/}
                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                    {
                        item.harvestState === "0BB" ?
                            <View></View>
                            :
                            <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        discussModal: true,
                                        harvestItem: item
                                    })
                                }}>
                                    <View style={[{width: 78, height: 28, flexDirection: "row", alignItems: 'center', margin: 10, 
                                        marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                                    }]}>
                                        <Image style={{ width: 20, height: 20, marginRight: 8, marginLeft: 12 }} source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.65)', fontSize: 14, lineHeight: 24 }]}>留言</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                    }
                </View>
                {/* 留言 */}
                {
                    (item.messageList && item.messageList.length > 0) ?
                        <View style={{backgroundColor:'rgba(242, 245, 252, 0.5)', borderRadius:10,width:screenWidth-24, marginLeft: 12, marginRight: 12, paddingTop: 5, marginBottom: 5}}>
                            {
                                item.messageList.map((item, index)=>{
                                    return(
                                        <View key={item.messageId} style={{ flexDirection: 'row', marginLeft: 10, marginTop: 10, marginBottom: 10}}>
                                            {
                                                item.operatorPhoto ?
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.operatorPhoto) }} style={{ height: 36, width: 36, borderRadius: 50}} />
                                                    :
                                                    <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ height: 36, width: 36}}>
                                                        <View style={{height: 36, width: 36,justifyContent: "center",alignItems: "center"}}>
                                                            {
                                                                item.operatorName <= 2 ? 
                                                                <Text style={{color:'#FFFFFF',fontSize:13,fontWeight:"normal",textAlign:'center', lineHeight:20}}>
                                                                    {item.operatorName}
                                                                </Text>
                                                                :
                                                                <Text style={{color:'#FFFFFF',fontSize:13,fontWeight:"normal",textAlign:'center', lineHeight:20,}}>
                                                                    {item.operatorName.slice(-2)}
                                                                </Text>
                                                            }
                                                        </View>
                                                    </ImageBackground>
                                            }

                                            <View style={{ flexDirection: 'column', marginLeft: 10, flex: 1}}>
                                                <View style={{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'center', paddingTop: 4 }}>
                                                    <View style={{ flexDirection: 'row'}}>
                                                        <Text style={{ fontSize: 16 }}>{item.operatorName}</Text>
                                                    </View>
                                                    <View style={{ flexDirection: 'row', marginLeft: 6}}>
                                                        <Text style={[{ fontSize: 12, color: 'rgba(0,10,32,0.45)' }]}>{item.gmtCreated.slice(0,16)}</Text>
                                                    </View>
                                                    
                                                </View>

                                                {
                                                    item.parentMessageId ?
                                                        <View style={[{flexDirection: 'column', justifyContent: 'flex-start'}]}>
                                                            <View style={[{flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'flex-start', marginLeft: 9, marginTop: 11}]}>
                                                                <Text style={[styles.itemContentStyle, {color:'rgba(0,10,32,0.45)'}]}>{"回复 "+ item.parentUserName + ": "+ item.parentMessageContent}</Text>
                                                            </View>
                                                            <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 8}]}>
                                                                <Text style={styles.itemContentStyle}>{item.messageContent}</Text>
                                                            </View>
                                                        </View>
                                                        :
                                                        <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 10}]}>
                                                            <Text style={styles.itemContentStyle}>{item.messageContent}</Text>
                                                        </View>
                                                }
                                            </View>
                                        </View>
                                    )                           
                                })
                            }
                        </View>
                        :
                        <View/>
                }
                

            </View>
            </TouchableOpacity>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HarvestMgrAdd",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/addBlack.png')}></Image>

            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    openQryStartDate() {
        this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate)
    }

    callBackSelectQryStartDateValue(value) {
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryStartDate: value
        })
        if (value && value.length) {
            var qryStartTime = "";
            var vartime;
            for (var index = 0; index < value.length; index++) {
                vartime = value[index];
                if (index === 0) {
                    qryStartTime += vartime;
                }
                else {
                    qryStartTime += "-" + vartime;
                }
            }
            this.setState({
                qryStartTime: qryStartTime
            })

            let loadUrl = "/biz/harvest/list";
            let loadRequest = {
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "userId": constants.loginUser.userId,
                "qryStartTime": qryStartTime,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }
    }

    resetQry() {
        this.setState({
            qryStartTime: null,
        })
        let loadUrl = "/biz/harvest/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "qryStartTime": null,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    exportPdfFile = () => {
        console.log("=======exportPdfFile");
        let url = "/biz/generate/pdf/harvest";
        let requestParams = {
            "currentPage": 1,
            "pageSize": 1000,
            "userId": constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
            "harvestState": "0AA"
        };
        httpPost(url, requestParams, (response) => {
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data);
                // WToast.show({ data: "导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data });
                // 直接打开外网链接 
                Linking.openURL(response.data)
                // Alert.alert('确认', '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?', [
                //     {
                //         text: "不打开", onPress: () => {
                //             WToast.show({ data: '点击了不打开' });
                //         }
                //     },
                //     {
                //         text: "打开", onPress: () => {
                //             WToast.show({ data: '点击了打开' });
                //             // 直接打开外网链接 
                //             Linking.openURL(response.data)
                //         }
                //     }
                // ]);
            }
        });
    }


    render() {
        return (
            <View>
                <CommonHeadScreen title='我的成果'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                {/* <View style={CommonStyle.rightTop50FloatingBlockView}>
                    <Text style={CommonStyle.rightTop50FloatingBlockText}>{this.state.dataSource.length}</Text>
                </View> */}
                <View style={{
                    //内边距
                    padding: 0,
                    paddingTop: 0, paddingBottom: 0,
                    paddingRight: 0, paddingLeft: 0,
                    //外边距
                    margin: 0,
                    marginTop: 0, marginBottom: 0,
                    marginLeft: 0, marginRight: 0,
                    //主轴均匀空白填充
                    justifyContent: "space-evenly",
                    //主轴横向
                    flexDirection: 'row',
                    //交叉轴居中
                    alignItems: 'center',
                    index: 1000,
                    // flexWrap: 'wrap',
                    borderWidth: 0,
                    borderRadius: 1,
                    height: 48,
                    backgroundColor: '#FFFFFF',
                }} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={[styles.inputRowStyle,
                    {
                        justifyContent: "space-between",
                        alignItems: 'center',
                        margin: 0,
                        marginTop: 0,
                        marginLeft: 0,
                        width: screenWidth / 1.4,
                        backgroundColor: '#F2F5FC',
                        height: 34,
                        borderRadius: 15
                    }]}>
                        <TouchableOpacity onPress={() => this.openQryStartDate()}>
                            <View style={{ alignItems: 'center', flexDirection: 'row', width: screenWidth / 1.9, backgroundColor: "#F2F5FC", borderRadius: 80 }}>
                                <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                <Text style={{ color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 5 }}>
                                    {!this.state.qryStartTime ? "提交日期" : this.state.qryStartTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => this.resetQry()}>
                            <View style={[CommonStyle.resetBtnViewStyle, { width: 10, borderWidth: 0, backgroundColor: 'rgba(0,0,0,0)', borderRadius: 20 }]}>
                                <Image style={{ width: 16, height: 16 }} source={require('../../assets/icon/iconfont/replace.png')}></Image>
                            </View>
                        </TouchableOpacity>
                    </View>
                    {/* <TouchableOpacity onPress={()=>this.resetQry()}>
                        <View style={[CommonStyle.resetBtnViewStyle, { width: 70,marginLeft:20,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/replace.png')}></Image>
                            <Text style={CommonStyle.resetBtntextStyle}>重置</Text>
                        </View>
                    </TouchableOpacity> */}
                    <TouchableOpacity onPress={() => {
                        // 触发-导出弹窗Modal
                        this.setState({
                            exportPdfModal: true
                        })
                        
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle,
                        {
                            margin: 0,
                            alignItems: 'center',
                            width: 64,
                            backgroundColor: '#1E6EFA',
                            height: 32,
                            borderRadius: 20
                        }]}>
                            {/* <View style={[ CommonStyle.itemBottomDetailBtnViewStyle,{ width: 64, height: 32, backgroundColor: "#1E6EFA", flexDirection: "row", borderRadius: 20,alignItems: 'center' }]}> */}
                            {/* <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/output.png')}></Image> */}
                            <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { fontSize: 14 }]}>导出</Text>
                        </View>
                    </TouchableOpacity>

                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
                <BottomScrollSelect
                    ref={'SelectQryStartDate'}
                    callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
                />

                {/* 留言输入框弹窗 */}
                <Modal
                    animationType='slide'
                    transparent={true}
                    visible={this.state.discussModal}
                >
                    <KeyboardAvoidingView
                    behavior={Platform.OS == "ios" ? "padding" : "height"}
                    style={{flex:1}}
                    >
                        <TouchableOpacity style={{flex: 1, position: 'relative'}}
                            onPress={() => {
                                this.setState({
                                    discussModal: false,
                                    messageContent: ""
                                })
                        }}>
                            <View style={{backgroundColor: '#FFFFFF', flexDirection: 'row', alignItems: 'center',
                                position: 'absolute', width: '100%', left: 0, bottom: 0, padding: 5
                            }}>
                                <TextInput 
                                    autoFocus
                                    multiline={true}
                                    placeholder="小小鼓励，让团队更凝聚"
                                    style={{backgroundColor: '#F2F5FC', flex: 5, borderRadius: 15, height: 40, marginLeft: 10, paddingLeft: 15}}
                                    onChangeText={(text) => this.setState({ messageContent: text })}
                                />
                                <TouchableOpacity onPress={() => {
                                    if (!this.state.messageContent) {
                                        return;
                                    }
                                    this.setState({
                                        discussModal: false,
                                    })
                                    this.saveMessage();
                                }}>
                                    <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{flex: 1,width: 64, height: 32, backgroundColor: '#1E6EFA', borderRadius: 20 }, 
                                        (this.state.messageContent) ? "" : CommonStyle.disableViewStyle]}>
                                        <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { textAlign: 'center', fontSize: 14 }]}>发送</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </TouchableOpacity>
                    </KeyboardAvoidingView>
                </Modal>
                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.editModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    if (this.state.harvestItem.harvestState != "0BB" || this.state.harvestItem.auditScore || dateDiffHours(this.state.currentTime, this.state.harvestItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                        WToast.show({ data: '该成果不可编辑' });
                                        return;
                                    }
                                    this.setState({
                                        editModal: false,
                                    })
                                    this.props.navigation.navigate("HarvestMgrAdd",
                                        {
                                            // 传递参数
                                            harvestId: this.state.harvestItem.harvestId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , (this.state.harvestItem.harvestState != "0BB" || this.state.harvestItem.auditScore || this.state.harvestItem.auditScore || dateDiffHours(this.state.currentTime, this.state.harvestItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""]}>
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => {
                                    console.log("harvestItem=================",this.state.harvestItem)
                                    // 删除弹窗Modal
                                    this.setState({
                                        editModal: false,
                                        deleteModal: true
                                    })
                                    
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , (this.state.harvestItem.harvestState == "0XX") ? CommonStyle.disableViewStyle : ""]}>
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        editModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17,fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该成果?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17,fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteHarvest(this.state.harvestItem.harvestId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17,fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 导出pdf弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.exportPdfModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认导出成果？</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>导出地址已复制到粘贴板，使用浏览器打开</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 291, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        exportPdfModal: false
                                    })
                                    WToast.show({ data: '点击了不打开' });
                                }}>

                                    <View style={{ width: 145, height: 56, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#000A20', }}>不打开</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    WToast.show({ data: '点击了打开' });
                                    this.setState({
                                        exportPdfModal: false
                                    })
                                    this.exportPdfFile()
                                }}>

                                    <View style={{ width: 145, height: 56, alignItems: 'center', justifyContent: 'center', borderLeftWidth: 1, borderColor: '#DFE3E8' }}>
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA' }}>打开</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        // marginTop:10,
        borderLeftColor: '#FFFFFF',
        borderRightColor: '#FFFFFF',
        borderTopColor: '#F4F4F4',
        borderBottomColor: '#F4F4F4',
        borderWidth: 4
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#FFFFFF",
        backgroundColor: "#FFFFFF",
        borderRadius: 5,
        marginTop: 5
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentTextStyle: {
        marginLeft: 14,
        marginRight: 16,
        marginTop: 3,
        lineHeight: 24,
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 14,
        marginRight: 16,
        marginTop: 5
    },
    titleTextStyle: {
        fontSize: 16,
        lineHeight: 22
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        marginBottom: 6,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
});
import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, TouchableOpacity, Dimensions, Image } from 'react-native';
import { WToast } from 'react-native-smart-tip'

import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;


export default class MemberTypeAdd extends Component {
    constructor() {
        super()
        this.state = {
            operate: "",
            portraitTypeId: "",
            portraitTypeName: "",
            // courseTypeDesc: "",
            portraitTypeSort: 0,
            // courseTypeNameArrays: []
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { portraitTypeId } = route.params;

            if (portraitTypeId) {
                console.log("========Edit==portraitTypeId:", portraitTypeId);
                this.setState({
                    portraitTypeId: portraitTypeId,
                    operate: "编辑"
                })
                let loadTypeUrl = "/biz/member/portrait/get";
                let loadRequest = { 'portraitTypeId': portraitTypeId };
                httpPost(loadTypeUrl, loadRequest, this.loadMemberDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
            this.loadMemberTypeList();
        }
    }

    loadMemberTypeList = () => {
        let url = "/biz/member/portrait/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10000,
        };
        httpPost(url, loadRequest, this.loadMemberTypeListCallBack);
    }

    loadMemberTypeListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            let arr = [];
            data.forEach((element) => {
                arr.push(element.portraitTypeName);
            });
            // console.log("@__courseTypeNameArrays__@", JSON.stringify(arr, null, 6));
            // this.setState({
            //     courseTypeNameArrays: arr
            // })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadMemberDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                portraitTypeId: response.data.portraitTypeId,
                portraitTypeName: response.data.portraitTypeName,
                // courseTypeDesc: response.data.courseTypeDesc,
                portraitTypeSort: response.data.portraitTypeSort
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("MemberTypeMgrList")
            }}>
                <Text style={CommonStyle.headRightText}>会员行业</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    saveCourse = () => {
        // console.log("@@@@@"+JSON.stringify(route.params,null,6));
        console.log("=======saveCourse");
        let toastOpts;
        if (!this.state.portraitTypeName) {
            toastOpts = getFailToastOpts("请填写会员行业");
            WToast.show(toastOpts)
            return;
        }

        // if (this.state.courseTypeNameArrays) {
        //     console.log("1");
        //     for (let item of this.state.courseTypeNameArrays) {
        //         if (this.state.courseTypeName == item) {
        //             toastOpts = getFailToastOpts("“" + this.state.courseTypeName + "”" + "已被使用，请换个名称重试");
        //             WToast.show(toastOpts)
        //             return;
        //         }
        //     }
        // }
        console.log("===1===saveMember");
        let url = "/biz/member/portrait/add";
        if (this.state.portraitTypeId) {
            console.log("=========Edit===portraitTypeId", this.state.portraitTypeId)
            url = "/biz/member/portrait/modify";
        }

        let requestParams = {
            portraitTypeId: this.state.portraitTypeId,
            portraitTypeName: this.state.portraitTypeName,
            // courseTypeDesc: this.state.courseTypeDesc,
            portraitTypeSort: this.state.portraitTypeSort
        };
        httpPost(url, requestParams, this.saveMemberCallBack);
    }

    saveMemberCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    render() {
        return (
            <ScrollView style={[CommonStyle.contentViewStyle]}>
                <CommonHeadScreen title={this.state.operate + '会员行业'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>会员行业</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput
                        style={styles.inputRightText}
                        placeholder={'请填写会员行业'}
                        onChangeText={(text) => this.setState({ portraitTypeName: text })}
                    >
                        {this.state.portraitTypeName}
                    </TextInput>
                </View>
                {/* <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>类型说明</Text>
                        <Text style={styles.leftLabRedTextStyle}></Text>
                    </View>
                    <TextInput
                        style={styles.inputRightText}
                        placeholder={'请填写课程类型说明'}
                        onChangeText={(text) => this.setState({ courseTypeDesc: text })}
                    >
                        {this.state.courseTypeDesc}
                    </TextInput>
                </View> */}
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>
                            排序
                        </Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput
                        keyboardType='numeric'
                        style={styles.inputRightText}
                        placeholder={'请输入排序'}
                        onChangeText={(text) => this.setState({ portraitTypeSort: text })}
                    >
                        {this.state.portraitTypeSort}
                    </TextInput>
                </View>
                <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0, marginTop: 6}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginLeft: 20, width: (screenWidth - 56)/2}]} >
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveCourse.bind(this)}>
                    <View style={[CommonStyle.btnRowRightSaveBtnView, {marginRight: 20, width: (screenWidth - 56)/2}]}>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        )
    }
}

let styles = StyleSheet.create({
    contentViewStyle: {
        height: screenHeight - 90,
    },
    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 30),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },

    titleTextStyle: {
        fontSize: 16
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
})
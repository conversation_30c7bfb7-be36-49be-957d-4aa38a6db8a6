import './Global';
import { PermissionsAndroid, Platform , Alert } from "react-native";
import CameraRoll from "@react-native-community/cameraroll";


const getPremission =  async function hasAndroidPermission() {
  const permission = PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE;

  const hasPermission = await PermissionsAndroid.check(permission);
  if (hasPermission) {
    return true;
  }

  const status = await PermissionsAndroid.request(permission);
  return status === 'granted';
}

export const saveImage =  async (url) => {
  if (Platform.OS === "android" && !(await getPremission())) {
    return;
  }

  const RNFS = require('react-native-fs'); //文件处理
  const storeLocation = `${RNFS.DocumentDirectoryPath}`;
  let pathName = new Date().getTime() + Math.random(1000, 9999) + ".png";
  let downloadPath = `${storeLocation}/${pathName}`;
  const ret = RNFS.downloadFile({fromUrl: url, toFile: downloadPath});

  console.log("url +++++++" , url);
  
  ret.promise.then(res => {
    if (res && res.statusCode === 200) {
      let promise = CameraRoll.saveToCameraRoll("file://" + downloadPath);
      promise.then(function (result) {
        Alert.alert('提示','已保存到系统相册');
      }).catch(function (error) {
        Alert.alert('提示','保存失败！');
      })
    }
  })
};
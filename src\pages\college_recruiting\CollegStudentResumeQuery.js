import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,Linking,Clipboard,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import * as WeChat from 'react-native-wechat-lib';
import '../../utils/Global';

var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class CollegStudentResumeQuery extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            searchKeyWord: "",
            topBlockLayoutHeight: 0,
            collectionState:"",
            shareModal:false,
            typeDataSource:[],
            selResumeType:"all",
            resumeQueryTitle:null,
            resumeQuerySubTitle:null,
            resumeQueryLogo:null
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        let typeDataSource = [
            {
                resumeType:'all',
                typeName:"全部"
            },
            {
                resumeType:'E',
                typeName:"全职"
            },
            {
                resumeType:'P',
                typeName:"实习"
            }
        ]
        this.setState({
            typeDataSource: typeDataSource,
        })
        // 查询简历分享配置列表
        this.loadResumeConfigList();
        // 查询简历列表
        this.loadResumeList();
    }

    loadResumeConfigList = () => {
        let url = "/biz/tenant/config/list";
        let loadRequest = {
        };
        httpPost(url, loadRequest, this.loadResumeConfigListCallBack);
    }

    loadResumeConfigListCallBack=(response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            var resumeQueryTitle = data.filter(item => (item.paramCode == 'RESUME_SHARE_TITLE'));
            var resumeQuerySubTitle = data.filter(item => (item.paramCode == 'RESUME_SHARE_SUB_TITLE'));
            var resumeQueryLogo = data.filter(item => (item.paramCode == 'RESUME_SHARE_LOGO'));
            console.log("resumeQueryTitle==",resumeQueryTitle && resumeQueryTitle.length == 1 ? resumeQueryTitle[0].paramValue:null);
            console.log("resumeQuerySubTitle==",resumeQuerySubTitle && resumeQuerySubTitle.length == 1 ? resumeQuerySubTitle[0].paramValue:null);
            console.log("resumeQueryLogo==",resumeQueryLogo && resumeQueryLogo.length == 1 ? resumeQueryLogo[0].paramValue:null);
            this.setState({
                resumeQueryTitle:(resumeQueryTitle && resumeQueryTitle.length == 1) ? resumeQueryTitle[0].paramValue:null,
                resumeQuerySubTitle:(resumeQuerySubTitle && resumeQuerySubTitle.length == 1) ? resumeQuerySubTitle[0].paramValue:null,
                resumeQueryLogo:(resumeQueryLogo && resumeQueryLogo.length == 1) ? resumeQueryLogo[0].paramValue:null
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/cr/staff/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWordThree": this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "qryCollection":'Y',
            "excludesResumeDisplay":'N',
            "resumeClass":this.state.selResumeType ==="all" ? null :this.state.selResumeType
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/cr/staff/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWordThree": this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "qryCollection":'Y',
            "excludesResumeDisplay":'N',
            "resumeClass":this.state.selResumeType ==="all" ? null :this.state.selResumeType
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

     // 上拉触底加载下一页
     _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadResumeList();
    }

    loadResumeList = () => {
        let url = "/biz/cr/staff/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "searchKeyWordThree": this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "qryCollection":'Y',
            "excludesResumeDisplay":'N',
            "resumeClass":this.state.selResumeType ==="all" ? null :this.state.selResumeType
        };
        httpPost(url, loadRequest, this.loadResumeListCallBack);
    }

    loadResumeListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/cr/staff/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWordThree":this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "qryCollection":'Y',
            "excludesResumeDisplay":'N',
            "resumeClass":this.state.selResumeType ==="all" ? null :this.state.selResumeType
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    collectionResume=(item)=>{
        console.log("=========item========",item);
        let loadUrl = "/biz/collection/staff/add";
        let loadRequest = {
            "staffId":item.staffId,
            "enterpriseId":constants.loginUser.enterpriseId,
            "collectionStaff":item.collectionStaff == 'Y'? "N" : "Y",
        };
        httpPost(loadUrl, loadRequest, this.collectionResumeCallBack);
    }

    collectionResumeCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.callBackFunction();
            WToast.show({ data: response.data.collectionStaff == 'Y'?"加入收藏成功":"取消收藏成功" });

        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }

    }

    inviteInterview=(item)=>{
        console.log("=========item========",item);
        let loadUrl = "/biz/collection/staff/add";
        let loadRequest = {
            "staffId":item.staffId,
            "enterpriseId":constants.loginUser.enterpriseId,
            "inviteStaff":item.inviteStaff == 'Y'? "N" : "Y",
        };
        httpPost(loadUrl, loadRequest, this.inviteInterviewCallBack);
    }

    inviteInterviewCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({ data: response.data.inviteStaff == 'Y'?"已发出面试邀请":"已取消邀请" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }

    }

    exportPdfFile=(item)=> {
        console.log("=======exportPdfFile");
        let url= "/biz/generate/pdf/student_my_inter_view";
        let requestParams={
            "currentPage": 1,
            "pageSize": 15,
            "staffId":item.staffId,
            "userId":item.userId,
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data); 
                WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
                Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                    {
                        text:"不打开", onPress:()=>{
                        WToast.show({data:'点击了不打开'});
                        }
                    },
                    {
                        text:"打开", onPress:()=>{
                            WToast.show({data:'点击了打开'});
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }

    renderRow=(item,index)=>{
        return (
            <View key={item.dailyId} style={{ borderColor: '#F2F5FC', borderBottomWidth: 7, borderTopWidth: 8 }}>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>电子照片：{item.electronicPhotos}</Text>
                </View> */}
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>姓名：{item.staffName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>性别：{item.staffSex?(item.staffSex == "L"?"女":"男"):"信息尚未完善"}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系电话：{item.staffTel}</Text>
                </View>    */}
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>班级：{item.className}</Text>
                </View> */}
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>毕业院校：{item.graduateInstitutions}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>专业：{item.crProfessionalName ? item.crProfessionalName : item.professionalName}</Text>
                </View>
                {
                    item.comprehensivePoint ? 
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>综合绩点：{item.comprehensivePoint?(item.comprehensivePoint/100).toFixed(2):"信息尚未完善"}</Text>
                    </View>
                    : null
                }
                
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>求职类型：{item.resumeClass=="E"?"全职":(item.resumeClass=="P"?"实习":"尚未选择")}</Text>
                </View>
               
                {
                    item.personalHonor ? 
                    <View>
                        <View style={styles.titleViewStyle}>
                            <Text style={[styles.titleTextStyle,{fontWeight:'bold'}]}>个人荣誉</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>{item.personalHonor}</Text>
                        </View>
                    </View>
                    : null
                }
                {
                    item.collegeEvaluation ? 
                    <View>
                       <View style={styles.titleViewStyle}>
                            <Text style={[styles.titleTextStyle,{fontWeight:'bold'}]}>学院评价</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>{item.collegeEvaluation}</Text>
                        </View>
                    </View>
                    : null
                }
                <View style={styles.titleViewStyle}>
                {
                    item.inviteStaff == 'Y' ?
                    <Text style={{fontSize:14,color:'#FF8C28'}}>已发出面试邀请</Text>
                    :
                        null
                }
                </View>
                <View style={{width: 40, height: 40, 
                    backgroundColor: 'rgba(255,0,0,0.0)', 
                    position:'absolute', 
                    alignItems:'center',
                    justifyContent:'center',
                    right: 10,
                    top:5,
                    }}>
                    <TouchableOpacity onPress={()=>{
                        this.collectionResume(item)
                    }}>
                    {
                        item.collectionStaff == 'Y' ? 
                        <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/start_full.png')}></Image>
                        :
                        <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/start.png')}></Image>
                    }
                    </TouchableOpacity>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                    <TouchableOpacity onPress={()=>{this.inviteInterview(item)}}>
                        {
                            item.inviteStaff == 'Y' ?
                            <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle,{borderColor:'rgba(145, 147, 152, 0.5)',flexDirection:"row"}]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/closeGrey.png')}></Image> */}
                                <Text style={[{ color: 'rgba(145, 147, 152, 0.5)', fontSize: 14, lineHeight: 20 }]}>取消邀请</Text>
                            </View>
                            :
                            <View style={[{
                                width: 80,
                                height: 28,
                                flexDirection: "row",
                                justifyContent: 'center',
                                alignItems: 'center',
                                margin: 10,
                                marginRight: 0,
                                borderColor: '#FF8C28',
                                borderWidth: 0.85,
                                borderRadius: 6
                            }]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                <Text style={[{ color: '#FF8C28', fontSize: 14, lineHeight: 20 }]}>邀请面试</Text>
                            </View>    
                        }
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要导出PDF文件吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.exportPdfFile(item)
                                }
                            }
                        ]);
                    }}>
                        <View style={[{
                            width: 80,
                            height: 28,
                            flexDirection: "row",
                            justifyContent: 'center',
                            alignItems: 'center',
                            margin: 10,
                            marginRight: 0,
                            borderColor: 'rgba(30, 110, 250, 1)',
                            borderWidth: 0.85,
                            borderRadius: 6
                        }]}>
                            {/* <Image style={{ width: 24, height: 24, marginRight: 6, marginLeft: 5 }} source={require('../../assets/icon/iconfont/newMessageBlack.png')}></Image> */}
                            <Text style={[{ color: 'rgba(83, 106, 247, 1)', fontSize: 14 }]}>导出简历</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{this.props.navigation.navigate("StudentMyInterViewPreview", 
                            {
                                // 传递回调函数
                                staffId: item.staffId,
                                userPhotoUrl:constants.image_addr + '/' + item.electronicPhotos,
                                refresh: this.callBackFunction,
                                
                            })}}>
                        <View style={[{
                            width: 80,
                            height: 28,
                            flexDirection: "row",
                            justifyContent: 'center',
                            alignItems: 'center',
                            margin: 10,
                            marginRight: 10,
                            borderColor: 'rgba(27, 188, 130, 1)',
                            borderWidth: 0.85,
                            borderRadius: 6
                        }]}>
                            <Image style={{ width: 24, height: 24, marginRight: 0.5, marginLeft: 3 }} source={require('../../assets/icon/iconfont/detailGreen.png')}></Image>
                            <Text style={[{ color: 'rgba(27, 188, 130, 1)', fontSize: 14, lineHeight: 20 }]}>详情</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    typeChooseRow = (item, index) => {
        return (
            <View key={item.resumeType} >
                <TouchableOpacity onPress={() => {
                    var selResumeType = item.resumeType;
                    console.log("selResumeType",selResumeType);
                    this.setState({
                        selResumeType: selResumeType
                    })

                    let url= "/biz/cr/staff/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": 15,
                        "searchKeyWordThree":this.state.searchKeyWord,
                        "enterpriseId":constants.loginUser.enterpriseId,
                        "qryCollection":'Y',
                        "excludesResumeDisplay":'N',
                        "resumeClass":selResumeType ==="all" ? null :selResumeType,
                    };
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.resumeType} style={[item.resumeType === this.state.selResumeType ? 
                        [styles.selectedBlockItemViewStyle]
                        :
                        [styles.blockItemViewStyle]]}>
                        <Text style={[item.resumeType === this.state.selResumeType ? 
                            [{ color: "rgba(0, 10, 2, 0.8)", fontSize: 16, textAlign: 'center' }]
                            :
                            [{ color: "rgba(0, 10, 2, 0.45)", fontSize: 14, textAlign: 'center' }],
                        { fontWeight: 'bold' }
                        ]}>
                            {item.typeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    shareModal:true
                })
            }}>
                <Image style={{ width:25, height:25 }} source={require('../../assets/icon/iconfont/shareBlack.png')}></Image>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='简历查询'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle, { marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={[{marginLeft:0,marginTop: 0,paddingBottom:0 }]} onLayout={this.topBlockLayout.bind(this)}>
                        <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                            {
                                (this.state.typeDataSource && this.state.typeDataSource.length > 0)
                                    ?
                                    this.state.typeDataSource.map((item, index) => {
                                        return this.typeChooseRow(item)
                                    })
                                    : 
                                <View />
                            }
                        </View>
                    </View>
                    <View style={{}}>
                        <View style={[styles.inputRowStyle,{}]}>
                            <View style={styles.leftLabView}>
                                <Image  style={{width:18, height:18}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                    returnKeyLabel="搜索"
                                    onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                    }}
                                placeholder={'学校/专业/班级/姓名'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.shareModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() =>console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut,{backgroundColor: 'rgba(0,0,0,0.5)'}]}>
                        <View style={{width:screenWidth,height:250,bottom:0,position:'absolute',backgroundColor:'#f0f0f0'}}>
                            <View style={{height:55,justifyContent:'center',alignItems:'center'}}>
                                <Text style={{fontSize:18}}>
                                    选择分享方式
                                </Text>
                            </View>
                            <View style={{height:105,flexDirection:'row',justifyContent:'center',index:1000}}>
                                <TouchableOpacity 
                                    onPress={() => {
                                        console.log("标题=====",this.state.resumeQueryTitle?this.state.resumeQueryTitle:'江苏省数字经济联合会-人才库')
                                        // 分享微信好友
                                        WeChat.shareWebpage({
                                            title: this.state.resumeQueryTitle ? this.state.resumeQueryTitle : '江苏省数字经济联合会-人才库',
                                            description: this.state.resumeQuerySubTitle ? this.state.resumeQuerySubTitle : '江苏省数字经济联合会-人才库',
                                            thumbImageUrl: this.state.resumeQueryLogo ? (constants.image_addr + '/' +this.state.resumeQueryLogo) : 'https://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/logo/jiangsu_digital_economy_ederation_logo.jpg',
                                            webpageUrl: 'https://jzxs.njjzgk.com/html/resumeQuery/list.html?tenantId=' + constants.loginUser.tenantId,
                                            scene: 0
                                        })
                                        .then((respJSON) => {
                                            WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                        })
                                        .catch((error) => {
                                            WToast.show({ data: error });
                                            Alert.alert(error.message);
                                        });
                                    }}>
                                    <View style={[{width:100,flexDirection:"column",margin:5,justifyContent:'center',alignItems:'center'}]}>
                                        <Image  style={{width:40, height:40,marginRight:2}} source={require('../../assets/icon/iconfont/WeChat.png')}></Image>
                                        <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{color:'#000000'}]}>微信好友</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{height:50,justifyContent:'center',alignItems:'center',borderTopWidth:1,borderTopColor:'#cccccc'}}>
                                <TouchableOpacity onPress={()=>{
                                    this.setState({
                                        shareModal:false
                                    })
                                }}>
                                    <View style={{width:screenWidth,justifyContent:'center',alignItems:'center'}}>
                                        <Text style={[{fontSize:18}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            

                        </View>
                    </View>
                </Modal>

            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        // justifyContent: "space-between",
        alignItems: 'center',
        width: screenWidth / 1.05,
        paddingLeft: 5,
        height: 34,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#F2F5FC",
        backgroundColor: '#F2F5FC',
        borderRadius: 15,
        margin: 0,
        marginTop: 0,
        marginLeft: 0,
    },
    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 14,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle: {
        // marginTop: 10,
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    // 分段器样式
    blockItemViewStyle: {
        margin: 5,
        width: 45, 
        borderRadius: 0,
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 1, paddingRight: 1, 
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
        // marginTop: 0, 
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        width: 45, borderRadius: 0, 
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 1, paddingRight: 1, 
        justifyContent: 'center',
        backgroundColor: "#FFFFFF", 
        // marginTop: 0, 
    },
});
import React,{Component} from 'react';
import {
    View, ScrollView, Text, TextInput, StyleSheet,KeyboardAvoidingView,FlatList,TouchableOpacity,Dimensions,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../utils/CameraRollUtils';
export default class MaterialAudit extends Component {
    constructor(props) {
        super(props);
        this.state = {
            paymentApplyName:"",
            customerName:"",
            paymentClassName:"",
            paymentDate:"",
            paymentAmount:"",
            paymentModeName:"",
            paymentReason:"",
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            auditPatchAttach:"",
            auditPatchAttachUrl:"",
            auditPatchAttachCopy:"",
            fillDataList:[],
            selReplyId:1,
            replyDataSource:[
                {
                    replyId:1,
                    replyType:"同意",
                    replyName:"Y"
                },
                {
                    replyId:2,
                    replyType:"驳回",
                    replyName:"N"
                }
            ],
            selReplyName:"Y",
            recordId:"",
            nodeId:"",
            auditUserId:"",
            auditOpinion:"",
            applyAuditId:"",
            auditTypeCode:"",
            urls:[],
            isShowImage: false,
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId,recordId,applyAuditId } = route.params;
            if (recordId) {
                let url= "/biz/audit/node/record/get";
                let loadRequest={
                    "recordId": recordId,
                };
                httpPost(url, loadRequest, this.loadAuditRecordCallBack);
            }

            // if (applyAuditId) {
            //     httpPost("/biz/payment/apply/audit/get", { "applyAuditId": applyAuditId }, (response) => {
            //         if (response.code === 200 && response.data) {
            //             var urls = [];
            //             let auditPatchAttach = response.data.auditPatchAttach;
            //             console.log("applyAuditId +++++++",applyAuditId);
            //             console.log("auditPatchAttach +++++++",auditPatchAttach)
            //             var url = {
            //                 url:constants.image_addr + '/' + auditPatchAttach
            //             }
            //             urls=urls.concat(url);
            //             this.setState({
            //                 auditPatchAttach : response.data.auditPatchAttach,
            //                 auditPatchAttachUrl: constants.image_addr + '/' + auditPatchAttach,
            //                 urls:urls,
            //             })
            //             console.log(url);
            //         }
            //     });
            // }
            
        }
    }

    loadAuditRecordCallBack=(response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                paymentApplyName:response.data.paymentApplyAuditList[0].paymentApplyName,
                customerName:response.data.paymentApplyAuditList[0].customerName,
                paymentClassName:response.data.paymentApplyAuditList[0].paymentClassName,
                paymentDate:response.data.paymentApplyAuditList[0].paymentDate,
                paymentAmount:response.data.paymentApplyAuditList[0].paymentAmount,
                paymentModeName:response.data.paymentApplyAuditList[0].paymentModeName,
                paymentReason:response.data.paymentApplyAuditList[0].paymentReason,
                fillDataList:response.data.fillDataList,
                recordId:response.data.recordId,
                nodeId:response.data.nodeId,
                auditUserId:response.data.auditUserId,
                applyAuditId:response.data.paymentApplyAuditList[0].applyAuditId,
                auditTypeCode:response.data.auditTypeCode,
                auditPatchAttach:response.data.paymentApplyAuditList[0].auditPatchAttach,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    saveAudit = () => {
        console.log("=======saveAuditConfig");
        let toastOpts;
        if(this.state.selReplyName == 'Y') {
            if (this.state.fillDataList.includes("AUDIT_PATCH_ATTACH")) {
                if (!this.state.auditPatchAttach) {
                    if (!this.state.auditPatchAttachCopy) {
                        toastOpts = getFailToastOpts("请上传凭证截图");
                        WToast.show(toastOpts)
                        return;
                    }
                }
            }
        }

        let url = "/biz/audit/node/record/paymentAuditAdd";
        let requestParams = {
            "auditPatchAttach":this.state.auditPatchAttach,
            
            "applyAuditId":this.state.applyAuditId,

            "recordId":this.state.recordId,
            "nodeId":this.state.nodeId,
            "auditUserId":this.state.auditUserId,
            "auditTypeCode":this.state.auditTypeCode,


            "auditResult":this.state.selReplyName,
            "auditOpinion":this.state.auditOpinion,
        };
        httpPost(url, requestParams, this.saveAuditCallBack);
    }

    // 保存回调函数
    saveAuditCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    //sex列表展示
    renderReplyRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selReplyId:item.replyId,
                        selReplyName:item.replyName,
                    })
                }}>
                <View key={item.replyId} style={[item.replyId===this.state.selReplyId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.replyId===this.state.selReplyId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.replyType}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen  title='审核'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>付款名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入付款名称'}
                            onChangeText={(text) => this.setState({paymentApplyName:text})}
                        >
                            {this.state.paymentApplyName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>支付对象</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入支付对象'}
                            onChangeText={(text) => this.setState({customerName:text})}
                        >
                            {this.state.customerName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>支付类别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入支付类别'}
                            onChangeText={(text) => this.setState({paymentClassName:text})}
                        >
                            {this.state.paymentClassName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>支付日期</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入支付日期'}
                            onChangeText={(text) => this.setState({paymentDate:text})}
                        >
                            {this.state.paymentDate}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>付款金额</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入付款金额'}
                            onChangeText={(text) => this.setState({paymentAmount:text})}
                        >
                            {this.state.paymentAmount}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>付款事由</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        {/* <TextInput
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'无'}
                            onChangeText={(text) => this.setState({paymentReason:text})}
                        >
                            {this.state.paymentReason}
                        </TextInput> */}
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <TextInput 
                            editable={false} 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                            placeholder={'请输入付款事由'}
                            onChangeText={(text) => this.setState({paymentReason:text})}
                        >
                            {this.state.paymentReason}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>支付方式</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入支付方式'}
                            onChangeText={(text) => this.setState({paymentModeName:text})}
                        >
                            {this.state.paymentModeName}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={[styles.rowLabView,{marginRight:30}]}>
                            <Text style={styles.leftLabNameTextStyle}>审核批复</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row',marginLeft:0}}>
                            {
                                (this.state.replyDataSource && this.state.replyDataSource.length > 0) 
                                ? 
                                this.state.replyDataSource.map((item, index)=>{
                                    return this.renderReplyRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>
                    
                    {
                        this.state.auditPatchAttach?
                        <View>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>审核附件</Text>
                            </View>
                            <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                            <View>   
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        isShowImage:true,
                                    })
                                }}>
                                <Image source={{ uri: (constants.image_addr + '/' + this.state.auditPatchAttach) }} style={{width:120,height:150,justifyContent:'center',alignItems:'center'}} />                                                    
                                </TouchableOpacity>
                                <Modal visible={this.state.isShowImage} transparent={true}>
                                    <ImageViewer enableSwipeDown={false} menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} 
                                    onSave={()=>{
                                        saveImage( (constants.image_addr + '/' + this.state.auditPatchAttach) )
                                    }}
                                    onClick={() => { // 图片单击事件
                                        this.setState({
                                            isShowImage:false
                                        })
                                    }}
                                    imageUrls={[{url:(constants.image_addr + '/' + this.state.auditPatchAttach)}]} />
                                </Modal>
                            </View>
                            </View>
                        </View>
                        :
                        (
                            this.state.fillDataList.includes("AUDIT_PATCH_ATTACH")?
                            <View>
                                <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>审核附件</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                <TouchableOpacity 
                                onPress={() => {
                                    uploadImageLibrary(this.state.auditPatchAttach, "attachment_image", (imageUploadResponse) => {
                                        console.log("========imageUploadResponse", imageUploadResponse)
                                        if (imageUploadResponse.code === 200) {
                                            WToast.show({ data: "成功上传" });
                                            let { compressFile } = imageUploadResponse.data
                                            this.setState({
                                                auditPatchAttachUrl: constants.image_addr + '/' + compressFile,
                                                auditPatchAttach:compressFile,
                                            })
                                            // httpPost("/biz/material/inventory/modify_inventory_photo", {
                                            //     "inventoryId":this.state.inventoryId, 
                                            //     "materialImage":compressFile
                                            // }, (updateResponse)=>{
                                            //     if (updateResponse.code === 200) {
                                            //         console.log("======附件信息已经更新")
                                            //     }
                                            // })
                                        }
                                        else {
                                            WToast.show({ data: imageUploadResponse.message });
                                        }
                                    });

                            }}>
                                    {
                                        this.state.auditPatchAttach ?
                                        <Image source={{ uri: this.state.auditPatchAttachUrl }} style={{width:120,height:150,justifyContent:'center',alignItems:'center'}} />
                                        :
                                        <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                    }
                            </TouchableOpacity>
                            </View>
                            </View>
                            :
                            <View/>
                        )
                    }
                    

                   

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>审核意见</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:150}]}
                            placeholder={'请输入审核意见'}
                            onChangeText={(text) => this.setState({auditOpinion:text})}
                        >
                            {this.state.auditOpinion}
                        </TextInput>
                    </View>



                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveAudit.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:18
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputTextStyleTextStyle:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        height:45,
        justifyContent:'center'
    }
});
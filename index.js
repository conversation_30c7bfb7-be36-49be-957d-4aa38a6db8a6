/**
 * @format
 */

import {AppRegistry} from 'react-native';
// import App from './App';
// import App001 from './App001';
// import ShareWechat from './src/test/ShareWechat';
import AppNavigation5 from './AppNavigation5';
// import LoginView from './src/pages/LoginView';
// import MainPage from './src/pages/MainPage';
// import Welcome from './src/pages/Welcome';
// import PwdInputText from './PwdInputText';
// import BottomScrollSelectView from './BottomScrollSelectView';

import {name as appName} from './app.json';

import './src/utils/Global';
import './src/config/ProjectConfig';
import * as WeChat from 'react-native-wechat-lib';
WeChat.registerApp(constants.wechatShareAppId, constants.wechatUniversalLink);

AppRegistry.registerComponent(appName, () => AppNavigation5);

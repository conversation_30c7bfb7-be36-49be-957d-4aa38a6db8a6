import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import * as WeChat from 'react-native-wechat-lib';

var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class PortalEnterpriseList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            searchKeyWord: "",
            topBlockLayoutHeight: 0,
            enterpriseShareTitle:null,
            enterpriseShareSubTitle:null,
            enterpriseShareLogo:null,
            shareModal:false
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        this.loadEnterpriseList();

        //查询租户配置信息
        this.loadTenantConfigList();
    }

    loadTenantConfigList = () => {
        let url = "/biz/tenant/config/list";
        let loadRequest = {
        };
        httpPost(url, loadRequest, this.loadTenantConfigListCallBack);
    }

    loadTenantConfigListCallBack=(response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            var enterpriseShareTitle = data.filter(item => (item.paramCode == 'ENTERPRISE_SHARE_TITLE'));
            var enterpriseShareSubTitle = data.filter(item => (item.paramCode == 'ENTERPRISE_SHARE_SUB_TITLE'));
            var enterpriseShareLogo = data.filter(item => (item.paramCode == 'ENTERPRISE_SHARE_LOGO'));
            this.setState({
                enterpriseShareTitle:(enterpriseShareTitle && enterpriseShareTitle.length == 1) ? enterpriseShareTitle[0].paramValue:null,
                enterpriseShareSubTitle:(enterpriseShareSubTitle && enterpriseShareSubTitle.length == 1) ? enterpriseShareSubTitle[0].paramValue:null,
                enterpriseShareLogo:(enterpriseShareLogo && enterpriseShareLogo.length == 1) ? enterpriseShareLogo[0].paramValue:null
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/enterprise/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchEnterprise": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/enterprise/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchEnterprise": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadEnterpriseList();
    }

    loadEnterpriseList=()=>{
        let url= "/biz/enterprise/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "searchEnterprise": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this.loadEnterpriseListCallBack);
    }


    loadEnterpriseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/enterprise/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchEnterprise":this.state.searchKeyWord,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    deleteEnterprise =(enterpriseId)=> {
        console.log("=======delete=enterpriseId", enterpriseId);
        let url= "/biz/enterprise/delete";
        let requestParams={'enterpriseId':enterpriseId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.kilnCarId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>企业名称：{item.enterpriseName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>企业角色：{item.roleName}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={[styles.titleTextStyle,{fontWeight:'bold'}]}>企业简介</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.enterpriseIntroduction?item.enterpriseIntroduction:"无"}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>企业LOGO：{item.enterpriseLogo}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>地区：{item.enterpriseArea}</Text>
                </View> */}
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>排序：{item.enterpriseSort}</Text>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    <TouchableOpacity onPress={()=>{
                        console.log('===loadUserPWD:', item.userId);
                        let loadTypeUrl= "/biz/enterprise/send_enterprise_pwd";
                        let loadRequest={tenantId: constants.loginUser.tenantId, enterpriseId: item.enterpriseId};;
                        httpPost(loadTypeUrl, loadRequest, (response)=>{
                            if (response.code == 200 && response.data) {
                                WToast.show({data:"发送成功"});
                            }
                            else {
                                WToast.show({data:response.message});
                            }   
                        });
                    }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { height:28,width:95,backgroundColor:"#5DD421",flexDirection:"row"}]}>
                            <Image style={{width:16, height:16,marginRight:5}} source={require('../../assets/icon/iconfont/sendPwd.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>发送密码</Text>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity onPress={() => {
                            this.props.navigation.navigate("EnterpriseRecruiterList", {
                                "enterpriseId": item.enterpriseId,
                                "roleId": item.roleId
                            })
                        }}>
                            <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle
                                , { height:28,width: 95,backgroundColor:"#1BBC82",flexDirection:"row"}]}>
                                <Image  style={{width:16, height:16,marginRight:5}} source={require('../../assets/icon/iconfont/assistant.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>招聘专员</Text>
                            </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该企业吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteEnterprise(item.enterpriseId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle]}>
                            <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("PortalEnterpriseAdd", 
                            {
                                // 传递参数
                                enterpriseId:item.enterpriseId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle,{marginRight:10}]}>
                            <Image style={{ width: 17, height: 17, marginRight: 7 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20}}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PortalEnterpriseAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='企业管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{
                    padding: 0,
                    margin: 0,
                    //主轴均匀空白填充
                    justifyContent: "space-evenly",
                    //主轴横向
                    flexDirection: 'row',
                    //交叉轴居中
                    alignItems: 'center',
                    index: 1000,
                    // flexWrap: 'wrap',
                    borderWidth: 0,
                    borderRadius: 1,
                    height: 48,
                    backgroundColor: '#FFFFFF',
                }} onLayout={this.topBlockLayout.bind(this)}>
                        <View style={[styles.inputRowStyle]}>
                            <View style={styles.leftLabView}>
                                <Image style={{ width: 18, height: 18, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                this.searchByKeyWord();
                                }}
                                placeholder={'搜索企业名称'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                        <TouchableOpacity onPress={()=>{
                            this.setState({
                                shareModal:true
                            })
                        }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle,
                            {
                                margin: 0,
                                alignItems: 'center',
                                width: 64,
                                backgroundColor: '#1E6EFA',
                                height: 32,
                                borderRadius: 20,
                                justifyContent:'center'
                            }]}>
                                {/* <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/shareWhite.png')}></Image> */}
                                <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { fontSize: 14 }]}>分享</Text>
                            </View>

                        </TouchableOpacity>
                        {/* <View style={{ height: 5, backgroundColor: '#FFFFFF' }}></View> */}
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.shareModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() =>console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut,{backgroundColor: 'rgba(0,0,0,0.5)'}]}>
                        <View style={{width:screenWidth,height:250,bottom:0,position:'absolute',backgroundColor:'#f0f0f0'}}>
                            <View style={{height:55,justifyContent:'center',alignItems:'center'}}>
                                <Text style={{fontSize:18}}>
                                    选择分享方式
                                </Text>
                            </View>
                            <View style={{height:105,flexDirection:'row',justifyContent:'center',index:1000}}>
                                <TouchableOpacity 
                                    onPress={() => {
                                        // 分享微信好友
                                        WeChat.shareWebpage({
                                            title: this.state.enterpriseShareTitle ? this.state.enterpriseShareTitle : '江苏省数字经济联合会-企业库',
                                            description: this.state.enterpriseShareSubTitle ? this.state.enterpriseShareSubTitle : '江苏省数字经济联合会-企业库',
                                            thumbImageUrl: this.state.enterpriseShareLogo ? (constants.image_addr + '/' +this.state.enterpriseShareLogo) : 'https://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/logo/jiangsu_digital_economy_ederation_logo.jpg',
                                            webpageUrl: 'https://jzxs.njjzgk.com/html/enterpriseQuery/list.html?tenantId=' + constants.loginUser.tenantId,
                                            scene: 0
                                        })
                                        .then((respJSON) => {
                                            WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                        })
                                        .catch((error) => {
                                            WToast.show({ data: error });
                                            Alert.alert(error.message);
                                        });
                                    }}>
                                    <View style={[{width:100,flexDirection:"column",margin:5,justifyContent:'center',alignItems:'center'}]}>
                                        <Image  style={{width:40, height:40,marginRight:2}} source={require('../../assets/icon/iconfont/WeChat.png')}></Image>
                                        <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{color:'#000000'}]}>微信好友</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{height:50,justifyContent:'center',alignItems:'center',borderTopWidth:1,borderTopColor:'#cccccc'}}>
                                <TouchableOpacity onPress={()=>{
                                    this.setState({
                                        shareModal:false
                                    })
                                }}>
                                    <View style={{width:screenWidth,justifyContent:'center',alignItems:'center'}}>
                                        <Text style={[{fontSize:18}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            

                        </View>
                    </View>
                </Modal>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        borderColor: "#F2F5FC", 
        borderBottomWidth: 8, 
        borderTopWidth: 8 
    },
    inputRowStyle: {
        // justifyContent: "space-between",
        alignItems: 'center',
        width: screenWidth / 1.4,
        paddingLeft: 5,
        height: 34,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#F2F5FC",
        backgroundColor: '#F2F5FC',
        borderRadius: 15,
        margin: 0,
        marginTop: 0,
        marginLeft: 0,
    },
    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 14,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },

});
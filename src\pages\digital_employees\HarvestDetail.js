import React,{Component} from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,TextInput,
    FlatList, RefreshControl, Linking, Clipboard, Image, Modal,ScrollView,KeyboardAvoidingView,ImageBackground,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
// import EmptyListComponent from '../../component/EmptyRowViewComponent';
// import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

import moment from 'moment';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
var currentDate = (moment(new Date()).format('YYYY-MM-DD'));

export default class HarvestDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,

            showSearchItemBlock: false,
            saveMessage: "",

            harvestId: "",
            userId: "",
            userName: "",
            auditOperator:'',
            auditScore: null,
            auditOpinion:'',
            auditTime:'',
            harvestDate:'',
            harvestState:'',
            gmtCreated:'',
            // finishedWork:'',
            // unfinishedWork:'',
            // requiresCoordinationWork:'',
            // workPlan:'',
            departmentId:'',
            departmentName:'',
            userPhoto: "",
            userPhotoUrl: "",
            auditUserPhoto: "",
            operatorPhotoUrl:'',
            messageBoardList:[],
            messageId:null,
            auditModal:false,
            editModal:false,
            messageModal: false,
            backlogState: false,
            doneState: false,
            deleteModal:false,
            messageFkId: "",
            parentMessageId:"",
            messageContent: "",
           
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { harvestId, userName } = route.params;
            if (harvestId) {
                console.log('harvestId================', harvestId)
                this.setState({
                    harvestId: harvestId,
                    userName: userName
                })
                loadTypeUrl = "/biz/harvest/get";
                loadRequest = { 'harvestId': harvestId };
                httpPost(loadTypeUrl, loadRequest, this.loadHarvestDataCallBack);
            }
           
            // this.loadHarvestAuditTask(harvestId);//加载审核

            this.loadMessageBoardList(harvestId);
        }
    }

    loadHarvestDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            console.log('response.data=========================', response.data)
            console.log('++++++++++++++' , constants.loginUser.userId);
            console.log('++',response.data.userId);
            this.setState({
                userId: response.data.userId,
                // userName: response.data.userName, 
                // auditOperator: response.data.auditOperator,
                scoreTime: response.data.scoreTime,
                score: response.data.score,
                harvestId: response.data.harvestId,
                harvestTitle: response.data.harvestTitle,
                harvestState : response.data.harvestState,
                gmtCreated: response.data.gmtCreated,
                gmtModified: response.data.gmtModified,
                harvestContent: response.data.harvestContent,
                visible: response.data.visible,
                // requiresCoordinationWork: response.data.requiresCoordinationWork,
                // messageNum: response.data.messageNum,
                departmentId: response.data.departmentId,
                departmentName: response.data.departmentName,
                userPhoto: response.data.userPhoto,
            })
        }
    }
    // // 审核
    // loadHarvestAuditTask = (harvestId) =>{
    //     let url = "/biz/harvest/audit/task/list";
    //     let loadRequest = {
    //         "harvestId": harvestId
    //     }
    //     httpPost(url, loadRequest, this.loadHarvestAuditTaskCallBack);
    // }

    // // 审核
    // loadHarvestAuditTaskCallBack = (response) => {
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         var auditTaskData = response.data.dataList;
    //         this.setState({
    //             auditUserPhoto: auditTaskData[0].auditUserPhoto,
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    loadMessageBoardList = (harvestId) =>{
        let url = "/biz/portal/message/board/allList";
        let loadRequest = {
            "messageFkType":"C",
            "messageFkId":harvestId,
        }
        httpPost(url, loadRequest, this.loadMessageBoardListCallBack);
    }

    loadMessageBoardListCallBack = (response) => {
        // console.log("1"+response.data);
        if (response.code == 200 && response.data && response.data) {
            console.log("2");
            console.log("messageBoardList=========",response.data);
            this.setState({
                messageBoardList:response.data,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }

    //头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    editModal: true,
                })
            }}>
                <Image style={{ width: 28, height: 28}} source={require('../../assets/icon/iconfont/more.png')}></Image>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    // 保存成果留言
    saveHarvestMessage =()=> {
        console.log("=======saveHarvestMessage");
        let toastOpts;
        if (!this.state.messageContent) {
            toastOpts = getFailToastOpts("请输入留言内容");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/portal/message/board/add";
        let requestParams={
            messageContent:this.state.messageContent,
            messageFkId: this.state.harvestId,
            parentMessageId: this.state.parentMessageId,
            messageFkType:"C"
        };
        httpPost(url, requestParams, this.saveHarvestMessageCallBack);
    }

    // 保存成果留言的回调函数
    saveHarvestMessageCallBack=(response)=>{
        this.setState({
            messageContent: "",
            parentMessageId:""
        })
        let toastOpts;
        switch (response.code) {
            case 200:
                WToast.show({ data: "留言发送成功" });
                this.loadMessageBoardList(this.state.harvestId);
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
// 重写
    auditHarvest =()=> {
        console.log("=======auditHarvest");
        let toastOpts;
        if (!this.state.score) {
            toastOpts = getFailToastOpts("请输入审核得分");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/daily/audit";
        let requestParams={
            dailyId: this.state.dailyId,
            auditScore: this.state.auditScore,
            auditOpinion: this.state.auditOpinion,
        };
        httpPost(url, requestParams, this.saveHarvestCallBack);
    }

    // 删除
    deleteHarvest = (harvestId) => {
        console.log("=======delete=harvestId", harvestId);
        let url = "/biz/harvest/delete";
        let requestParams = { 'harvestId': harvestId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
            let toastOpts;
            switch (response.code) {
                case 200:
                    if (this.props.route.params.refresh) {
                        this.props.route.params.refresh();
                    }
                    WToast.show({ data: "删除完成" });
                    this.props.navigation.goBack()
                    break;
                case 401:
                    if (this.props.route.params.refresh) {
                        this.props.route.params.refresh();
                    }
                    WToast.show({ data: response.message });
                    this.props.navigation.navigate("LoginView");
                default:
                    WToast.show({ data: response.message });
            }
        }

 

    // 保存回调函数
    saveHarvestCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('审核完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return (
            <View style={{height:screenHeight}}>
                <CommonHeadScreen title={this.state.userName + "的成果"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <ScrollView style={[CommonStyle.formContentViewStyle]} >
                    {/* 成果顶部信息 */}
                <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
                    {
                        this.state.userPhoto ?
                            <Image source={{ uri: this.state.userPhotoUrl }} style={{ height: 48, width: 48, borderRadius: 50}} />
                            :
                            <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                    {
                                        this.state.userName.length <= 2 ? 
                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {this.state.userName}
                                        </Text>
                                        :
                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {this.state.userName.slice(-2)}
                                        </Text>
                                    }
                                </View>
                            </ImageBackground>
                    }
                        
                        <View style={{marginLeft:11, flexDirection: 'column'}}>
                            <View style={{flexDirection: 'row', marginTop: 4 }}>
                                <View style={{ flexDirection: 'row' }}>
                                    <Text style={{ fontSize: 16 }}>{this.state.userName}的成果</Text>
                                </View>
                                {/* 判断是否优秀 */}
                                {
                                    (this.state.harvestState === "0AA" && this.state.visible === 'Y' && this.state.score ===1) ?
                                        <View style={{ width: 58, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#E63633' }}>
                                            <Image style={{ width: 16, height: 18, marginRight: 2, marginRight: 3 }} source={require('../../assets/icon/good.png')}></Image>
                                            <Text style={{fontSize: 13, color: '#FFFFFF' }}>优秀</Text>
                                        </View>
                                        :
                                        <View></View>
                                }
                            </View>
                            <View style={{flexDirection: 'row'}}>
                            <Image style={{ height: 13 , width: 12, marginTop: 5, marginLeft: 1, marginRight: 5}} source={require('../../assets/icon/iconfont/clock.png')}></Image> 
                                <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                                    <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)' }]}>{this.state.gmtCreated.slice(0,10)} 提交</Text>
                                </View>
                            </View>
                        </View>
                    </View>
                    {/* 分隔线 */}
                    <View style={styles.lineViewStyle}/>
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>标题</Text>
                    </View>
                    <View style={styles.itemContentTextStyle}>
                        <Text style={styles.itemContentStyle}>{this.state.harvestTitle}</Text>
                    </View>
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>内容</Text>
                    </View>
                    <View style={[styles.itemContentTextStyle, { marginBottom: 5 }]}>
                        <Text style={styles.itemContentStyle}>{this.state.harvestContent}</Text>
                    </View>
                    <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                        <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    messageModal: true
                                })
                            }}>
                                <View style={[{width: 78, height: 28, flexDirection: "row", alignItems: 'center', margin: 10, 
                                    marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                                }]}>
                                    <Image style={{ width: 20, height: 20, marginRight: 8, marginLeft: 12 }} source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                                    <Text style={[{ color: 'rgba(0, 10, 32, 0.65)', fontSize: 14, lineHeight: 24 }]}>留言</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    {/* 分隔线 */}
                    <View style={styles.lineViewStyle}/>
                    {/* 留言 */}
                    {
                        (this.state.messageBoardList && this.state.messageBoardList.length > 0) ?
                            <View style={{backgroundColor:'rgba(242, 245, 252, 0.5)', borderRadius:10,width:screenWidth-24, marginLeft: 12, marginRight: 12, paddingTop: 5, marginBottom: 5}}>
                                {
                                    this.state.messageBoardList.map((item, index)=>{
                                        return(
                                            <View key={item.messageId} style={{ flexDirection: 'row', marginLeft: 10, marginTop: 10, marginRight: 6, marginBottom: 10}}>
                                                {/* {
                                                    item.operatorPhoto ?
                                                        <Image source={{ uri: (constants.image_addr + '/' + item.operatorPhoto) }} style={{ height: 36, width: 36, borderRadius: 50}} />
                                                        :
                                                        <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ height: 36, width: 36}}>
                                                            <View style={{height: 36, width: 36,justifyContent: "center",alignItems: "center"}}>
                                                                {
                                                                    item.operatorName <= 2 ? 
                                                                    <Text style={{color:'#FFFFFF',fontSize:13,fontFamily:'PingFangSC-Regular',fontWeight:"normal",textAlign:'center', lineHeight:20}}>
                                                                        {item.operatorName}
                                                                    </Text>
                                                                    :
                                                                    <Text style={{color:'#FFFFFF',fontSize:13,fontFamily:'PingFangSC-Regular',fontWeight:"normal",textAlign:'center', lineHeight:20,}}>
                                                                        {item.operatorName.slice(-2)}
                                                                    </Text>
                                                                }
                                                            </View>
                                                        </ImageBackground>
                                                } */}
                                                {
                                                    item.operatorName <= 2 ? 
                                                    <Text style={{color:'rgba(255,255,255,1)',fontSize:17,fontFamily:'PingFangSC-Regular',fontWeight:"normal",textAlign:'center', lineHeight:20}}>
                                                        {item.operatorName}
                                                    </Text>
                                                    :
                                                    <Text style={{color:'rgba(255,255,255,1)',fontSize:17,fontFamily:'PingFangSC-Regular',fontWeight:"normal",textAlign:'center', lineHeight:20,}}>
                                                        {item.operatorName.slice(-2)}
                                                    </Text>
                                                }
                                                <View style={{ flexDirection: 'column', marginLeft: 10, flex: 1}}>
                                                    <View style={{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'center', paddingTop: 4 }}>
                                                        <View style={{ flexDirection: 'row'}}>
                                                            <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16 }}>{item.operatorName}</Text>
                                                        </View>
                                                        <View style={{ flexDirection: 'row', marginLeft: 6}}>
                                                            <Text style={[{ fontSize: 12, color: 'rgba(0,10,32,0.45)' }]}>{item.gmtCreated.slice(0,16)}</Text>
                                                        </View>
                                                        
                                                    </View>

                                                    {
                                                        item.parentMessageId ?
                                                            <View style={[{flexDirection: 'column', justifyContent: 'flex-start'}]}>
                                                                <View style={[{flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'flex-start', marginLeft: 9, marginTop: 11}]}>
                                                                    <Text style={[styles.itemContentStyle, {color:'rgba(0,10,32,0.45)'}]}>{"回复 "+ item.parentUserName + ": "+ item.parentMessageContent}</Text>
                                                                </View>
                                                                <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 8}]}>
                                                                    <Text style={styles.itemContentStyle}>{item.messageContent ? item.messageContent : "无"}</Text>
                                                                </View>
                                                            </View>
                                                            :
                                                            <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 10}]}>
                                                                <Text style={styles.itemContentStyle}>{item.messageContent ? item.messageContent : "无"}</Text>
                                                            </View>
                                                    }
                                                </View>

                                                <View style={{ position:'absolute', right: 0, top: 0}}>
                                                    <TouchableOpacity onPress={() => {
                                                        this.setState({
                                                            parentMessageId:item.messageId,
                                                            messageModal: true,
                                                        })
                                                    }}>
                                                        <View style={[{width: 35, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                                                            <Image style={{ width: 24, height: 24 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                        )                           
                                    })
                                }
                            </View>
                            :
                            <View/>
                    }
              
                </ScrollView>
                {/* 分隔线 */}
                <View style={[styles.lineViewStyle, {paddingBottom: 60, backgroundColor: '#FFFFFF'}]}/>
                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.editModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.08)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        editModal: false,
                                    })
                                    this.props.navigation.navigate("QueryHarvest",
                                        {
                                            // 传递参数
                                            departmentId: this.state.departmentId,
                                            departmentName: this.state.departmentName,
                                            userId: this.state.userId,
                                            userName: this.state.userName,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}]}>
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>查看更多日报</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {
                                constants.loginUser.userId == this.state.userId ?
                                <View>
                                    <View>
                                        <TouchableOpacity onPress={() => {
                                            if (this.state.harvestState != "0BB" || dateDiffHours(this.state.currentTime, this.state.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                                WToast.show({ data: '该成果不可编辑' });
                                                return;
                                            }
                                            this.setState({
                                                editModal: false,
                                            })
                                            this.props.navigation.navigate("HarvestMgrAdd",
                                                {
                                                    // 传递参数
                                                    harvestId: this.state.harvestId,
                                                    // 传递回调函数
                                                    refresh: this.callBackFunction
                                                })
                                        }}>
                                            <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                                , (this.state.harvestState != "0BB" || dateDiffHours(this.state.currentTime, this.state.dailyDate) > constants.loginUser.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""]}>
                                                {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                                                <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View>
                                        <TouchableOpacity onPress={() => {
                                            if (this.state.harvestState != "0BB" && this.state.score != null ) {
                                                WToast.show({ data: '成果已评优不可删除' });
                                                return;
                                            }
                                            if (this.state.harvestState != "0BB" && dateDiffHours(this.state.currentTime, this.state.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                                WToast.show({ data: '成果已超出删除时限' });
                                                return;
                                            }
                                            // 删除弹窗Modal
                                            this.setState({
                                                editModal: false,
                                                deleteModal: true
                                            })
                                            
                                        }}>
                                            <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                                , (this.state.auditScore || (this.state.harvestState != "0BB" && dateDiffHours(this.state.currentTime, this.state.dailyDate)) > constants.loginUser.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""]}>
                                                {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                                                <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                                :
                                <View></View>
                            }
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        editModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 留言输入框弹窗 */}
                <Modal
                    animationType='slide'
                    transparent={true}
                    visible={this.state.messageModal}
                >
                    <TouchableOpacity style={{flex: 1, position: 'relative'}}
                        onPress={() => {
                            this.setState({
                                messageModal: false,
                                messageContent: "",
                                parentMessageId:""
                            })
                    }}>
                        <View style={{backgroundColor: '#FFFFFF', flexDirection: 'row', alignItems: 'center',
                            position: 'absolute', width: '100%', left: 0, bottom: 0, padding: 5
                        }}>
                            <TextInput 
                                autoFocus
                                multiline={true}
                                placeholder="小小鼓励，让团队更凝聚"
                                style={{backgroundColor: '#F2F5FC', flex: 5, borderRadius: 15, height: 40, marginLeft: 10, paddingLeft: 15}} 
                                onChangeText={(text) => this.setState({ messageContent: text })}
                            />
                            <TouchableOpacity onPress={() => {
                                if (!this.state.messageContent) {
                                    return;
                                }
                                this.setState({
                                    messageModal: false,
                                })
                                this.saveHarvestMessage();
                            }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{flex: 1,width: 64, height: 32, backgroundColor: '#1E6EFA', borderRadius: 20 }, 
                                    (this.state.messageContent) ? "" : CommonStyle.disableViewStyle]}>
                                    <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { textAlign: 'center', fontSize: 14 }]}>发送</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </TouchableOpacity>
                </Modal>
                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.08)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该成果?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteHarvest(this.state.harvestId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
            </View>
        )
    }
    
}         

const styles = StyleSheet.create({
    headline:{
        height:75,
        // paddingLeft:13,
        // paddingRight:13,
        flexDirection:'row',
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 14,
        marginRight: 16,
        marginTop: 5
    },
    titleTextStyle: {
        fontSize: 16,
        lineHeight: 22
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        // color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },
    holdbtnView: {
        fontSize: 16, width: 60, height: 30,
        borderWidth: 1,
        borderColor: 'rgba(250, 250, 250, 1)',
        backgroundColor: 'rgba(30, 110, 250, 1)',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 5,
        borderRadius: 4,
        flexDirection: 'row'
    },
    holdBtnText: {
        color: 'rgba(250, 250, 250, 1)', fontSize: 16
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        marginBottom: 6,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
    itemContentTextStyle: {
        marginLeft: 14,
        marginRight: 16,
        marginTop: 3
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
});


import React, { Component } from 'react';
import { View, Text, StyleSheet, Image, FlatList, Dimensions, ScrollView, TouchableOpacity, StatusBar, ImageBackground, Linking } from 'react-native';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import Swiper from 'react-native-swiper';
import { WToast } from 'react-native-smart-tip';
var CommonStyle = require('../../assets/css/CommonStyle');
// var Dimensions = require('Dimensions');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
var cols = 4;
var cellWH = 100;
var vMargin = (screenWidth - cellWH * cols) / (cols + 1);
var hMargin = 20;
const leftLabWidth = 130;
class DigitalEmployeesHome extends Component {
    constructor(props) {
        super(props);
        console.log("======Home=Props:", props.navigationContext);
        this.state = {
            digitalEmployeesMenuDataSource: [],
            //积分排名信息
            dataSource: [],
            advertisingDataSource: [],
            menuTypeFlagDR: "",
            swiperDataSource: [],
            informationDataSource: [],
            myBacklogDailyTotalRecord: 0,
            pointRanking: "",
            pointTotalValue: "",
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        if (null != constants.tenantExtAttrJSON) {
            if (constants.tenantExtAttrJSON.menuTypes.indexOf('D') > 0 || constants.tenantExtAttrJSON.menuTypes.indexOf('R') > 0) {
                this.setState({
                    menuTypeFlagDR: true,
                })
                console.log("=====D,R==")
            }
        }
        this.loadswiperDataSource();
        this.loadInformationDataSource();
        this.loadMyBacklogDailyData();
        this.loadMyPointData();
        // this.changeMessageRemindIcon();
        console.log("===测试===")
        console.log(JSON.stringify(constants.roleInfo.menuDTOList, undefined, 2))

        var homeMenuDTO;
        var homeMenuList;
        for (var j = 0, len = constants.roleInfo.menuDTOList.length; j < len; j++) {
            homeMenuDTO = constants.roleInfo.menuDTOList[j];
            if (homeMenuDTO && homeMenuDTO.menuCode && "HomeStackScreen" === homeMenuDTO.menuCode) {
                homeMenuList = homeMenuDTO.menuDTOList;
                console.log("===HomeStackScreen==homeMenuDTO===");
                console.log(JSON.stringify(homeMenuList, undefined, 2))
                break;
            }
        }
        if (!homeMenuList || homeMenuList.length <= 0) {
            console.log("=========首页子菜单是空的");
            return;
        }
        // 重新排序
        var MenuList = []
        var homeMenuSortString = [
            "租户管理", "数字化管理", "课程管理", "提问管理", "排课设置", "考试管理", "考核管理", "积分管理", "销售管理", "就业实习平台", "物资管理", "药品管理", "病患管理", "平台设置", "产销平台", "客户管理", "收入管理", "外协管理",
            "付款管理", "订单管理", "原料管理", "审核管理", "半成品管理", "干燥洞管理", "装窑管理", "隧道窑高温区管理", "烧结管理", "卸窑管理", "砖料出入库", "自检管理", "质检管理", "成品检选", "库存管理", "报表", "设备巡检", "系统设置", "个人中心"
        ]
        for (var index = 0; index < homeMenuSortString.length; index++) {
            for (var y = 0; y < homeMenuList.length; y++) {
                if (homeMenuList[y].menuName == homeMenuSortString[index]) {
                    for (var x = 0; x < homeMenuList[y].menuDTOList.length; x++) {
                        var roleSecondLevelMenuDTO = homeMenuList[y].menuDTOList[x]
                        var sonMenuDTO = {
                            "code": roleSecondLevelMenuDTO.menuCode,
                            "icon": { uri: roleSecondLevelMenuDTO.menuIcon.replace("../../assets/icon", "http://image.njjzgk.com/images/jizhi_app/icon") },
                            "title": roleSecondLevelMenuDTO.menuName,
                            "component": roleSecondLevelMenuDTO.menuUrl
                        }
                        MenuList.push(sonMenuDTO)
                    }
                }
            }
        }

        console.log("===MenuList===");
        console.log(JSON.stringify(MenuList, undefined, 2))
        if (MenuList.length >= 3 && MenuList.length <= 6) {
            var newMenuList = MenuList.slice(0, 3)
            newMenuList.push({
                "code": "HomeStackScreen",
                "icon": require('../../assets/icon/moreItem.png'),
                "title": "更多",
                "component": "Home"
            })
            console.log("===new MenuList===");
            console.log(JSON.stringify(newMenuList, undefined, 2))
            this.setState({
                digitalEmployeesMenuDataSource: newMenuList
            })
        } else if (MenuList.length >= 7) {
            var newMenuList = MenuList.slice(0, 7)
            newMenuList.push({
                "code": "HomeStackScreen",
                "icon": require('../../assets/icon/moreItem.png'),
                "title": "更多",
                "component": "Home"
            })
            console.log("===new MenuList===");
            console.log(JSON.stringify(newMenuList, undefined, 2))
            this.setState({
                digitalEmployeesMenuDataSource: newMenuList
            })
        }
        this.loadUserList();

    }
    loadUserList = () => {
        let url = "/biz/point/record/userank";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 3
        };
        httpPost(url, loadRequest, this.loadUserListCallBack);
    }

    loadUserListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            console.log("积分排名信息", JSON.stringify(response.data.dataList, undefined, 2))
            this.setState({
                dataSource: [...response.data.dataList]
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    loadMyPointData = () => {
        let url = "/biz/point/record/userank";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "uerId": constants.loginUser.userId,
        };
        httpPost(url, loadRequest, this.loadMyPointDataCallBack);
    }

    loadMyPointDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                pointTotalValue: response.data.dataList[0].pointTotalValue,
                pointRanking: response.data.dataList[0].pointRanking,
            })
            console.log("====@@@@@@@@@@=====：", response.data)
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <View></View>
        )
    }
    // 头部中间
    renderTitleItem() {
        return (
            <Text>首页</Text>
        )
    }

    logout = () => {
        console.log("===logout");
        let url = "/biz/user/logout?a=123&b=234"
        httpGet(url, this.logout_call_back);
    }

    logout_call_back = (response) => {
        console.log("=====logout_call_back:", response);
        this.props.navigation.navigate('MyCenterOrLogin');
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={this.logout.bind(this)}>
                <Text style={[CommonStyle.headRightText, { color: '#33333375' }]}>首页登出</Text>
            </TouchableOpacity>
        )
    }

    // 点击跳转
    _pressJump(item) {
        const { navigation } = this.props;
        if (navigation && item.component != null) {
            navigation.navigate(item.component, {
                // 测试参数
                itemId: 1000000,
                code: item.code,
                title: item.title,
                isPersonalCenter: item.code == "message_remind" ? "Y" : null,
                refresh: item.code == "message_remind" ? this.changeMessageRemindIcon : null
            })
        }
    }

    loadswiperDataSource = () => {
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10,
            "advertisingType": "D"
        };
        httpPost(url, loadRequest, this.loadswiperDataSourceCallBack);
    }

    loadswiperDataSourceCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                swiperDataSource: [...response.data.dataList],
            })
            var swiper = this.refs.swiper;

            swiper.scrollBy(0, false)

            console.log("轮播图数据：", JSON.stringify(response.data, null, 6))
            console.log("地址" + JSON.stringify(constants.image_addr, null, 6))

        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadInformationDataSource = () => {
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 3,
            "advertisingType": "N"
        };
        httpPost(url, loadRequest, this.loadInformationDataSourceCallBack);
    }

    loadInformationDataSourceCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                informationDataSource: [...response.data.dataList],
            })
            var swiper = this.refs.swiper;

            swiper.scrollBy(0, false)

            console.log("资讯数据：", JSON.stringify(response.data, null, 6))
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadMyBacklogDailyData = () => {
        let url = "/biz/daily/audit/task/list";
        let loadRequest = {
            "userId": constants.loginUser.userId,
            "my_backlog": "Y",
            "dailyState": "0AA"
        };
        httpPost(url, loadRequest, this.loadMyBacklogDailyDataCallBack);
    }

    loadMyBacklogDailyDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                myBacklogDailyTotalRecord: response.data.totalRecord
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow = (item) => {
        if ("hidden" === item.visibility) {
            return;
        }
        // 登出
        // if ("logout" === item.code) {
        //     return (
        //         <TouchableOpacity onPress={this.logout.bind(this)}>
        //             <View key={item.code} style={styles.innerViewStyle}>
        //                 <Image style={styles.innerViewImageStyle} source={item.icon}></Image>
        //                 <Text style={CommonStyle.bodyTextStyle}>{item.title}</Text>
        //             </View>
        //         </TouchableOpacity>
        //     )
        // }
        return (
            <View>


                <TouchableOpacity onPress={this._pressJump.bind(this, item)} >
                    <View key={item.code} style={{
                        width: cellWH,
                        height: cellWH - 20,
                        marginLeft: vMargin,
                        marginTop: hMargin,
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}>
                        <Image style={{
                            width: 50,
                            height: 50,
                        }} source={item.icon}></Image>
                        <Text style={{
                            fontSize: 16,
                            flexWrap: 'wrap',
                            alignItems: 'center',
                        }}>{item.title}</Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    render() {
        return (
            <View>
                <StatusBar
                    // animated={true} //指定状态栏的变化是否应以动画形式呈现。目前支持这几种样式：backgroundColor, barStyle和hidden  
                    hidden={false}  //是否隐藏状态栏。
                    backgroundColor={'#FFFFFF'} //状态栏的背景色  
                    // translucent={true}//指定状态栏是否透明。设置为true时，应用会在状态栏之下绘制（即所谓“沉浸式”——被状态栏遮住一部分）。常和带有半透明背景色的状态栏搭配使用。  
                    barStyle={'dark-content'} // enum('default', 'light-content', 'dark-content')   
                >
                </StatusBar>
                <CommonHeadScreen title='极致学社'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()} />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    {
                        this.state.menuTypeFlagDR == true ?
                            <View style={{ alignItems: 'center' }} >
                                <View style={[CommonStyle.contentViewStyle, { height: 200, width: screenWidth, borderColor: '#FFFFFF', paddingLeft: 10, paddingRight: 10 }]}>
                                    {
                                        this.state.swiperDataSource && this.state.swiperDataSource.length > 0
                                            ?
                                            <Swiper style={[{ justifyContent: 'center' }]}
                                                ref="swiper"
                                                key={this.state.swiperDataSource.length}
                                                height={300}
                                                // horizontal={false} 
                                                autoplay={true} //自动轮播
                                                autoplayTimeout={3.5} //每隔2秒切换
                                            >
                                                {
                                                    this.state.swiperDataSource && this.state.swiperDataSource.length > 0 ?
                                                        (
                                                            this.state.swiperDataSource.map((item, index) => {
                                                                return (
                                                                    <TouchableOpacity onPress={() => { Linking.openURL(item.advertisingLink) }}>
                                                                        <Image style={[{ height: 180, borderRadius: 10, width: screenWidth - 20 }]} source={{ uri: constants.image_addr + '/' + item.advertisingImage }} resizeMode='contain' />
                                                                    </TouchableOpacity>
                                                                );
                                                            })
                                                        )
                                                        :
                                                        <View></View>
                                                }


                                            </Swiper>
                                            :
                                            <Swiper style={[styles.wrapper, { justifyContent: 'center', alignItems: 'center' }]}
                                                ref="swiper"
                                                height={300}
                                                // horizontal={false} 
                                                autoplay={false} //自动轮播
                                            // autoplayTimeout={3.5} //每隔2秒切换
                                            // data={this.state.swiperDataSource}
                                            // renderItem={({ item, index }) => this.renderPictureRow(item, index)}
                                            >
                                                <View></View>


                                            </Swiper>

                                    }
                                </View>
                            </View>

                            :
                            <View />
                    }
                    {/* 
                    {this.state.menuTypeFlagDR == true ?
                        <View style={{
                            flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'center',
                            width: screenWidth, marginTop: 2, backgroundColor: '#FFF'
                        }}>
                            
                             <Image style={[{ height: 180, borderRadius: 10, width: screenWidth / 0.9 }]} source={require('../../assets/image/entrepreneurshipGuide.png')} /> 
                        </View>
                        :
                        <View />
                    } */}
                    <TouchableOpacity onPress={() => {
                        this.props.navigation.navigate("MyBacklogDailyList",
                            {
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }}>
                        <View style={{ flexDirection: 'row', flexWrap: 'wrap', backgroundColor: 'rgba(255, 244, 236, 1)', position: 'relative', height: 50, marginLeft: vMargin + 15, marginRight: vMargin + 15, marginTop: 20, justifyContent: 'space-between', borderRadius: 10 }}>
                            <View style={{ marginTop: 15, paddingLeft: 20, position: 'relative', width: 100 }}>
                                <Text>我的待办</Text>
                            </View>
                            <View style={{
                                alignItems: 'center', justifyContent: 'center', marginTop: 15, backgroundColor: 'rgba(255,153,25, 0.15)', width: 30, position: 'relative', height: 21, width: 25, marginRight: 10, borderRadius: 8
                            }}>
                                <Text style={{ color: 'rgba(253, 66, 70, 1)', fontWeight: '500' }} textAlign>{this.state.myBacklogDailyTotalRecord}</Text>
                            </View>
                        </View>
                    </TouchableOpacity>

                    <View style={[{ width: screenWidth, justifyContent: 'space-between', }]}>
                        <FlatList
                            numColumns={4}
                            data={this.state.digitalEmployeesMenuDataSource}
                            renderItem={({ item }) => this.renderRow(item)}
                        />
                    </View>

                    <View style={{ marginTop: hMargin, }}>
                        <TouchableOpacity onPress={() => {
                            this.props.navigation.navigate("PointRanking",
                                {
                                    // 传递回调函数
                                    refresh: this.callBackFunction
                                })
                        }}>
                            <ImageBackground source={require('../../assets/image/pointsRanking.png')} style={{ width: screenWidth, height: 420 }}>

                                <View style={[{ width: screenWidth - (leftLabWidth + 25), paddingTop: 55, position: 'relative', paddingLeft: 25, display: 'flex', flexDirection: 'column', justifyContent: 'center', }]}>
                                    {
                                        this.state.userPhoto ?
                                            <Image source={{ uri: this.state.userPhotoUrl }} style={{ height: 48, width: 48, borderRadius: 50 }} />
                                            :
                                            <Image style={{ width: 48, height: 51 }} source={require('../../assets/icon/iconfont/newAddHead.png')}></Image>
                                    }
                                    <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16, }}>{constants.loginUser.userName}</Text>
                                    {/* <View style={{ justifyContent: "space-between", width: screenWidth - 15, flexDirection: 'row', position: 'absolute', paddingTop: 105 }}>
                                    <View style={{ flexDirection: 'row', backgroundColor: '#FF9919', width: 200 }}>

                                    </View>

                                </View> */}

                                    <View style={[{
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        width: screenWidth - 15,
                                        flexDirection: 'row',
                                        paddingLeft: 85,
                                        position: 'absolute',
                                        paddingTop: 20
                                    }]}>
                                        <Text style={[{ fontSize: 12 }]}>当前排名：</Text>

                                    </View>
                                    <View style={[{
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        width: screenWidth - 15,
                                        flexDirection: 'row',
                                        paddingLeft: 85,
                                        position: 'absolute',
                                        paddingTop: 60
                                    }]}>
                                        <Text style={[{ fontSize: 16, color: '#FF9100' }]}>NO.{this.state.pointRanking}</Text>

                                    </View>

                                </View>
                                <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', marginTop: 70, paddingLeft: 20, paddingRight: 20 }}>
                                    <View style={{ flexDirection: 'row' }}>
                                        <View style={{ width: 45 }}>
                                            <Text>周排名</Text>
                                        </View>
                                        <View style={{ width: 90 - 20 }}></View>
                                    </View>
                                    <View>
                                        <Text>姓名</Text>
                                    </View>
                                    <View>
                                        <Text>总积分</Text>
                                    </View>
                                </View>
                                {/* 排名第一 */}
                                <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', marginTop: 10, paddingLeft: 20, paddingRight: 20 }}>
                                    <View style={{ flexDirection: 'row' }}>
                                        <Image style={{ width: 45, height: 48 }} source={require('../../assets/icon/workbench/workbench_points_ranking_first.png')}></Image>
                                        <View style={{ width: 45 - 20, height: 48 }}></View>
                                        {
                                            this.state.dataSource[0] && this.state.dataSource[0].userPhoto ?
                                                <Image style={{ width: 48, height: 51 }} source={{ uri: (constants.image_addr + '/' + this.state.dataSource[0].userPhoto) }}></Image>
                                                :
                                                <Image style={{ width: 48, height: 51 }} source={require('../../assets/icon/iconfont/newAddHead.png')}></Image>
                                        }
                                    </View>

                                    <View style={{ flexDirection: 'row', position: 'relative' }}>
                                        <View style={{ flexDirection: 'row', paddingLeft: 15, paddingTop: 12 }}>
                                            <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16, }}>{this.state.dataSource[0] ? this.state.dataSource[0].userName : "0"}</Text>
                                        </View>
                                    </View>

                                    <View style={{ flexDirection: 'row', position: 'relative', paddingTop: 12 }}>
                                        <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16, }}>{this.state.dataSource[0] ? this.state.dataSource[0].pointTotalValue : "0"}</Text>
                                    </View>
                                </View>
                                {/* 排名第二 */}
                                <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', marginTop: 10, paddingLeft: 20, paddingRight: 20 }}>
                                    <View style={{ flexDirection: 'row' }}>
                                        <Image style={{ width: 45, height: 48 }} source={require('../../assets/icon/workbench/workbench_points_ranking_second.png')}></Image>
                                        <View style={{ width: 45 - 20, height: 48 }}></View>
                                        {
                                            this.state.dataSource[1] && this.state.dataSource[1].userPhoto ?
                                                <Image style={{ width: 48, height: 51 }} source={{ uri: (constants.image_addr + '/' + this.state.dataSource[1].userPhoto) }}></Image>
                                                :
                                                <Image style={{ width: 48, height: 51 }} source={require('../../assets/icon/iconfont/newAddHead.png')}></Image>
                                        }
                                    </View>

                                    <View style={{ flexDirection: 'row', position: 'relative' }}>
                                        <View style={{ flexDirection: 'row', paddingLeft: 15, paddingTop: 12 }}>
                                            <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16, }}>{this.state.dataSource[1] ? this.state.dataSource[1].userName : "0"}</Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', position: 'relative', paddingTop: 12 }}>
                                        <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16, }}>{this.state.dataSource[1] ? this.state.dataSource[1].pointTotalValue : "0"}</Text>
                                    </View>
                                </View>
                                {/* 排名第三 */}
                                <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', marginTop: 10, paddingLeft: 20, paddingRight: 20 }}>
                                    <View style={{ flexDirection: 'row' }}>
                                        <Image style={{ width: 45, height: 48 }} source={require('../../assets/icon/workbench/workbench_points_ranking_third.png')}></Image>
                                        <View style={{ width: 45 - 20, height: 48 }}></View>
                                        {
                                            this.state.dataSource[2] && this.state.dataSource[2].userPhoto ?
                                                <Image style={{ width: 48, height: 51 }} source={{ uri: (constants.image_addr + '/' + this.state.dataSource[2].userPhoto) }}></Image>
                                                :
                                                <Image style={{ width: 48, height: 51 }} source={require('../../assets/icon/iconfont/newAddHead.png')}></Image>
                                        }
                                    </View>

                                    <View style={{ flexDirection: 'row', position: 'relative' }}>
                                        <View style={{ flexDirection: 'row', paddingLeft: 15, paddingTop: 12 }}>
                                            <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16, }}>{this.state.dataSource[2] ? this.state.dataSource[2].userName : "0"}</Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', position: 'relative', paddingTop: 12 }}>
                                        <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16, }}>{this.state.dataSource[2] ? this.state.dataSource[2].pointTotalValue : "0"}</Text>
                                    </View>
                                </View>

                            </ImageBackground>
                        </TouchableOpacity>

                        <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', height: 40, paddingLeft: 20, paddingRight: 20 }}>
                            <Text style={{ fontSize: 24, fontWeight: '500', fontFamily: 'PingFangSC - Medium' }}>精品资讯</Text>
                            <Text style={{ fontSize: 16, color: 'rgba(0, 10, 32, 0.45)', }}>查看更多</Text>
                        </View>
                        <View style={{ backgroundColor: 'D0E11F', marginBottom: 10 }}>
                            {
                                this.state.informationDataSource && this.state.informationDataSource.length > 0 ?
                                    (
                                        this.state.informationDataSource.map((item, index) => {
                                            return (
                                                <TouchableOpacity onPress={() => { Linking.openURL(item.advertisingLink) }}>
                                                    <View style={{ display: 'flex', flexDirection: 'row', marginLeft: 10, marginRight: 10, marginTop: 18 }}>
                                                        <Image style={[{ height: 100, borderRadius: 10, width: 144 }]} source={{ uri: constants.image_addr + '/' + item.advertisingImage }} resizeMode='contain' />
                                                        <View style={{ flexDirection: 'column', paddingTop: 1, marginRight: screenWidth / 3 }}>
                                                            <View style={{
                                                                marginLeft: 15,
                                                                marginRight: 16,
                                                                marginTop: 1,
                                                                lineHeight: 20,
                                                            }}>
                                                                <Text style={{
                                                                    fontFamily: 'PingFangSC-Semibold', fontSize: 18,
                                                                    textAlign: 'left',
                                                                    textAlignVertical: 'top',
                                                                    fontWeight: '600',
                                                                }}>{item.advertisingTitle}</Text>
                                                            </View>
                                                            <View style={{
                                                                marginLeft: 15,
                                                                marginRight: 10,
                                                                marginTop: 24,
                                                                lineHeight: 24,
                                                            }}>
                                                                <Text style={{
                                                                    fontFamily: 'PFSC-Regular', fontSize: 16,
                                                                    textAlign: 'left',
                                                                    textAlignVertical: 'top',
                                                                    color: 'rgba(0, 10, 32, 0.65)',
                                                                }} numberOfLines={2}>{item.advertisingContent}</Text>
                                                            </View>
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            );
                                        })
                                    )
                                    :
                                    <View></View>
                            }
                        </View>
                    </View>
                </ScrollView >
            </View >
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140
    // },
    classViewStyle: {
        height: 50,
        alignItems: 'flex-start',
        justifyContent: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0'
    },
    classTextStyle: {
        color: '#000',
        fontSize: 20,
        marginLeft: 15
    },
    innerViewStyle: {
        width: cellWH,
        height: cellWH,
        marginLeft: vMargin,
        marginTop: hMargin,
        alignItems: 'center',
        justifyContent: 'center'
    },
    innerViewImageStyle: {
        width: cellWH - 50,
        height: cellWH - 50
    },
    wrapper: {
        justifyContent: 'center'
    },
});
module.exports = DigitalEmployeesHome;
import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Linking, Clipboard,
    FlatList, RefreshControl, Image, Modal, TextInput, ScrollView, ImageBackground, ProgressBarAndroid
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import ClassHeadScreen from '../../../component/ClassHeadScreen';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../../utils/CameraRollUtils';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;

export default class MyCourseList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            seltaskState: null,

            searchKeyWord: "",

            showCourseTypeSearchItemBlock: false,
            courseTypeDataSource: [],
            selcourseTypeId: null,
            selcourseTypeName: null,

            showCourseLevelSearchItemBlock: false,
            courseLevelDataSource: null,
            selcourseLevelId: null,
            selcourseLevelName: null,

            selStaffId: null,

            qryNowTime: null,
            //待展示开始学习按钮的courseID
            courseIdData: [],
            flag: true,
            documentTypeList: ["TD", "TV"],
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    initqryNowTime = () => {
        // 当前时间
        var currentDate = new Date();
        // currentDate.setMonth(currentDate.getMonth() - 1);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _qryNowTime = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            qryNowTime: _qryNowTime
        })
    }



    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        // 课程类型
        this.initqryNowTime();
        let loadCourseTypeUrl = "/biz/course/type/list";
        let loadCourseTypeRequest = { "qryAll": "Y", "currentPage": 1, "pageSize": 1000 };
        httpPost(loadCourseTypeUrl, loadCourseTypeRequest, (response) => {
            if (response.code == 200 && response.data.dataList) {
                var courseTypeData = response.data.dataList;
                courseTypeData.unshift({ "courseTypeId": 0, "courseTypeName": "全部" })
                this.setState({
                    courseTypeDataSource: courseTypeData,
                })
                // courseTypeData
                // console.log("==========课程类型数据源：", this.state.courseTypeDataSource);
            }
        });

        //所属职级
        let loadCourseLevelUrl = "/biz/course/level/list";
        let loadCourseLevelRequest = { "qryAll": "Y", "currentPage": 1, "pageSize": 1000 };
        httpPost(loadCourseLevelUrl, loadCourseLevelRequest, (response) => {
            if (response.code == 200 && response.data.dataList) {
                var courseLevelData = response.data.dataList;
                courseLevelData.unshift({ "courseLevelId": 0, "courseLevelName": "全部" })
                this.setState({
                    courseLevelDataSource: response.data.dataList,
                })
                // console.log("==========所属职级数据源：", this.state.courseLevelDataSource);
            }
        });
        this.loadMyCourseList();
    }



    // 回调函数
    callBackFunction = () => {
        let url = "/biz/course/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "courseLevelId": this.state.selcourseLevelId,
            "courseTypeId": this.state.selcourseTypeId,
            "userId": constants.loginUser.userId,
            "documentTypeList": this.state.documentTypeList

        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/course/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "courseLevelId": this.state.selcourseLevelId,
            "courseTypeId": this.state.selcourseTypeId,
            "documentTypeList": this.state.documentTypeList
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }

    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadMyCourseList();
    }

    loadMyCourseList = () => {
        let url = "/biz/course/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "courseLevelId": this.state.selcourseLevelId,
            "courseTypeId": this.state.selcourseTypeId,
            "documentTypeList": this.state.documentTypeList
        };
        httpPost(url, loadRequest, this.loadMyCourseCallBack);
    }

    loadMyCourseCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld, ...dataNew];
            console.log("我的课程列表",JSON.stringify(dataAll, null, 6))
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false,

            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }


    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                Alert.alert('确认', '您确定要导出PDF文件吗？', [
                    {
                        text: "取消", onPress: () => {
                            WToast.show({ data: '点击了取消' });
                        }
                    },
                    {
                        text: "确定", onPress: () => {
                            WToast.show({ data: '点击了确定' });
                            this.exportPdfFile()
                        }
                    }
                ]);
            }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 23, height: 23 }} source={require('../../../assets/icon/iconfont/newExport.png')}></Image>
            </TouchableOpacity>
        )
    }

    exportPdfFile = () => {
        console.log("=======exportPdfFile");
        console.log("constants.loginUser", JSON.stringify(constants.loginUser, null, 6))
        let url = "/biz/generate/pdf/course";
        let requestParams = {
            "currentPage": 1,
            "pageSize": 1000,
            "checkOutUserId": constants.loginUser.userId,
            "courseTypeId": this.state.selcourseTypeId == 0 ? null : this.state.selcourseTypeId,
            "courseLevelId": this.state.selcourseLevelId == 0 ? null : this.state.selcourseLevelId,
            "userId": constants.loginUser.userId,
            "tenantId": constants.loginUser.tenantId,
            "taskState": this.state.selCourseStateCode === 'all' ? null : this.state.selCourseStateCode,
            "scene": "D",
            "documentTypeList": this.state.documentTypeList
        };
        httpPost(url, requestParams, (response) => {
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data);
                WToast.show({ data: "导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data });
                Alert.alert('确认', '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?', [
                    {
                        text: "不打开", onPress: () => {
                            WToast.show({ data: '点击了不打开' });
                        }
                    },
                    {
                        text: "打开", onPress: () => {
                            WToast.show({ data: '点击了打开' });
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }


    addCourseTask = (taskItem, index) => {
        console.log("=======addCourseTask=taskItem", taskItem);
        // console.log("@_addCourseTask_@", JSON.stringify(this.state.dataSource, null, 6));
        var _dataSource = copyArr(this.state.dataSource);
        if (_dataSource && Object.keys(_dataSource).length > 0) {
            _dataSource = _dataSource.filter(item => item.courseId == taskItem.courseId);
        }
        console.log("数据" + _dataSource[0].courseDuration);

        var dateTime = new Date();
        console.log("dateTime1=====" + dateTime);
        dateTime = dateTime.setHours(dateTime.getHours() + 8);
        dateTime = new Date(dateTime);
        dateTime = dateTime.setDate(dateTime.getDate() + _dataSource[0].courseDuration);
        dateTime = new Date(dateTime);
        console.log("dateTime3=====" + dateTime);
        let Y = dateTime.getFullYear() + '-';
        let M = (dateTime.getMonth() + 1 < 10 ? '0' + (dateTime.getMonth() + 1) : dateTime.getMonth() + 1) + '-';
        let D = (dateTime.getDate() < 10 ? '0' + (dateTime.getDate()) : dateTime.getDate());
        let date = Y + M + D;
        // console.log("dateTime4=====", date);
        let requestUrl = "/biz/course/task/add";
        let requestParams = {
            'courseId': taskItem.courseId,
            'planCompletionTime': date,
            'checkOutUserId': constants.loginUser.userId,

        };
        httpPost(requestUrl, requestParams, this.addCourseTaskCallBack)

    }
    // 开始学习按操作的回调
    addCourseTaskCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "该课程学习任务已开始" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            if (response.message == "重复检验失败") {
                response.message = "任务已开始，无需重复点击";
                WToast.show({ data: response.message });
            }
        }
    }

    renderRow = (item, index) => {

        //计算最后学习时间
        item.lastStudyTime=(
            item.syCourseTaskDTO?
            (
                item.syCourseTaskDTO&&                                
                item.syCourseTaskDTO.portalTrackDetailDTOList&&
                item.syCourseTaskDTO.portalTrackDetailDTOList[0]&&
                item.syCourseTaskDTO.portalTrackDetailDTOList[0].gmtCreated
                ?
                item.syCourseTaskDTO.portalTrackDetailDTOList[0].gmtCreated:
                item.syCourseTaskDTO.gmtCreated
            ):(
                item.gmtCreated
            )
        )           


        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("MyCourseDetail", {
                    "courseTaskId": item.courseTaskId,
                    "courseId":item.courseId,
                    // 传递回调函数
                    refresh: this.callBackFunction 
                    
                })
            }}>
                {/* <View style={{ position:'absolute', right: 13, top: 12,zIndex:10}}>
                    <View style={[{width: 35, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                        <Image style={{ width: 28, height: 28 }} source={require('../../../assets/icon/iconfont/more.png')}></Image>
                    </View>
                </View> */}
                <View style={{
                    flexDirection:"row",
                    height:129,
                    width:screenWidth-13,
                    // backgroundColor:"red"
                }}>

                    <View style={{ width:130+28,alignItems:"center",justifyContent:"center"}}>
                        <View>
                            {
                                item.coursePhoto?
                                <View>
                                <Image source={{ uri:constants.image_addr + '/' + item.coursePhoto }} style={{ width: 130, height: 93, borderRadius:10}}></Image>
                                </View>
                                :
                                <View>
                                    <View style={{position:'absolute',zIndex:10,width: 130, height: 93,alignItems:"center",justifyContent:"center"}}>
                                        <Text style={{fontSize:20,color:"white"}}>{item.courseName}</Text>
                                    </View>
                                    <Image source={require('../../../assets/image/defaultCover.png')} style={{ width: 130, height: 93, borderRadius:10}}></Image>
                                </View>
                            }
                            <View style={{position:'absolute',zIndex:1}}>
                                {
                                    item.taskState === '0AA' ?
                                        <Text style={{ backgroundColor: '#0000ff', color: 'rgba(255, 255, 255, 1)', fontSize: 12, width: 55, height: 19, borderTopLeftRadius: 6, borderBottomRightRadius: 6, paddingLeft: 9, paddingTop: 2 }}>学习中</Text>
                                        :
                                        (
                                            item.taskState === '0BB' ?
                                                <Text style={{ backgroundColor: '#ff0000', color: 'rgba(255, 255, 255, 1)', fontSize: 12, width: 55, height: 19, borderTopLeftRadius: 6, borderBottomRightRadius: 6, paddingLeft: 9, paddingTop: 2 }}>已超期</Text>
                                                :
                                                (
                                                    item.taskState === '0CC' ?
                                                        <Text style={{ backgroundColor: '#008000', color: 'rgba(255, 255, 255, 1)', fontSize: 12, width: 55, height: 19, borderTopLeftRadius: 6, borderBottomRightRadius: 6, paddingLeft: 9, paddingTop: 2 }}>已完成</Text>
                                                        :
                                                        <Text style={{ backgroundColor: 'rgba(0, 10, 32, 0.45)', color: 'rgba(255, 255, 255, 1)', fontSize: 12, width: 55, height: 19, borderTopLeftRadius: 6, borderBottomRightRadius: 6, paddingLeft: 9, paddingTop: 2 }}>未开始</Text>
                                                )
                                        )
                                }
                            </View>
                        </View>
                    </View>


                    <View style={{ flex:1 ,alignItems:"center",justifyContent:"center"}}>

                        <View style={{height: 93,width:"100%"}}>
                        {/* 自定义组件 */}
                        <ClassHeadScreen 
                            redTitle={item.courseLevelName}
                            blackTitle={" 第" + item.courseSort + "课 " + item.courseName}
                        />
                        {/* <View style={{backgroundColor:"white", height:44 ,flexDirection:"row"}}>
                            <View style={{
                                position:'absolute',
                                backgroundColor:"red",
                                borderRadius:6,
                                borderBottomLeftRadius:0,
                                height:23,
                                // width:19,
                                justifyContent: 'center',
                                alignItems:"center",
                                paddingLeft:2,
                                paddingRight:2,
                                zIndex:10
                            }}>
                                <Text style={{fontSize:16,color:"white",}}>{item.courseLevelName?item.courseLevelName:"?"}</Text>
                            </View>
                            <Text style={{fontSize:16}}>{item.courseLevelName+" 第" + item.courseSort + "课 " + item.courseName}</Text>
                        </View> */}

                        <View style={{ height:20 ,flexDirection: 'row',}}>
                            <View  style={{         
                                    //外边距
                                    borderRadius:10,
                                    backgroundColor:'rgba(27,188,130,0.2)',
                                    height:20,
                                    paddingLeft:10,paddingRight:10,
                                    justifyContent: 'center',
                                    alignItems:"center"
                                }}>
                                <Text
                                    style={{fontSize:12,color:"#1BBC82"}}
                                >
                                    {item.courseTypeName}
                                </Text>
                            </View>  
                        </View>

                        {
                            item.courseTaskId&&item.syCourseTaskDTO?
                            <View style={{ height:17+12}}>
                                <View style={{ height: 12 }}>
                                    <ProgressBarAndroid styleAttr='Horizontal' progress={
                                    item.syCourseTaskDTO.taskState=="0AA"?
                                    (
                                        (((new Date(item.lastStudyTime.substr(0,10)).getTime()-new Date(item.gmtCreated.substr(0,10)).getTime()) / (1000 * 3600 * 24))/item.courseDuration)>=1 ?0.8: (((new Date(item.lastStudyTime.substr(0,10)).getTime()-new Date(item.gmtCreated.substr(0,10)).getTime()) / (1000 * 3600 * 24))/item.courseDuration)
                                    )
                                    :
                                    (
                                        item.syCourseTaskDTO.taskState=="0BB"?
                                        (
                                            0.8
                                        )
                                        :
                                        (
                                            1
                                        )
                                    )
                                } color='rgba(30, 110, 250, 1)' indeterminate={false} style={{ backgroundColor: 'rgba(0,10,32,0.06)', borderRadius: 3, height: 4 ,marginTop:7}}></ProgressBarAndroid>
                                </View>
                                <View style={{ height:17 ,flexDirection:"row"}}>
                                    <Text>{"已学习（天）："}</Text>
                                    { 
                                        item.syCourseTaskDTO.taskState=="0AA"?
                                        (
                                            <Text>{ (((new Date(item.lastStudyTime.substr(0,10)).getTime()-new Date(item.gmtCreated.substr(0,10)).getTime()) / (1000 * 3600 * 24)))}</Text>
                                        )
                                        :
                                        (
                                            item.syCourseTaskDTO.taskState=="0BB"?
                                            (
                                                <Text style={{color:"red"}}>{"超期"}</Text>
                                            )
                                            :
                                            (
                                                <Text>{item.courseDuration}</Text>
                                            )
                                        )
                                    }
                                    <Text>{"/" + item.courseDuration}</Text>
                                </View>
                            </View>
                            :
                            <View style={{ height:17+12}}>
                                <View style={{ height: 12 }}>
                                    <ProgressBarAndroid styleAttr='Horizontal' progress={
                                        0
                                } color='rgba(30, 110, 250, 1)' indeterminate={false} style={{ backgroundColor: 'rgba(0,10,32,0.06)', borderRadius: 3, height: 4 ,marginTop:7}}></ProgressBarAndroid>
                                </View>
                                <View style={{ height:17 ,flexDirection:"row"}}>
                                    <Text>{"已学习（天）：0"}</Text>
                                    <Text>{"/" + item.courseDuration}</Text>
                                </View>
                            </View>                                             
                        }
                        </View>

                    </View>

                </View>
                <View style={styles.lineViewStyle}/> 
            </TouchableOpacity>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }

    // 课程类型
    renderCourseTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selcourseTypeId: item.courseTypeId,
                    selcourseTypeName: item.courseTypeName,
                })
            }}>
                <View key={"department_" + item.courseTypeId} style={[item.courseTypeId === this.state.selcourseTypeId ?
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                CommonStyle.choseToSearchItemsViewSize
                ]}>
                    <Text style={[item.courseTypeId === this.state.selcourseTypeId ?
                        CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.courseTypeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    //所属职级
    renderCourseLevelRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selcourseLevelId: item.courseLevelId,
                    selcourseLevelName: item.courseLevelName,
                })
            }}>
                <View key={"department_" + item.courseLevelId} style={[item.courseLevelId === this.state.selcourseLevelId ?
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                CommonStyle.choseToSearchItemsViewSize
                ]}>
                    <Text style={[item.courseLevelId === this.state.selcourseLevelId ?
                        CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.courseLevelName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/course/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "courseLevelId": this.state.selcourseLevelId,
            "courseTypeId": this.state.selcourseTypeId,
            "documentTypeList": this.state.documentTypeList
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    // 显示搜索项目
    showCourseTypeSearchItemSelect() {
        if (this.state.flag == false) {
            return
        }
        if (!this.state.courseTypeDataSource || this.state.courseTypeDataSource.length < 1) {
            WToast.show({ data: "请先添加课程类型" });
            return
        }
        this.setState({
            showCourseTypeSearchItemBlock: true,
            flag: false
        })
    }
    showCourseLevelSearchItemSelect() {
        if (this.state.flag == false) {
            return
        }
        if (!this.state.courseLevelDataSource || this.state.courseLevelDataSource.length < 1) {
            WToast.show({ data: "请先添加课程所属职级" });
            return
        }
        this.setState({
            showCourseLevelSearchItemBlock: true,
            flag: false
        })
    }



    render() {
        return (
            <View>
                <CommonHeadScreen title='我的课程'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.headViewStyle, { borderLeftWidth: 0, borderRightWidth: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ flexDirection: 'row', justifyContent: "flex-start", flexWrap: 'wrap', flexDirection: 'row' }}>
                        <View style={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                            <TouchableOpacity onPress={() => this.showCourseTypeSearchItemSelect()}>
                                {
                                    this.state.showCourseTypeSearchItemBlock ?
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                                {this.state.selcourseTypeId && this.state.selcourseTypeName ? (this.state.selcourseTypeName) : "课程类型"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchClosedIconSize]} source={require('../../../assets/icon/iconfont/arrow-up.png')}></Image>
                                        </View>
                                        :
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                                {this.state.selcourseTypeId && this.state.selcourseTypeName ? (this.state.selcourseTypeName) : "课程类型"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchOpenedIconSize]} source={require('../../../assets/icon/iconfont/arrow_down.png')}></Image>
                                        </View>
                                }
                            </TouchableOpacity>
                        </View>

                        <View style={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                            <TouchableOpacity onPress={() => this.showCourseLevelSearchItemSelect()}>
                                {
                                    this.state.showCourseLevelSearchItemBlock ?
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                                {this.state.selcourseLevelId && this.state.selcourseLevelName ? (this.state.selcourseLevelName) : "所属职级"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchClosedIconSize]} source={require('../../../assets/icon/iconfont/arrow-up.png')}></Image>
                                        </View>
                                        :
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                                {this.state.selcourseLevelId && this.state.selcourseLevelName ? (this.state.selcourseLevelName) : "所属职级"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchOpenedIconSize]} source={require('../../../assets/icon/iconfont/arrow_down.png')}></Image>
                                        </View>

                                }
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>


                <View>
                    {
                        this.state.showCourseTypeSearchItemBlock ?
                            <View style={[CommonStyle.choseToSearchBigBoxViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                                <View style={CommonStyle.heightLimited}>
                                    <ScrollView>
                                        <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                            <View style={[{ backgroundColor: 'rgba(255,255,255,1)' }, CommonStyle.choseToSearchItemsViewSize]}>
                                                <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>课程类型：</Text>
                                            </View>
                                            {
                                                (this.state.courseTypeDataSource && this.state.courseTypeDataSource.length > 0)
                                                    ?
                                                    this.state.courseTypeDataSource.map((item, index) => {
                                                        return this.renderCourseTypeRow(item)
                                                    })
                                                    : null
                                            }
                                        </View>
                                    </ScrollView>
                                </View>
                                <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showCourseTypeSearchItemBlock: false,
                                            flag: true
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        let loadUrl = "/biz/course/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "userId": constants.loginUser.userId,
                                            "courseLevelId": this.state.selcourseLevelId,
                                            "courseTypeId": this.state.selcourseTypeId,
                                            "documentTypeList": this.state.documentTypeList
                                        };
                                        console.log("选择的课程类型=====" + this.state.selcourseTypeId)
                                        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                        this.setState({
                                            showCourseTypeSearchItemBlock: false,
                                            flag: true
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            null
                    }
                    {
                        this.state.showCourseLevelSearchItemBlock ?
                            <View style={[CommonStyle.choseToSearchBigBoxViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                                <View style={CommonStyle.heightLimited}>
                                    <ScrollView>
                                        <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                            <View style={[{ backgroundColor: 'rgba(255,255,255,1)' }, CommonStyle.choseToSearchItemsViewSize]}>
                                                <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>所属职级：</Text>
                                            </View>
                                            {
                                                (this.state.courseLevelDataSource && this.state.courseLevelDataSource.length > 0)
                                                    ?
                                                    this.state.courseLevelDataSource.map((item, index) => {
                                                        return this.renderCourseLevelRow(item)
                                                    })
                                                    : null
                                            }
                                        </View>
                                    </ScrollView>
                                </View>
                                <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showCourseLevelSearchItemBlock: false,
                                            flag: true
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        let loadUrl = "/biz/course/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "courseTypeId": this.state.selcourseTypeId,
                                            "userId": constants.loginUser.userId,
                                            "courseLevelId": this.state.selcourseLevelId,
                                            "documentTypeList": this.state.documentTypeList

                                        };
                                        console.log("选择的职级=====" + this.state.selcourseLevelId)
                                        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                        this.setState({
                                            showCourseLevelSearchItemBlock: false,
                                            flag: true
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            null
                    }

                    <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                        <FlatList
                            data={this.state.dataSource}
                            renderItem={({ item, index }) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                    tintColor="#FF0000"
                                    title="loading"
                                    colors={['#FF0000', '#00FF00', '#0000FF']}
                                    progressBackgroundColor="#FFFF00"
                                    refreshing={this.state.refreshing}
                                    onRefresh={() => {
                                        this._loadFreshData()
                                    }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={() => this.flatListFooterComponent()}
                            onEndReached={() => this._loadNextData()}
                        // onEndReachedThreshold={0.2}
                        />
                    </View>
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentTextStyle: {
        marginLeft: 12,
        marginRight: 16,
        marginTop: 3,
        lineHeight: 24,
    },
    lineViewStyle:{
        // height:1,
        marginLeft: 13,
        marginRight: 13,
        // marginTop: 15,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },
});
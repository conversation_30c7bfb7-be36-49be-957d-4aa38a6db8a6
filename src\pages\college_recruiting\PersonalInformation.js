import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,
    FlatList,RefreshControl,ScrollView,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CrHeadScreen from '../../component/CrHeadScreen'
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
import { FilterType } from 'react-native-video';

export default class PersonalInformation extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            userPhotoUrl:"",
            userPhoto:"",

            staffName: "",
            staffSex: "",
            staffSexType:"",
            selSexId:1,
            
            staffBirthday: "",
            selectStaffBirthday:[],

            nationality: "",
            height: "",
            politicsStatus: "",
            professionalName: "",

            gradePoint:"",
            comprehensiveAbility:"",
            comprehensivePoint: "",

            englishLevelName: "",
            graduateInstitutions: "",
            education: "",
            nativePlace: "",
            address: "",
            staffTel: "",
            email: "",
                                                                               
            englishLevelName:"",
            englishLevelCode:"",
            selectedEnglishLevel:[],
            englishLevelDataSource:[],
            sexDataSource:[
                {
                    sexId:1,
                    sexType:"男",
                    sexName:"M"
                },
                {
                    sexId:2,
                    sexType:"女",
                    sexName:"L"
                }
            ],
            selSexName:"",
            selectedPoliticalStatus:[],            
            
            adjustFactor:"",
            graduateInstitutions:"",

            topBlockLayoutHeight:0,

            staffId:"",
        }
    }


    UNSAFE_componentWillMount() {
        httpPost("/biz/cr/staff/get", { "staffId": constants.loginUser.staffId }, (response) => {
            if (response.code === 200) {
                let userPhoto = response.data.electronicPhotos;
                this.setState({
                    userPhoto:response.data.electronicPhotos,
                    userPhotoUrl: constants.image_addr + '/' + userPhoto
                })
                console.log( userPhoto)
            }
        });

        console.log('componentWillMount');
        let url = "/biz/cr/staff/englishLevelList";
        let loadRequest = {};
        httpPost(url, loadRequest, this.loadEnglishLevelListCallBack);

        let politicalStatusDataSource =[
            {
                politicalStatusId:0,
                politicalStatusName:"中共党员"
            },
            {
                politicalStatusId:1,
                politicalStatusName:"中共预备党员"
            },
            {
                politicalStatusId:2,
                politicalStatusName:"共青团员"
            },
            {
                politicalStatusId:3,
                politicalStatusName:"群众"
            }
        ]
    
        this.setState({
            politicalStatusDataSource:politicalStatusDataSource,
        })
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        // var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectStaffBirthday:[currentDate.getFullYear(), currentDateMonth],
        })
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
            console.log("==========" + constants.loginUser.tenantName);
            console.log("==========" + constants.loginUser.staffId);
            var staffId = constants.loginUser.staffId;
            this.setState({
                staffId:staffId,
            })
            let url= "/biz/cr/staff/get";
            let loadRequest={"staffId":staffId};
             httpPost(url, loadRequest, this.loadPersonalInformationCallBack);
             this.loadAdjustFactor();
        }

        // console.log('componentWillMount');
        // const { route, navigation } = this.props;
        // if (route && route.params) {
        //     const { staffId } = route.params;
        //     if (staffId) {
        //         console.log("staffId +++++", staffId)
        //         this.setState({
        //             staffId: staffId
        //         })
        //         let loadTypeUrl = "/biz/cr/staff/get";
        //         let loadRequest = { 'staffId': staffId };
        //         httpPost(loadTypeUrl, loadRequest, this.loadPersonalInformationCallBack);
        //     }
        // }
    }

    loadPersonalInformationCallBack = (response) => {
        if (response.code == 200 && response.data) {

            this.setState({

                staffName:response.data.staffName,
                professionalName:response.data.professionalName,
                gradePoint:response.data.gradePoint/100,
                graduateInstitutions:response.data.graduateInstitutions,
                education:response.data.education,
                staffTel:response.data.staffTel,
                personalHonor:response.data.personalHonor,
                collegeEvaluation:response.data.collegeEvaluation,
                politicsStatus:response.data.politicsStatus,
                selectedPoliticalStatus:[response.data.politicsStatus],
                englishLevelName:response.data.englishLevelName,
                englishLevelCode:response.data.englishLevel,
                selectedEnglishLevel:[response.data.englishLevelName],
                staffSex:response.data.staffSex,
                staffSexType:response.data.staffSex == 'L' ? "女" : "男",
                selSexId:response.data.staffSex == 'L' ? 2 : 1,
                staffBirthday:response.data.staffBirthday,
                selectStaffBirthday:[response.data.staffBirthday],
                nationality:response.data.nationality,
                height:response.data.height,
                professionalName:response.data.professionalName,
                nativePlace:response.data.nativePlace,
                address:response.data.address,
                email:response.data.email,
                // personalHonor:response.data.personalHonor,
                // collegeEvaluation:response.data.collegeEvaluation,
                electronicPhotos:response.data.electronicPhotos,
                // extDataSource:response.data.crStaffExtDTOList,
                comprehensiveAbility:response.data.comprehensiveAbility/100,
                comprehensivePoint:response.data.comprehensivePoint/100,
            })
        }
    }

    loadEnglishLevelListCallBack=(response) =>{
        if (response.code == 200 && response.data) {
            this.setState({
                englishLevelDataSource:response.data
            })
        }
    } 

    loadAdjustFactor=()=>{
        let url = "/biz/tenant/get";
        let loadRequest = {
            "operateTenantId":constants.loginUser.tenantId
        };
        httpPost(url, loadRequest, this.loadAdjustFactorCallBack);
    }

    loadAdjustFactorCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                adjustFactor: response.data.adjustFactor,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:24, height:24}} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("TemplateMgrAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>新增模版</Text>
            </TouchableOpacity>
        )
    }


    openBirthdayTime(){
        this.refs.SelectBirthday.showMonth(this.state.selectStaffBirthday)
    }

    openPoliticalStatus(){      
        this.refs.SelectPoliticalStatus.showPoliticalStatus(this.state.selectedPoliticalStatus,this.state.politicalStatusDataSource)
    }

    openEnglishLevel(){   
        
        if (!this.state.englishLevelDataSource || this.state.englishLevelDataSource.length < 1) {
            WToast.show({data:"暂无数据"});
            return
        }
        
        this.refs.SelectEnglishLevel.showEnglishLevel(this.state.selectedEnglishLevel,this.state.englishLevelDataSource)
    }

    callBackSelectBirthdayValue(value){
        console.log("==========出生年月选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectStaffBirthday:value
        })
        if (this.state.selectStaffBirthday && this.state.selectStaffBirthday.length) {
            var staffBirthday = "";
            var vartime;
            for(var index=0;index<this.state.selectStaffBirthday.length;index++) {
                vartime = this.state.selectStaffBirthday[index];
                if (index===0) {
                    staffBirthday += vartime;
                }
                else{
                    staffBirthday += "" + vartime;
                }
            }
            this.setState({
                staffBirthday:staffBirthday
            })
        }
    }
    
    callBackSelectPoliticalStatusValue(value){
        console.log("==========政治面貌选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedPoliticalStatus:value
        })
        var politicalStatusName = value.toString();
        this.setState({
            politicsStatus:politicalStatusName
        })
    }

    callBackSelectEnglishLevelValue(value){
        console.log("==========英语能力选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedEnglishLevel:value
        })
        var englishLevelName = value.toString();
        this.setState({
            englishLevelName:englishLevelName
        })
        let url= "/biz/cr/staff/englishLevelByName";
        let loadRequest={"englishLevelName":englishLevelName};
        httpPost(url, loadRequest, this.loadCrEnglishLevelCallBack);
    }

    loadCrEnglishLevelCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                englishLevelCode:response.data.englishLevel
            })
        }
    }

    saveMyInterView=()=>{
        console.log("=======saveMyInterView");
        let toastOpts;
        if (!this.state.staffName) {
            toastOpts = getFailToastOpts("请输入姓名");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selSexId) {
            toastOpts = getFailToastOpts("请选择性别");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.staffBirthday) {
            toastOpts = getFailToastOpts("请选择出生年月");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.nationality) {
            toastOpts = getFailToastOpts("请输入民族");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.height) {
            toastOpts = getFailToastOpts("请输入身高");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.politicsStatus) {
            toastOpts = getFailToastOpts("请选择政治面貌");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.professionalName) {
        //     toastOpts = getFailToastOpts("请输入专业");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.gradePoint) {
        //     toastOpts = getFailToastOpts("请输入绩点");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.englishLevelName) {
            toastOpts = getFailToastOpts("请选择英语能力");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.graduateInstitutions) {
            toastOpts = getFailToastOpts("请输入毕业院校");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.education) {
        //     toastOpts = getFailToastOpts("请输入学历");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.nativePlace) {
            toastOpts = getFailToastOpts("请输入籍贯");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.address) {
            toastOpts = getFailToastOpts("请输入住址");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.staffTel) {
            toastOpts = getFailToastOpts("请输入联系电话");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.email) {
            toastOpts = getFailToastOpts("请输入电子邮箱");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.electronicPhotos) {
        //     toastOpts = getFailToastOpts("请上传电子照片");
        //     WToast.show(toastOpts)
        //     return;
        // }

        let url= "/biz/cr/staff/modify";
        
        let requestParams={
            "staffId":constants.loginUser.staffId,
            "gradePoint":this.state.gradePoint*100,
            "politicsStatus":this.state.politicsStatus,
            "englishLevel":this.state.englishLevelCode,
            "staffSex": this.state.sexDataSource[this.state.selSexId-1].sexName,
            "staffBirthday":this.state.staffBirthday,
            "nationality":this.state.nationality,
            "height":this.state.height,
            "nativePlace":this.state.nativePlace,
            "address":this.state.address,
            "email":this.state.email,
            "electronicPhotos":this.state.userPhoto,
            // "crStaffExtDTOList": _spExtDTOList,
            "userId":constants.loginUser.userId,
            "staffTel":this.state.staffTel,
            "graduateInstitutions":this.state.graduateInstitutions,
            
        };
        console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.saveMyInterViewCallBack);
    }

    saveMyInterViewCallBack=(response) =>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('更新完成')
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
        }
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    //sex列表展示
    renderSexRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selSexId:item.sexId,
                        staffSexType:item.sexType,
                        selSexName:item.selSexName,
                    })
                }}>
                <View key={item.sexId} style={[item.sexId===this.state.selSexId ? styles.selectedBlockItemViewStyle1 : CommonStyle.blockItemViewStyle,{marginTop:15,marginLeft:0}] }>
                    <Text style={item.sexId===this.state.selSexId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.sexType}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CrHeadScreen title='基本信息'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle,{backgroundColor:'#FFF',borderTopColor:'#EEE',borderTopWidth:1,height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <View style={[styles.inputRowStyle, { height: 130 ,alignItems:'center',justifyContent:'center',marginTop:20}]}>
                        <TouchableOpacity 
                            style = {{height:80,width:80,borderRadius:50,alignItems:'center'}}
                            onPress={() => {
                                uploadImageLibrary(this.state.userPhotoUrl, "user_header", (imageUploadResponse) => {
                                    console.log("========imageUploadResponse", imageUploadResponse)
                                    if (imageUploadResponse.code === 200) {
                                        WToast.show({ data: "成功上传" });
                                        let { compressFile } = imageUploadResponse.data
                                        this.setState({
                                            userPhotoUrl: constants.image_addr + '/' + compressFile,
                                            userPhoto:compressFile
                                        })
                                    }
                                    else {
                                        WToast.show({ data: imageUploadResponse.message });
                                    }
                                });

                        }}>
                                {
                                    this.state.userPhotoUrl ?
                                    <Image source={{ uri: this.state.userPhotoUrl }} style={{ height: 80, width:80,borderRadius:50}} />
                                    :
                                    <Image source ={require('../../assets/icon/iconfont/headPortrait.png')} style ={{width:80,height:80,borderRadius:50}}></Image>
                                }
                                <View style={[ ]}>
                                    <Text style={[styles.leftLabNameTextStyle,{fontSize:15,color:'#333333',marginTop:8}]}>编辑头像</Text>
                                </View>
                        </TouchableOpacity> 
                    </View>
                    
                    <View style={[styles.titleViewStyle]}>
                        <Text style={[styles.titleTextStyle]}>姓名</Text>
                        <View style={[{height:50},{backgroundColor:'#FFFFFF',width:screenWidth - 20,marginTop:2}]}>
                            <TextInput 
                                editable={false}
                                style={styles.inputRightText}
                                placeholder={'请输入姓名'}
                                placeholderTextColor={'#000000D9'}
                                onChangeText={(text) => this.setState({staffName:text})}                    
                            >
                                {this.state.staffName}
                            </TextInput>   
                        </View>  
                    </View>
                                  
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>性别</Text>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.sexDataSource && this.state.sexDataSource.length > 0) 
                            ? 
                            this.state.sexDataSource.map((item, index)=>{
                                return this.renderSexRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    </View>
                       
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>出生年月</Text>
                        <TouchableOpacity onPress={()=>this.openBirthdayTime()}>
                        {/* <View style={CommonStyle.inputTextStyleTextStyle}>
                            <Text>{this.state.schedulingProductionTime}</Text>
                        </View> */}
                        <View style={styles.inputTextStyle1}>
                            <Text style={{color:'#000000D9', fontSize:15}}>
                                {!this.state.staffBirthday ? "请选择出生年月" : this.state.staffBirthday}
                            </Text>
                        </View>
                    </TouchableOpacity> 
                    </View>
                          
                        
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>民族</Text>
                        <View style={[]}>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入民族'}
                            onChangeText={(text) => this.setState({nationality:text})}       
                            placeholderTextColor={'#000'}             
                        >
                            {this.state.nationality}
                        </TextInput>
                    </View>
                    </View>
                    
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>身高(cm)</Text>
                        <View style={[]}>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入身高'}
                            onChangeText={(text) => this.setState({height:text})}
                            placeholderTextColor={'#000'}                       
                        >
                            {this.state.height}
                        </TextInput>
                    </View>
                    </View>
                    
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>政治面貌</Text>
                        <TouchableOpacity onPress={()=>this.openPoliticalStatus()}>
                        <View style={styles.inputTextStyle1}>
                            <Text style={{color:'#000000D9', fontSize:15}}>
                                {!this.state.politicsStatus ? "请选择政治面貌" : this.state.politicsStatus}
                            </Text>
                        </View>
                    </TouchableOpacity>
                    </View>

                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>专业</Text>
                        <View style={[]}>
                        <TextInput 
                            editable={false}
                            style={styles.inputRightText}
                            placeholder={'请输入专业'}
                            onChangeText={(text) => this.setState({professionalName:text})}    
                            placeholderTextColor={'#000'}                   
                        >
                            {this.state.professionalName}
                        </TextInput>
                    </View>
                    </View>
                     
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>专业绩点</Text>
                        <View style={[]}>
                        <TextInput
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入专业绩点'}
                            onChangeText={(text) => this.setState({gradePoint:text})}
                            placeholderTextColor={'#000'}   
                        >
                            {this.state.gradePoint}
                        </TextInput>     
                    </View>        
                    </View>      
                    
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>实践绩点</Text>
                        <View style={[]}>
                        <TextInput
                            editable={false}
                            keyboardType='numeric' 
                            style={styles.inputRightText}
                            placeholder={'请输入实践绩点'}
                            onChangeText={(text) => this.setState({comprehensiveAbility:text})}
                            placeholderTextColor={'#000'}   
                        >
                            {this.state.comprehensiveAbility}
                        </TextInput>
                    </View>
                    </View>
                                   
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>综合绩点</Text>
                        <View style={[]}>
                        <TextInput
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入综合绩点'}
                            onChangeText={(text) => this.setState({comprehensivePoint:text})}
                            placeholderTextColor={'#000'}   
                        >
                            {this.state.comprehensivePoint}
                        </TextInput>
                    </View>
                    </View>
                                         
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>英语能力</Text>
                        <TouchableOpacity onPress={()=>this.openEnglishLevel()}>
                        <View style={styles.inputTextStyleTextStyle}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.englishLevelName ? "请选择英语能力" : this.state.englishLevelName}
                            </Text>
                        </View>
                    </TouchableOpacity>   
                    </View>
                   
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>毕业院校</Text>
                        <View style={[]}>
                        <TextInput
                            editable={false} 
                            style={styles.inputRightText}
                            placeholder={'请输入毕业院校'}
                            onChangeText={(text) => this.setState({graduateInstitutions:text})}   
                            placeholderTextColor={'#000'}                    
                        >
                            {this.state.graduateInstitutions}
                        </TextInput>
                    </View>
                    </View> 
                    
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>学历</Text>
                        <View style={[]}>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入学历'}
                            onChangeText={(text) => this.setState({education:text})} 
                            placeholderTextColor={'#000'}                      
                        >
                            {this.state.education}
                        </TextInput>
                    </View>
                    </View>
                      
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>籍贯</Text>
                        <View style={[]}>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入籍贯'}
                            onChangeText={(text) => this.setState({nativePlace:text})}   
                            placeholderTextColor={'#000'}                    
                        >
                            {this.state.nativePlace}
                        </TextInput>
                    </View>
                    </View>
                    
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>住址</Text>
                        <View style={[]}>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入住址'}
                            onChangeText={(text) => this.setState({address:text})} 
                            placeholderTextColor={'#000'}                      
                        >
                            {this.state.address}
                        </TextInput>
                    </View>
                    </View>
                    
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>联系电话</Text>
                        <View style={[]}>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入联系电话'}
                            onChangeText={(text) => this.setState({staffTel:text})}   
                            placeholderTextColor={'#000'}                    
                        >
                            {this.state.staffTel}
                        </TextInput>
                    </View>
                    </View>
                    
                    <View style={[styles.titleViewStyle,{marginBottom:65}]}>
                        <Text style={[styles.titleTextStyle]}>电子邮箱</Text>
                        <View style={[]}>
                            <TextInput 
                                style={styles.inputRightText}
                                placeholder={'请输入电子邮箱'}
                                onChangeText={(text) => this.setState({email:text})} 
                                placeholderTextColor={'#000'}                      
                            >
                                {this.state.email}
                            </TextInput>
                        </View>
                    </View>
                    
                    <BottomScrollSelect 
                        ref={'SelectBirthday'} 
                        callBackMonthValue={this.callBackSelectBirthdayValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectPoliticalStatus'} 
                        callBackPoliticalStatusValue={this.callBackSelectPoliticalStatusValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectEnglishLevel'} 
                        callBackEnglishLevelValue={this.callBackSelectEnglishLevelValue.bind(this)}
                    />

                </ScrollView>

                <View style={CommonStyle.btnRowStyle,{display:'flex',justifyContent:'center',alignItems:'center',position:'absolute',bottom:0,zIndex:100,left:40}}>
                    <TouchableOpacity onPress={this.saveMyInterView.bind(this)}>
                        <View style={[styles.btnRowRightSaveBtnView1, {width:screenWidth - 80,height:45,borderRadius:40,opacity:0.6 }]}>
                            {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                </View>

            </View>
        )
    }
}
const styles = StyleSheet.create({

    titleTextStyle: {
        fontSize: 14, 
        color: '#00000073' ,
        marginTop:5,
        marginBottom:10,
    },
    titleViewStyle: {
        flexDirection: 'column',
        marginLeft: 20,
        marginTop: 15,
        marginBottom: 5,
        justifyContent:'center',
        width:screenWidth - 40,
    },
    
    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        marginTop: 10,
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight:30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - 40,
        borderColor: '#EEE',
        borderBottomWidth: 1,
        color: '#000000D9',
        fontSize: 15,
    },
    inputTextStyleTextStyle: {
        width: screenWidth - 40,
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    },
    inputTextStyleTextStyleAutoHeight: {
        width: screenWidth - 40,
        borderRadius: 0,
        borderColor: 'red',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        justifyContent: 'center'
    },
    btnRowRightSaveBtnView1:{
        backgroundColor:'#3366FF',
        alignItems:'center',
        // alignContent:'center',
        justifyContent:'center',
        borderRadius:30,
        flexDirection:'row',
        width:130,height:40,
        marginRight:35,
        marginTop:15
    },
    selectedBlockItemViewStyle1:{
        margin:5,
        paddingTop:5,paddingBottom:5,
        paddingLeft:10, paddingRight:10,
        // height:35,
        justifyContent:'center',
        borderRadius:2,
        backgroundColor:"#02A7F0"
    },
    // 假装是输入框的Text样式
    inputTextStyle1:{
        width:screenWidth - 40,
        borderRadius:5,
        borderBottomColor:'#EEEEEE',
        borderBottomWidth:1,
        color:'#A0A0A0',
        fontSize:15,
        height:45,
        justifyContent:'center'
    },

});
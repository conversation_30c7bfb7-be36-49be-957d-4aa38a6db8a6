<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.sp_mobile_front_jizhixueshe">

    <!--联网权限-->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!--相册、储存权限-->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <!--相册权限-->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- 推送通知 -->
    <!-- <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> -->

    <queries>
        <package android:name="com.tencent.mm" />   <!--指定微信包名-->
    </queries>

    <application
      android:name=".MainApplication"
      android:usesCleartextTraffic="true"
      android:label="@string/app_name"
      android:icon="@mipmap/jzxs_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      tools:replace="android:allowBackup"
      android:theme="@style/AppTheme">

      <!-- 请填写你自己的- appKey -->
      <meta-data android:name="com.alibaba.app.appkey" android:value="333726395" />
      <!-- 请填写你自己的appSecret -->
      <meta-data android:name="com.alibaba.app.appsecret" android:value="832c40f6e30e49a289bf4fb40426b2fb" />

      <!-- 华为 -->
      <meta-data android:name="com.huawei.hms.client.appid" android:value="appid=103530847" />
      
      <!-- vivo -->
      <meta-data android:name="com.vivo.push.api_key" android:value="e0b855e6b68d2195691cd8d8a0af1e21" />
      <meta-data android:name="com.vivo.push.app_id" android:value="105498427" />
      <!--Vivo Push需要配置的service、activity--> 
      <!-- <service android:name="com.vivo.push.sdk.service.CommandClientService" android:exported="true"/> -->

      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:screenOrientation="portrait"
        android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
      </activity>
      <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />

      <receiver
            android:name=".push.MyMessageReceiver"
            android:exported="false">
            <!-- 为保证receiver安全，建议设置不可导出，如需对其他应用开放可通过android：permission进行限制 -->
            <intent-filter>
                <action android:name="com.alibaba.push2.action.NOTIFICATION_OPENED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.alibaba.push2.action.NOTIFICATION_REMOVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.alibaba.sdk.android.push.RECEIVE" />
            </intent-filter>
        </receiver>
    </application>

</manifest>

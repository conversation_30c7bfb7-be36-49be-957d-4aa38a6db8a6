import React, { Component } from 'react';
import {
    TouchableOpacity, StyleSheet, TextInput, View, Text, Dimensions, 
    Alert, Modal, ScrollView, KeyboardAvoidingView, Image
} from 'react-native';
import { ifIphoneXHeaderHeight } from '../utils/ScreenUtil';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../component/CommonHeadScreen';
import EmptyRowViewComponent from '../component/EmptyRowViewComponent';
import CountdownUtil from '../utils/CountdownUtil';
var CommonStyle = require('../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class Register extends Component {
    constructor(props) {
        super(props);
        this.state = {
            userCode: "",
            userPwd: '',
            confirmUserPwd: '',
            userName: "",
            userNbr: "",
            userEmail: "",
            tenantAbbreviation: "智慧工厂",
            tenantLogo: "http://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/jzgk_logo.jpeg",
            tenantLoginBackground: "#303F58",

            selTenantId: 0,
            selTenantName: null,
            tenantsDataSource: [],
            modal: false,
            searchKeyWord: null,
            timerTitle: '获取验证码',
            isSentVerify: true,
        }
        // 上次点击登录按钮时间
        this.lastClickTime = 0
    }
    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        this.loadTenantList();
    }


    loadTenantList = () => {
        let loadUrl = "/biz/tenant/register_show_tenant_list";
        let loadRequest = {
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadTenantList);
    }

    // 订单回调加载
    callBackLoadTenantList = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                tenantsDataSource: response.data,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View />
        )
    }

    renderTenantItem = (item) => {
        return (
            <TouchableOpacity key={item.tenantId} onPress={() => {

                this.setState({
                    selTenantId: item.tenantId,
                    selTenantName: item.tenantName,
                })
            }}>
                <View style={item.tenantId === this.state.selTenantId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.tenantId === this.state.selTenantId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.tenantName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 注册
    onRegister = () => {
        let toastOpts;
        if (!this.state.userCode) {
            toastOpts = getFailToastOpts("请输入登录账号");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.userPwd) {
            toastOpts = getFailToastOpts("请输入新密码");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.confirmUserPwd) {
            toastOpts = getFailToastOpts("请输入确认新密码");
            WToast.show(toastOpts)
            return;
        }

        if (this.state.confirmUserPwd != this.state.userPwd) {
            toastOpts = getFailToastOpts("确认密码和新密码不一致，请重新输入");
            WToast.show(toastOpts)
            return;
        }

        if (!this.state.selTenantId) {
            toastOpts = getFailToastOpts("请选择租户");
            WToast.show(toastOpts)
            return;
        }

        if (!this.state.userName) {
            toastOpts = getFailToastOpts("请输入姓名");
            WToast.show(toastOpts)
            return;
        }

        if (!this.state.confirmUserPwd) {
            toastOpts = getFailToastOpts("请输入电话");
            WToast.show(toastOpts)
            return;
        }

        if (!this.state.verificationCode) {
            toastOpts = getFailToastOpts("请输入验证码");
            WToast.show(toastOpts)
            return;
        }

        Alert.alert('确认', '您确定要提交注册吗？', [
            {
                text: "取消", onPress: () => {
                    WToast.show({ data: '点击了取消' });
                    // this在这里可用，传到方法里还有问题
                    // this.props.navigation.goBack();
                }
            },
            {
                text: "确定", onPress: () => {
                    WToast.show({ data: '点击了确定' });
                    let url = "/biz/user/register";
                    let requestParams = {
                        'userCode': this.state.userCode,
                        'userPwd': this.state.userPwd,
                        'userName': this.state.userName,
                        'userNbr': this.state.userNbr,
                        'userEmail': this.state.userEmail,
                        'tenantId': this.state.selTenantId,
                        "verificationCode": this.state.verificationCode,
                    };
                    httpPost(url, requestParams, this.callBackRegister);
                }
            }
        ]);
    }

    // 订单回调加载
    callBackRegister = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                tenantsDataSource: response.data,
            })
            Alert.alert('确认', '注册成功，跳转到登录页面', [
                {
                    text: "确定", onPress: () => {
                        this.props.navigation.navigate("LoginView");
                    }
                }
            ]);
        }
        else if (response.code == 401) {
            let toastOpts = getFailToastOpts(response.message);
            WToast.show(toastOpts)
            this.props.navigation.navigate("LoginView");
        }
        else {
            let toastOpts = getFailToastOpts(response.message);
            WToast.show(toastOpts)
        }
    }

    render() {
        return (
            <KeyboardAvoidingView style={CommonStyle.contentViewStyle} behavior="padding">
                <CommonHeadScreen title='注册'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                登录账号
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 30) }]}
                            placeholder={'登录账号，建议使用手机号'}
                            onChangeText={(text) => this.setState({ userCode: text })}
                        >
                            {this.state.userCode}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                密码
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 30) }]}
                            placeholder={'请输入密码'}
                            secureTextEntry={true}
                            onChangeText={(text) => this.setState({ userPwd: text })}
                        >
                            {this.state.userPwd}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                确认密码
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 30) }]}
                            placeholder={'请输入确认密码'}
                            secureTextEntry={true}
                            onChangeText={(text) => this.setState({ confirmUserPwd: text })}
                        >
                            {this.state.confirmUserPwd}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                租户
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                modal: true,
                            })

                            if (!this.state.selTenantId && this.state.tenantsDataSource && this.state.tenantsDataSource.length > 0) {
                                this.setState({
                                    selTenantId: this.state.tenantsDataSource[0].tenantId,
                                    selTenantName: this.state.tenantsDataSource[0].tenantName,
                                })
                            }
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: 'rgba(178,178,178,0.5)', padding: 10, margin: 5 }]}>
                                <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>

                                    {this.state.selTenantId && this.state.selTenantName ? (this.state.selTenantName) : "选择租户"}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                姓名
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 30) }]}
                            placeholder={'请输入姓名'}
                            onChangeText={(text) => this.setState({ userName: text })}
                        >
                            {this.state.userName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                电话
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 30) }]}
                            placeholder={'请输入电话'}
                            onChangeText={(text) => this.setState({ userNbr: text })}
                        >
                            {this.state.userNbr}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                验证码
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            style={[styles.inputRightText, { width: (screenWidth - (leftLabWidth + 30)) / 2 }]}
                            placeholder={'验证码'}
                            onChangeText={(text) => this.setState({ verificationCode: text })}
                        >
                            {this.state.verificationCode}
                        </TextInput>
                        <TouchableOpacity onPress={() => {
                            if (!this.state.isSentVerify) {
                                return;
                            }
                            let toastOpts;
                            if (!this.state.selTenantId) {
                                toastOpts = getFailToastOpts("请选择租户");
                                WToast.show(toastOpts)
                                return;
                            }
                            if (!this.state.userNbr) {
                                toastOpts = getFailToastOpts("请输入电话");
                                WToast.show(toastOpts)
                                return;
                            }
                            if (!isMobileNumber(this.state.userNbr)) {
                                toastOpts = getFailToastOpts("电话格式错误");
                                WToast.show(toastOpts)
                                return;
                            }

                            this.setState({
                                isSentVerify: false
                            });
                            let requestUrl = "/biz/user/send_verification_sms_by_module_code";
                            let requestParams = {
                                "accNbr": this.state.userNbr,
                                "moduleCode": "R",
                                "tenantId": this.state.selTenantId,
                            };
                            httpPost(requestUrl, requestParams, (response) => {
                                if (response.code == 200 && response.data && response.data === true) {
                                    WToast.show({ data: '验证码已发送' });

                                    // 倒计时时间
                                    let countdownDate = new Date(new Date().getTime() + 60 * 1000)
                                    // 点击之后验证码不能发送网络请求
                                    this.setState({
                                        isSentVerify: false
                                    });
                                    CountdownUtil.settimer(countdownDate, (time) => {
                                        this.setState({
                                            timerTitle: time.sec > 0 ? time.sec + 's' : '重新获取'
                                        }, () => {
                                            if (this.state.timerTitle == "重新获取") {
                                                this.setState({
                                                    isSentVerify: true
                                                })
                                            }
                                        })
                                    })
                                }
                                else if (response.code == 200 && response.data && response.data === false) {
                                    WToast.show({ data: '验证码发送失败' });
                                    this.setState({
                                        isSentVerify: true
                                    });
                                }
                                else if (response.code != 200 && response.message) {
                                    WToast.show({ data: response.message });
                                    this.setState({
                                        isSentVerify: true
                                    });
                                }
                                else if (response.message) {
                                    WToast.show({ data: response.message });
                                    this.setState({
                                        isSentVerify: true
                                    });
                                }
                            });

                        }}>
                            <View style={[styles.loginBtnStyle, { opacity: this.state.isSentVerify ? 1 : 0.6, width: (screenWidth - (leftLabWidth + 30)) / 2 - 5 }]}>
                                <Text style={{ color: 'white', fontSize: 16, }}>{this.state.timerTitle}</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                邮箱
                            </Text>
                        </View>
                        <TextInput
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 30) }]}
                            placeholder={'请输入邮箱'}
                            onChangeText={(text) => this.setState({ userEmail: text })}
                        >
                            {this.state.userEmail}
                        </TextInput>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadTenantList();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state.tenantsDataSource && this.state.tenantsDataSource.length > 0)
                                                ?
                                                this.state.tenantsDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderTenantItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selTenantId) {
                                            let toastOpts = getFailToastOpts("您还没有选择租户");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    <View style={{ padding: 10 }}>
                        <TouchableOpacity
                            onPress={this.onRegister.bind(this)}>
                            <View style={styles.loginBtnStyle}>
                                <Text style={{ color: 'white', fontSize: 20, fontWeight: 'bold' }}>注  册</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        );
    }
}

const styles = StyleSheet.create({
    // contentViewStyle:{
    //     backgroundColor:'#FFFFFF',
    //     height:screenHeight - 140
    // },
    container: {
        width: screenWidth,
        height: screenHeight - ifIphoneXHeaderHeight(),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    loginBtnStyle: {
        width: screenWidth * 0.76,
        height: 45,
        backgroundColor: '#CE3C27',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 8,
        alignSelf: 'center'
    },
})
import React,{ Component } from 'react';
import {View, Text, StyleSheet,Dimensions,Image} from 'react-native';

var CommonStyle = require('../assets/css/CommonStyle');
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
class EmptyDepartmentStaffComponent extends Component {

    render(){
        return(
            <View style={{width:screenWidth,alignItems: 'center', paddingTop:150,height:screenHeight - 220,backgroundColor:'rgba(242, 245, 252, 1)'}}>
                <Image  style={{width:122, height:129}} source={require('../assets/image/emptyDepartment.png')}></Image>
                <Text style={styles.contentTextStyle}>人员为空</Text>
            </View>
        )
    }
}
const styles = StyleSheet.create({

    contentTextStyle:{
        width:70,
        height:22,
        fontSize:16,
        fontWeight:'500',
        color:'rgba(0,10,32,0.15)',
    }
})
module.exports = EmptyDepartmentStaffComponent;
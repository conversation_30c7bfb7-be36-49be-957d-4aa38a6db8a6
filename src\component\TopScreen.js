import React, { Component } from 'react';
import {
    AppRegistry,
    Dimensions,
    StyleSheet,
    View,
    Text,
    Button,
} from 'react-native';
let ScreenWidth = Dimensions.get('window').width;
let ScreenHeight = Dimensions.get('window').height;
class TopScreen extends React.Component{
    constructor(props) {
        super(props);
        this.state = {  
            title:props.title,
            do:props.do,
            doName:''
        };
    }

    componentDidMount(){
        console.log('componentDidMount');
        if(this.state.do === 'simi_finished_check') {
            this.setState({
                doName:'点验' 
            })
        }
        else if (this.state.do === 'encastage') {
            this.setState({
                doName:'装窑'
            })
        }
        else if (this.state.do === 'warm_record_add') {
            this.setState({
                doName:'新增'
            })
        }
        else if (this.state.do === 'check_out_add') {
            this.setState({
                doName:'新增'
            })
        }
        else {
            this.setState({
                doName:'登出' 
            })
        }
    }


    render(){
        return(
            <View style={styles.secHeader}>
                <View style={styles.secTitle}>
                    <Text style={styles.secTitleText}>{this.state.title}</Text>
                </View>
                <View style={styles.secLogout}>
                  <Text style={styles.secLogoutText}>{this.state.doName}</Text>
                </View>
            </View>
        );
    }
}

const styles=StyleSheet.create({
   secHeader:{
      backgroundColor:"#CB4139",
      borderBottomWidth:1,
      borderBottomColor:"#ddd",
   },
   secTitle:{
      height:50,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
   },
   secTitleText:{
      fontSize:18,
      color:'#FFFFFF',
      fontWeight:'600',
   },
   secLogout:{
      height:50,
      width:ScreenWidth-10,
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
      position: 'absolute',
   },
   secLogoutText:{
       color:'#FFFFFF'
   }

});

module.exports=TopScreen;
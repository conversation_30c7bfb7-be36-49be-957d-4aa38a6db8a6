import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class EnterpriseInvitedApply extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            searchKeyWord:"",
            topBlockLayoutHeight:0
        }
    }

    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        this.loadInvitedApplyList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/collection/position/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWordCollectionPosition":this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "applyPosition":"Y"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/collection/position/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWordCollectionPosition":this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "applyPosition":"Y"
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/collection/position/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWordCollectionPosition":this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "applyPosition":"Y"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadInvitedApplyList();
    }


    loadInvitedApplyList=()=>{
        let url= "/biz/collection/position/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "searchKeyWordCollectionPosition":this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "applyPosition":"Y"
        };
        httpPost(url, loadRequest, this.loadInvitedApplyListCallBack);
    }

    loadInvitedApplyListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    acceptItem=(item)=>{
        if(item.positionAttitude == 'N'){
            return;
        }else{
            let url= "/biz/collection/position/modify";
            let requestParams={
                collectionId:item.collectionId,
                positionAttitude:item.positionAttitude == 'Y' ? "I":"Y",
            };
            httpPost(url, requestParams, this.acceptCallBack);
        }
    }

    refuseItem=(item)=>{
        if(item.positionAttitude == 'Y'){
            return;
        }else{
            let url= "/biz/collection/position/modify";
            let requestParams={
                collectionId:item.collectionId,
                positionAttitude:item.positionAttitude == 'N' ? "I":"N",
            };
            httpPost(url, requestParams, this.refuseCallBack);
        }
    }

    // 保存回调函数
    acceptCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({ data: response.data.positionAttitude == 'Y'?"已接受该面试申请":"已取消接受" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 保存回调函数
    refuseCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({ data: response.data.positionAttitude == 'N'?"已拒绝该面试申请":"已取消拒绝" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }

    renderRow = (item, index) => {
        return (
            <View key={item.dailyId} style={{ borderColor: '#F2F5FC', borderBottomWidth: 7, borderTopWidth: 8 }}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>申请岗位：{item.positionName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>姓名：{item.staffName}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>性别：{item.staffSex == 'M' ? "男" : "女"}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>联系电话：{item.staffTel ? item.staffTel : "无"}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>班级：{item.className}</Text>
                </View> */}
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>专业：{item.professionalName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>综合绩点：{item.comprehensivePoint?(item.comprehensivePoint/100).toFixed(2):"信息尚未完善"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,{fontWeight:'bold'}]}>个人荣誉</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.personalHonor?item.personalHonor:"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,{fontWeight:'bold'}]}>学院评价</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.collegeEvaluation?item.collegeEvaluation:"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                {
                    item.positionAttitude == 'Y' ?
                    <Text style={{fontSize:14,color:'#FF8C28'}}>已接受该面试申请</Text>
                    :
                    (
                        item.positionAttitude == 'N' ?
                        <Text style={{fontSize:14,color:'#FD4246'}}>已拒绝该面试申请</Text>
                        :
                        null
                    )
                }
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>

                    {/* 20231221  原写法不变，样式暂定此版本，待调整 */}
                    <TouchableOpacity onPress={()=>{this.acceptItem(item)}}>
                        {
                            item.positionAttitude == 'Y' ?
                            <View style={[{
                                width: 65,
                                height: 28,
                                flexDirection: "row",
                                justifyContent: 'center',
                                alignItems: 'center',
                                margin: 10,
                                marginRight: 0,
                                borderColor: '#FF8C28',
                                borderWidth: 0.85,
                                borderRadius: 6
                            }]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                <Text style={[{ color: '#FF8C28', fontSize: 14, lineHeight: 20 }]}>取消</Text>
                            </View>  
                            :
                            (
                                item.positionAttitude == 'N' ?
                                <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle,{width: 65,borderColor:'rgba(145, 147, 152, 0.5)',flexDirection:"row"}]}>
                                    {/* <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/closeGrey.png')}></Image> */}
                                    <Text style={[{ color: 'rgba(145, 147, 152, 0.5)', fontSize: 14, lineHeight: 20 }]}>接受</Text>
                                </View>
                                :
                                <View style={[{
                                    width: 65,
                                    height: 28,
                                    flexDirection: "row",
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    margin: 10,
                                    marginRight: 0,
                                    borderColor: '#FF8C28',
                                    borderWidth: 0.85,
                                    borderRadius: 6
                                }]}>
                                    {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                    <Text style={[{ color: '#FF8C28', fontSize: 14, lineHeight: 20 }]}>{item.positionAttitude == 'Y' ? "取消" : "接受" }</Text>
                                </View>    
    
                            )
                        }
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{this.refuseItem(item)}}>
                        {
                            item.positionAttitude == 'N' ?
                            <View style={[{
                                width: 65,
                                height: 28,
                                flexDirection: "row",
                                justifyContent: 'center',
                                alignItems: 'center',
                                margin: 10,
                                marginRight: 0,
                                borderColor: '#FD4246',
                                borderWidth: 0.85,
                                borderRadius: 6
                            }]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                <Text style={[{ color: '#FD4246', fontSize: 14, lineHeight: 20 }]}>取消</Text>
                            </View>
                            :
                            (
                                item.positionAttitude == 'Y' ?
                                <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle,{width: 65,borderColor:'rgba(145, 147, 152, 0.5)',flexDirection:"row"}]}>
                                    {/* <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/closeGrey.png')}></Image> */}
                                    <Text style={[{ color: 'rgba(145, 147, 152, 0.5)', fontSize: 14, lineHeight: 20 }]}>拒绝</Text>
                                </View>
    
                                :
                                <View style={[{
                                    width: 65,
                                    height: 28,
                                    flexDirection: "row",
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    margin: 10,
                                    marginRight: 0,
                                    borderColor: '#FD4246',
                                    borderWidth: 0.85,
                                    borderRadius: 6
                                }]}>
                                    {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                    <Text style={[{ color: '#FD4246', fontSize: 14, lineHeight: 20 }]}>拒绝</Text>
                                </View>    
    
                            )
                        }
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{this.props.navigation.navigate("StudentMyInterViewPreview", 
                            {
                                // 传递回调函数
                                staffId: item.staffId,
                                refresh: this.callBackFunction,
                                userPhotoUrl:constants.image_addr + '/' + item.electronicPhotos,
                                
                            })}}>
                        <View style={[{
                            width: 80,
                            height: 28,
                            flexDirection: "row",
                            justifyContent: 'center',
                            alignItems: 'center',
                            margin: 10,
                            marginRight: 10,
                            borderColor: 'rgba(27, 188, 130, 1)',
                            borderWidth: 0.85,
                            borderRadius: 6
                        }]}>
                            <Image style={{ width: 24, height: 24, marginRight: 0.5, marginLeft: 3 }} source={require('../../assets/icon/iconfont/detailGreen.png')}></Image>
                            <Text style={[{ color: 'rgba(27, 188, 130, 1)', fontSize: 14, lineHeight: 20 }]}>详情</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='面试申请'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle, { marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{}}>
                        <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                <Image  style={{width:18, height:18}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                </View>
                                <TextInput
                                    style={[styles.searchInputText, {}]}
                                    returnKeyType="search"
                                    returnKeyLabel="搜索"
                                    onSubmitEditing={e => {
                                        this.searchByKeyWord();
                                    }}
                                    placeholder={'搜索姓名/班级/专业/岗位'}
                                    onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                    {this.state.searchKeyWord}
                                    </TextInput>
                            {/* <TouchableOpacity onPress={() => {
                            this.searchByKeyWord();
                                }}>
                            <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 70,backgroundColor:'#DEB887',borderColor:'#DEB887' }]}>
                                <Text style={[CommonStyle.itemBottomDeleteBtnTextStyle,{color:'#FFFFFF'}]}>查询</Text>
                            </View>
                        </TouchableOpacity> */}
                            </View>
                            {/* <View style={{ height: 5, backgroundColor: '#FFFFFF' }}></View> */}
                        </View>
                </View>
                <View style={[CommonStyle.contentViewStyle,{height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
        // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    inputRowStyle: {
        // justifyContent: "space-between",
        alignItems: 'center',
        width: screenWidth / 1.05,
        paddingLeft: 5,
        height: 34,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#F2F5FC",
        backgroundColor: '#F2F5FC',
        borderRadius: 15,
        margin: 0,
        marginTop: 0,
        marginLeft: 0,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 14,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle: {
        // marginTop: 10,
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});
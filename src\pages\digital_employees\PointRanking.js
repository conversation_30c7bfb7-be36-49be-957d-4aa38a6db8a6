import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Image,
    FlatList, RefreshControl,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class PointRanking extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            userPhoto:"",
            userName:"",
            topBlockLayoutHeight:0,
            showSearchItemBlock:false,
            departmentDataSource:null,
            selDepartmentId:null,
            selDepartmentName:null,
            showClassSearchItemBlock:false,
            classDataSource:null,
            selClassId:null,
            selClassName:null,
        }
    }
    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let loadTypeUrl= "/biz/department/list_for_tenant";
        let loadRequest={"qryAll_NoPower":"Y", "currentPage": 1, "pageSize": 1000};
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    departmentDataSource:response.data,
                })
            }
        });
        let loadClassTypeUrl= "/biz/college/class/grades/list";
        let loadClassRequest={"qryAll":"Y","tenantId":constants.loginUser.tenantId, "currentPage": 1, "pageSize": 1000};
        httpPost(loadClassTypeUrl, loadClassRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                var classData = response.data.dataList;
                classData.unshift({"classId":0,"className":"全部"})
                this.setState({
                    classDataSource:classData,
                })
            }
        });

        this.loadUserList();
    }

    loadUserList=()=>{
        let url= "/biz/point/record/userank";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "departmentId":this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId,
            "classId":this.state.selClassId == 0 ? null : this.state.selClassId
        };
        httpPost(url, loadRequest,this.loadUserListCallBack);
    }

    loadUserListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 回调函数
    callBackFunction = () => {
        let url= "/biz/point/record/userank";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId":this.state.selDepartmentId,
            "classId":this.state.selClassId == 0 ? null : this.state.selClassId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url= "/biz/point/record/userank";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId":this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId,
            "classId":this.state.selClassId == 0 ? null : this.state.selClassId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    // 显示搜索项目
    showSearchItemSelect(){
        if (this.state.selClassId != null && this.state.selClassId != 0){
            return;
         } 
        if (!this.state.departmentDataSource || this.state.departmentDataSource.length < 1) {
            WToast.show({data:"请先添加部门"});
            return
        }
        this.setState({
            showSearchItemBlock:true,
        })
    }
    // 显示搜索项目
    showClassSearchItemSelect(){
        if (this.state.selDepartmentId != null && this.state.selDepartmentId != 0){
           return;
        }
        if (!this.state.classDataSource || this.state.classDataSource.length < 1) {
            WToast.show({data:"请先添加班级"});
            return
        }
        this.setState({
            showClassSearchItemBlock:true,
        })
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

     // 上拉触底加载下一页
     _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadUserList();
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PointRecord",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Text style={CommonStyle.headRightText}>我的积分</Text>
            </TouchableOpacity>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    // 部门
    renderDepartmentRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                selDepartmentId:item.departmentId,
                selDepartmentName:item.departmentName,
                // selDepartmentStaffDataSource:item.departmentUserDTOList,
                // selStaffId:null,
                // selStaffName:null,
            }) }}>
                <View key={"department_" + item.departmentId} style={[item.departmentId === this.state.selDepartmentId ?
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                    CommonStyle.choseToSearchItemsViewSize
                ]}>
                    <Text style={[item.departmentId === this.state.selDepartmentId ?
                    CommonStyle.choseToSearchItemsSelectedTextStyle:CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.departmentName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 班级
    renderClasstRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                selClassId:item.classId,
                selClassName:item.className,
            }) }}>
                <View key={"department_" + item.classId} style={[item.classId===this.state.selClassId? 
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                    CommonStyle.choseToSearchItemsViewSize
                    ] }>
                    <Text style={[item.classId===this.state.selClassId? 
                    CommonStyle.choseToSearchItemsSelectedTextStyle:CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.className}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.kilnCarId} style={[CommonStyle.innerViewStyle]}>
                <View style={{flexDirection: 'row',alignItems:'center',marginLeft:20}}>
                    <View style={[styles.textContentStyle,{marginRight:0}]}>
                        <Text style={styles.titleTextStyle}>{item.pointRanking}</Text>
                    </View>
                    {
                        item.userPhoto?
                        <View style={styles.imgContentStyle}>
                        <Image source={{ uri: (constants.image_addr + '/' + item.userPhoto) }} style={{width:60,height:60, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                        </View>
                        :
                        <View style={styles.imgContentStyle}>
                        <View style={{height: 60, width:60,borderRadius:50,borderColor:'#AAAAAA',borderWidth:0.3,justifyContent:'center',alignItems:'center'}}>
                            <Text style={[styles.titleTextStyle,{fontSize:14,color:"#aaaaaa"}]}>无</Text>
                        </View>
                        </View>
                    }
                    <View style={[styles.textContentStyle,{marginLeft:25,}]}>
                        <Text style={[styles.titleTextStyle,{}]}>{item.userName}</Text>
                    </View>
                    <View style={styles.textContentStyle}>
                        <Text style={styles.titleTextStyle}>{item.pointTotalValue ? item.pointTotalValue :"0"}</Text>
                    </View>
                </View>
            </View>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='积分排行'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
            <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                
                <View style={{width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                    {/* ============部门========== */}
                    <TouchableOpacity onPress={()=>this.showSearchItemSelect()}>
                        {
                            this.state.showSearchItemBlock?
                            <View style={[CommonStyle.choseToSearchViewStyle]}>
                                <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                    {this.state.selDepartmentId && this.state.selDepartmentName ? (this.state.selDepartmentName) : "选择部门"}
                                </Text>
                                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/arrow-up.png')}></Image>
                            </View>
                            :
                            <View style={[CommonStyle.choseToSearchViewStyle]}>
                                <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                    {this.state.selDepartmentId && this.state.selDepartmentName ? (this.state.selDepartmentName) : "选择部门"}
                                </Text>
                                <Image style={{ width: 20, height: 20 }} source={require('../../assets/icon/iconfont/arrow_down.png')}></Image>
                            </View>
                        }
                    </TouchableOpacity>

                    {
                        this.state.classDataSource?
                        <TouchableOpacity onPress={() => this.showClassSearchItemSelect()}>
                        {
                            this.state.showClassSearchItemBlock?
                            <View style={[CommonStyle.choseToSearchViewStyle]}>
                                <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                    {this.state.selClassId && this.state.selClassName ? (this.state.selClassName) : "选择班级"}
                                </Text>
                                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/arrow-up.png')}></Image>
                            </View>
                            :
                            <View style={[CommonStyle.choseToSearchViewStyle]}>
                                <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                    {this.state.selClassId && this.state.selClassName ? (this.state.selClassName) : "选择班级"}
                                </Text>
                                <Image style={{ width: 20, height: 20 }} source={require('../../assets/icon/iconfont/arrow_down.png')}></Image>
                            </View>
                        }
                        </TouchableOpacity>
                        :
                        null
                    }
                </View>
            </View>
                
                <View>
                {
                    this.state.showSearchItemBlock ?
                    <View style={[CommonStyle.choseToSearchBigBoxViewStyle,{height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    
                    <View style={CommonStyle.heightLimited}>
                    <ScrollView>
                    
                    <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                        <View style={[{backgroundColor: 'rgba(255,255,255,1)'},CommonStyle.choseToSearchItemsViewSize]}>
                            <Text style={{ fontSize: 16, fontWeight: 'bold' }}>部门：</Text>
                        </View>
                        {
                            (this.state.departmentDataSource && this.state.departmentDataSource.length > 0)
                                ?
                                this.state.departmentDataSource.map((item, index) => {
                                    return this.renderDepartmentRow(item)
                                })
                                : null
                        }
                    </View>
                    </ScrollView> 
                    </View>
                    <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                            <TouchableOpacity onPress={() => { 
                                let loadUrl= "/biz/point/record/userank";
                                let loadRequest={
                                    "currentPage": 1,
                                    "pageSize": this.state.pageSize,
                                    "departmentId":this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId,
                                    "classId":null
                                };
                                console.log("选择的部门====="+this.state.selDepartmentId)
                                httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                this.setState({
                                    showSearchItemBlock:false,
                                    // selClassId:null
                                }) 
                            }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    :
                    null
                }                
                {
                    this.state.showClassSearchItemBlock?
                    <View style={[CommonStyle.choseToSearchBigBoxViewStyle,{height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    
                    <View style={CommonStyle.heightLimited}>
                    <ScrollView>
                    
                    <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                        <View style={[{backgroundColor: 'rgba(255,255,255,1)'},CommonStyle.choseToSearchItemsViewSize]}>
                            <Text style={{ fontSize: 16, fontWeight: 'bold' }}>班级：</Text>
                        </View>
                            {
                                (this.state.classDataSource && this.state.classDataSource.length > 0) 
                                ? 
                                this.state.classDataSource.map((item, index)=>{
                                    return this.renderClasstRow(item)
                                })
                                : null 
                            }
                        </View>
                        </ScrollView> 
                    </View>
                        <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showClassSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                            <TouchableOpacity onPress={() => { 
                                let loadUrl= "/biz/point/record/userank";
                                let loadRequest={
                                    "currentPage": 1,
                                    "pageSize": this.state.pageSize,
                                    "departmentId":null,
                                    "classId":this.state.selClassId == 0 ? null : this.state.selClassId
                                };
                                console.log("选择的部门====="+this.state.selClassId)
                                httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                this.setState({
                                    showClassSearchItemBlock:false,
                                }) 
                            }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                            </TouchableOpacity>
                        </View>
                    </View>    
                    :
                    null
                }

                <View style={{flexDirection:'row',marginLeft:15,backgroundColor:"rgba(242, 245, 252, 1)"}}>
                    <View style={{margin:10,width:screenWidth/5}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 18,fontWeight: 'bold'}]}>排名</Text>
                    </View>
                    <View style={{margin:10,width:screenWidth/5}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 18,fontWeight: 'bold'}]}>头像</Text>
                    </View>
                    <View style={{margin:10,width:screenWidth/5}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 18,fontWeight: 'bold'}]}>姓名</Text>
                    </View>
                    <View style={{margin:10,width:screenWidth/5}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 18,fontWeight: 'bold'}]}>总积分</Text>
                    </View>
                </View>       

                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                    />                       
                </View>
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },
    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle: {
        // marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 8,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent:'space-between',
        // justifyContent:'center',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
        alignItems:"center",
        
    },
    titleTextStyle: {
        fontSize: 16,
        alignItems:"center",
        // textAlign:"center"
    },
    imgContentStyle: {
        marginTop:8,
        marginBottom:8,
        marginLeft:0,
        marginRight:10,
        width:screenWidth/5
    },
    textContentStyle: {
        margin:10,
        
        width:screenWidth/5
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    photos:{
        width:50,
        height:50,
        borderRadius:50,
        borderWidth:0,
        // marginTop:80,
        // marginBottom:30
    },

});
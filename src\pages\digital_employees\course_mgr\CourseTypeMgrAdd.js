import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, TouchableOpacity, Dimensions, Image } from 'react-native';
import { WToast } from 'react-native-smart-tip'

import CommonHeadScreen from '../../../component/CommonHeadScreen';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import EmptyRowViewComponent from '../../../component/EmptyRowViewComponent';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';

export default class CourseTypeMgrAdd extends Component {
    constructor() {
        super()
        this.state = {
            operate: "",
            courseTypeId: "",
            courseTypeName: "",
            courseTypeDesc: "",
            courseTypeSort: 0,
            // courseTypeNameArrays: []
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { courseTypeId } = route.params;
            if (courseTypeId) {
                console.log("========Edit==courseTypeId:", courseTypeId);
                this.setState({
                    courseTypeId: courseTypeId,
                    operate: "编辑"
                })
                let loadTypeUrl = "/biz/course/type/get";
                let loadRequest = { 'courseTypeId': courseTypeId };
                httpPost(loadTypeUrl, loadRequest, this.loadCourseDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
            this.loadCourseTypeList();
        }
    }

    loadCourseTypeList = () => {
        let url = "/biz/course/type/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10000,
        };
        httpPost(url, loadRequest, this.loadCourseTypeListCallBack);
    }

    loadCourseTypeListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            let arr = [];
            data.forEach((element) => {
                arr.push(element.courseTypeName);
            });
            // console.log("@__courseTypeNameArrays__@", JSON.stringify(arr, null, 6));
            // this.setState({
            //     courseTypeNameArrays: arr
            // })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadCourseDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                courseTypeId: response.data.courseTypeId,
                courseTypeName: response.data.courseTypeName,
                courseTypeDesc: response.data.courseTypeDesc,
                courseTypeSort: response.data.courseTypeSort
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    saveCourse = () => {
        console.log("=======saveCourse");
        let toastOpts;
        if (!this.state.courseTypeName) {
            toastOpts = getFailToastOpts("请填写课程类型名称");
            WToast.show(toastOpts)
            return;
        }

        // if (this.state.courseTypeNameArrays) {
        //     console.log("1");
        //     for (let item of this.state.courseTypeNameArrays) {
        //         if (this.state.courseTypeName == item) {
        //             toastOpts = getFailToastOpts("“" + this.state.courseTypeName + "”" + "已被使用，请换个名称重试");
        //             WToast.show(toastOpts)
        //             return;
        //         }
        //     }
        // }
        console.log("===1===saveCourse");
        let url = "/biz/course/type/add";
        if (this.state.courseTypeId) {
            console.log("=========Edit===courseTypeId", this.state.courseTypeId)
            url = "/biz/course/type/modify";
        }

        let requestParams = {
            courseTypeId: this.state.courseTypeId,
            courseTypeName: this.state.courseTypeName,
            courseTypeDesc: this.state.courseTypeDesc,
            courseTypeSort: this.state.courseTypeSort
        };
        httpPost(url, requestParams, this.saveCourseCallBack);
    }

    saveCourseCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    render() {
        return (
            <View >
                <CommonHeadScreen title={this.state.operate + '课程类型'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />                
                <View style={{height:ifIphoneXContentViewDynamicHeight(45+10+10-2)}}>
                    <View style={{backgroundColor:"white",paddingBottom:10}}>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>类型名称</Text>
                        </View>
                        <TextInput 
                            maxLength={7}
                            style={[CommonStyle.inputTextStyleTextStyle, {borderWidth: 0,color: '#A0A0A0', fontSize: 15}]}
                            placeholder={'请填写课程类型'}
                            onChangeText={(text) => this.setState({ courseTypeName: text })}
                        >
                            {this.state.courseTypeName}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>排序</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[CommonStyle.inputTextStyleTextStyle, {borderWidth: 0,color: '#A0A0A0', fontSize: 15}]}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({ courseTypeSort: text })}
                        >
                            {this.state.courseTypeSort}
                        </TextInput>
                    </View>    
                    <View style={styles.inputLineViewStyle}/>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>  </Text>
                            <Text style={styles.leftLabNameTextStyle}>备注</Text>
                        </View>
                        <TextInput 
                            style={[CommonStyle.inputTextStyleTextStyle, {borderWidth: 0,color: '#A0A0A0', fontSize: 15}]}
                            placeholder={'请填写备注'}
                            onChangeText={(text) => this.setState({ courseTypeDesc: text })}
                        >
                            {this.state.courseTypeDesc}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    </View>
                </View>

                <View style={[CommonStyle.btnRowStyle, {width: screenWidth, margin: 0,backgroundColor:"white"}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginBottom:15,marginLeft: 20, width: (screenWidth - 56)/2}]} >
                            {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveCourse.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, {marginBottom:15,marginRight: 20, width: (screenWidth - 56)/2}]}>
                            {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>确认</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>

        )
    }
}

let styles = StyleSheet.create({
    contentViewStyle: {
        height: screenHeight - 90,
    },
    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 30),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },

    titleTextStyle: {
        fontSize: 16
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    inputLineViewStyle: {
        height:1,
        marginLeft: 13,
        marginRight: 13,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
})
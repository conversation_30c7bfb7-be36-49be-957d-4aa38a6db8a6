import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Image, Linking, Clipboard,
    FlatList, RefreshControl, Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../../utils/CameraRollUtils';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class MyReceiveList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,

            // askQuestionsToClaimUserId:"",
            selAskQuestionsStateCode: 'all',
            showSearchItemBlock: false,

            qryStartTime: null,
            selectedQryStartDate: [],
            compressFileList: [],
            urls: [],
            isShowImage: false,
            pictureIndex: 0
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        console.log("当前用户Id是" + constants.loginUser.userId)
        let askQuestionsState = [
            {
                stateCode: 'all',
                stateName: '全部',
            },
            {
                stateCode: '0BB',
                stateName: '解决中',
            },
            {
                stateCode: '0CC',
                stateName: '已解决',
            },
        ]
        this.setState({
            askQuestionsState: askQuestionsState,
        })
        const { route, navigation } = this.props;

        this.loadAskQuestionsList();
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/portal/ask/questions/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "askQuestionsToClaimUserId": constants.loginUser.userId,
            "askQuestionsState": this.state.selAskQuestionsStateCode === 'all' ? null : this.state.selAskQuestionsStateCode,
            "receive_list": "Y",
            // "qryStartTime":this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/portal/ask/questions/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "askQuestionsToClaimUserId": constants.loginUser.userId,
            "askQuestionsState": this.state.selAskQuestionsStateCode === 'all' ? null : this.state.selAskQuestionsStateCode,
            "receive_list": "Y",
            // "qryStartTime":this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { pictureDisplay: "N" }))
            })
            this.setState({
                dataSource: listNew,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadAskQuestionsList = (_qryStartTime) => {
        let url = "/biz/portal/ask/questions/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "askQuestionsToClaimUserId": constants.loginUser.userId,
            "askQuestionsState": this.state.selAskQuestionsStateCode === 'all' ? null : this.state.selAskQuestionsStateCode,
            "receive_list": "Y",
            // "qryStartTime": _qryStartTime ? _qryStartTime : this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this.loadAskQuestionsListCallBack);
    }

    loadAskQuestionsListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { pictureDisplay: "N" }))
            })
            this.setState({
                dataSource: listNew,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadAskQuestionsList();
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View />
        )
    }

    //取消认领
    claimAskQuestions = (quesItem, index) => {
        console.log("=======setPromotionPlan=quesItem", quesItem);
        let requestUrl = "/biz/portal/ask/questions/modify";
        let requestParams = {
            'askQuestionsId': quesItem.askQuestionsId,
            'askQuestionsState': quesItem.askQuestionsState === '0BB' ? '0AA' : '0BB',
            'compressFileList': quesItem.compressFileList
        };
        httpPost(requestUrl, requestParams, (response) => {
            if (response.code == 200) {
                // 更新页面上显示
                quesItem.askQuestionsState = (quesItem.askQuestionsState === '0BB' ? '0AA' : '0BB');
                let askQuestionsDataSource = this.state.dataSource;
                // JS 数组遍历
                askQuestionsDataSource.forEach((obj) => {
                    if (obj.askQuestionsId === quesItem.askQuestionsId) {
                        obj.askQuestionsState = quesItem.askQuestionsState;
                        obj.compressFileList = quesItem.compressFileList;
                        WToast.show({ data: (quesItem.askQuestionsState === '0BB' ? '认领' : '取消认领') + "完成" });
                    }
                })
                askQuestionsDataSource = askQuestionsDataSource.filter(item => item.askQuestionsState === "0BB" || item.askQuestionsState === "0CC")
                this.setState({
                    dataSource: askQuestionsDataSource
                })
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }
    closeAskQuestions = (quesItem, index) => {
        console.log("=======setPromotionPlan=quesItem", quesItem);
        let requestUrl = "/biz/portal/ask/questions/modify";
        let requestParams = {
            'askQuestionsId': quesItem.askQuestionsId,
            'askQuestionsState': quesItem.askQuestionsState === '0BB' ? '0CC' : '0BB',
            // 'askQuestionsToClaimUserId':""
        };
        httpPost(requestUrl, requestParams, (response) => {
            if (response.code == 200) {
                // 更新页面上显示
                quesItem.askQuestionsState = (quesItem.askQuestionsState === '0BB' ? '0CC' : '0BB');
                let askQuestionsDataSource = this.state.dataSource;
                // JS 数组遍历
                askQuestionsDataSource.forEach((obj) => {
                    if (obj.askQuestionsId === quesItem.askQuestionsId) {
                        obj.askQuestionsState = quesItem.askQuestionsState;
                        // obj.askQuestionsToClaimUserId = "";
                        WToast.show({ data: (quesItem.askQuestionsState === '0BB' ? '关闭' : '取消关闭') + "完成" });
                    }
                })
                this.callBackFunction();
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }

    renderRow = (item, index) => {
        return (
            <View key={item.askQuestionsId} style={[CommonStyle.innerViewStyle]}>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle, { marginRight: 50 }]}>标题：{item.askQuestionsTitle}</Text>
                    {
                        item.askQuestionsState !== '0AA' ?
                            <View style={{ position: 'absolute', right: 0, top: 0 }}>
                                {
                                    item.askQuestionsState === '0BB' ?
                                        <Text style={{ color: 'green' }}>解决中</Text>
                                        :
                                        <Text style={{ color: '#CB4139' }}>已解决</Text>
                                }
                            </View>
                            :
                            <View />
                    }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>内容：</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.askQuestionsContent}</Text>
                </View>
                {
                    item.compressFileList && item.compressFileList.length > 0 ?
                        (
                            <View>
                                {
                                    item.pictureDisplay === "N" ?
                                        <View style={[styles.titleViewStyle, { justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                                            <Text style={styles.titleTextStyle}>附件：</Text>
                                            <TouchableOpacity onPress={() => {
                                                var urls = [];
                                                if (item.compressFileList && item.compressFileList.length > 0) {
                                                    for (var i = 0; i < item.compressFileList.length; i++) {
                                                        var url = {
                                                            url: constants.image_addr + '/' + item.compressFileList[i].compressFile
                                                        }
                                                        urls = urls.concat(url)
                                                        console.log(url)
                                                    }
                                                }
                                                this.setState({
                                                    urls: urls
                                                })
                                                let list = this.state.dataSource;
                                                list.map((elem, index) => {
                                                    if (elem.askQuestionsId == item.askQuestionsId) {
                                                        elem.pictureDisplay = "Y"
                                                    }
                                                })
                                                this.setState({
                                                    dataSource: list
                                                })
                                                // console.log("==============",list)
                                            }}>
                                                <Text style={[styles.titleTextStyle, { color: "#CB4139" }]}>点击展开</Text>
                                            </TouchableOpacity>
                                        </View>
                                        :
                                        <View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>附件：</Text>
                                            </View>
                                            <View style={[{ flexDirection: 'row', flexWrap: 'wrap' }]}>
                                                {
                                                    item.compressFileList.map((item, index) => {
                                                        return (
                                                            <View style={[{ width: 120, height: 150, marginLeft: 10, marginBottom: 10, display: 'flex' }]}>
                                                                <TouchableOpacity onPress={() => {
                                                                    this.setState({
                                                                        isShowImage: true,
                                                                        pictureIndex: index
                                                                    })
                                                                }}>
                                                                    <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width: 120 }} />
                                                                </TouchableOpacity>
                                                                <Modal visible={this.state.isShowImage} transparent={true}>
                                                                    <ImageViewer onClick={() => { this.setState({ isShowImage: false }) }} index={this.state.pictureIndex}
                                                                        enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                                        onSwipeDown={() => { this.setState({ isShowImage: false }) }} imageUrls={this.state.urls}
                                                                        onSave={() => {
                                                                            saveImage(this.state.urls[this.state.pictureIndex].url)
                                                                        }} />
                                                                </Modal>
                                                            </View>
                                                        )
                                                    })
                                                }
                                            </View>
                                            <View style={[styles.titleViewStyle, { justifyContent: 'center' }]}>
                                                {
                                                    item.pictureDisplay === "Y" ?
                                                        <TouchableOpacity onPress={() => {
                                                            this.setState({
                                                                urls: []
                                                            })
                                                            let list = this.state.dataSource;
                                                            list.map((elem, index) => {
                                                                if (elem.askQuestionsId == item.askQuestionsId) {
                                                                    elem.pictureDisplay = "N"
                                                                }
                                                            })
                                                            this.setState({
                                                                dataSource: list
                                                            })
                                                            // console.log("==============",list)
                                                        }}>
                                                            <Text style={[styles.titleTextStyle, { color: "#CB4139", textAlign: 'center' }]}>点击收起</Text>
                                                        </TouchableOpacity>
                                                        :
                                                        <View />
                                                }
                                            </View>
                                        </View>
                                }
                            </View>
                        ) :
                        null
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>提问人：{item.askQuestionsUserName}</Text>
                </View>
                {
                    item.askQuestionsState === '0BB' ?
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>认领人：{item.askQuestionsToClaimUserName ? item.askQuestionsToClaimUserName : "无"}</Text>
                        </View>
                        :
                        <View>
                            {
                                item.askQuestionsState === '0CC' ?
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>解决人：{item.askQuestionsToClaimUserName ? item.askQuestionsToClaimUserName : "无"}</Text>
                                    </View>
                                    :
                                    <View />
                            }
                        </View>
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>提问时间：{item.gmtCreated}</Text>
                </View>
                {
                    item.askQuestionsState == "0CC" ?
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>解决时间：{item.gmtModified}</Text>
                        </View>
                        :
                        <View />
                }
                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                    <TouchableOpacity onPress={() => {
                        this.props.navigation.navigate("AskQuestionsSolveTrackingList", {
                            "askQuestionsId": item.askQuestionsId,
                            "listTitleName": "解决进展",
                        })
                    }}>
                        <View style={CommonStyle.itemBottomProgressGreyBtnViewStyle}>
                            <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../../assets/icon/iconfont/progress.png')}></Image>
                            <Text style={[{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }]}>进展</Text>
                        </View>
                    </TouchableOpacity>
                    {
                        item.askQuestionsState === '0CC' ?
                            null
                            :
                            <TouchableOpacity onPress={() => {
                                let message = '您确定要' + (item.askQuestionsState === '0BB' ? '取消认领' : '认领') + '该提问吗？';
                                Alert.alert('确认', message, [
                                    {
                                        text: "取消", onPress: () => {
                                            WToast.show({ data: '点击了取消' });
                                        }
                                    },
                                    {
                                        text: "确定", onPress: () => {
                                            WToast.show({ data: '点击了确定' });
                                            this.claimAskQuestions(item);
                                        }
                                    }
                                ]);
                            }}>
                                <View style={[{
                                    width: 80,
                                    height: 28,
                                    flexDirection: "row",
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    margin: 10,
                                    marginRight:0,
                                    borderColor: '#FF8C28',
                                    borderWidth: 0.85,
                                    borderRadius: 6
                                }]}>
                                    {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                    <Text style={[{ color: '#FF8C28', fontSize: 14, lineHeight: 20 }]}>{item.askQuestionsState === '0BB' ? '取消认领' : '认领'}</Text>
                                </View>
                            </TouchableOpacity>
                    }
                    <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                        <TouchableOpacity onPress={() => {
                            let message = '您确定要' + (item.askQuestionsState === '0CC' ? '重启' : '关闭') + '该提问吗？';
                            Alert.alert('确认', message, [
                                {
                                    text: "取消", onPress: () => {
                                        WToast.show({ data: '点击了取消' });
                                    }
                                },
                                {
                                    text: "确定", onPress: () => {
                                        WToast.show({ data: '点击了确定' });
                                        this.closeAskQuestions(item);
                                    }
                                }
                            ]);
                        }}>
                            {/* <View style={item.askQuestionsState === '0CC' ? [CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64, backgroundColor: '#1E6EFA' }] : [CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64, flexDirection: 'row', backgroundColor: '#FF8C28', }]}>
                                <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../../assets/icon/iconfont/close.png')}></Image>
                                <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }}>
                                    {item.askQuestionsState === '0CC' ? '重启' : '关闭'}
                                </Text>
                            </View> */}

                                <View style={[{
                                    width: 65,
                                    height: 28,
                                    flexDirection: "row",
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    margin: 10,
                                    marginRight: 0,
                                    borderColor: '#FD4246',
                                    borderWidth: 0.85,
                                    borderRadius: 6
                                }]}>
                                    {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                    <Text style={[{ color: '#FD4246', fontSize: 14, lineHeight: 20 }]}>{item.askQuestionsState === '0CC' ? '重启' : '关闭'}</Text>
                                </View>    
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        )
    }

    renderAskQuestionsStateRow = (item, index) => {
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={() => {
                    let selAskQuestionsStateCode = item.stateCode;
                    this.setState({
                        "selAskQuestionsStateCode": selAskQuestionsStateCode
                    })
                    let loadUrl = "/biz/portal/ask/questions/list";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "askQuestionsToClaimUserId": constants.loginUser.userId,
                        "askQuestionsState": selAskQuestionsStateCode === 'all' ? null : selAskQuestionsStateCode,
                        "receive_list": "Y",
                        // "qryStartTime": this.state.qryStartTime,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.stateCode} style={[CommonStyle.tabItemViewStyle]}>
                        <Text style={[item.stateCode === this.state.selAskQuestionsStateCode ?
                            [CommonStyle.selectedtabItemTextStyle]
                            :
                            [CommonStyle.tabItemTextStyle]
                        ]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='我的认领'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.askQuestionsState && this.state.askQuestionsState.length > 0)
                                ?
                                this.state.askQuestionsState.map((item, index) => {
                                    return this.renderAskQuestionsStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerHeadViewStyle: {
        borderColor: "#ffffff",
        borderWidth: 4,
        backgroundColor: "#ffffff"
    },
    // 分段器样式
    blockItemViewStyle: {
        margin: 5,
        width: 60,
        borderRadius: 0,
        paddingTop: 2, paddingBottom: 0,
        paddingLeft: 2, paddingRight: 2,
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        width: 60, borderRadius: 0,
        paddingTop: 2, paddingBottom: 0,
        paddingLeft: 2, paddingRight: 2,
        justifyContent: 'center',
        backgroundColor: "#FFFFFF",
    },


    innerViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});
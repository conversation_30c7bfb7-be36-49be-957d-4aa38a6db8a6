import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Image, ImageBackground
    , FlatList, Dimensions, ScrollView, TouchableOpacity
    , Platform, StatusBar,
} from 'react-native';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import { isIphoneX } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
// var Dimensions = require('Dimensions');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
var cols = 4;
var cellWH = 100;
var vMargin = (screenWidth - cellWH * cols) / (cols + 1);
var hMargin = 10;
class Home extends Component {
    constructor(props) {
        super(props);
        console.log("======Home=Props:", props.navigationContext);
        this.state = {
            menuTypeFlagDR: false,
            myBacklogDailyTotalRecord: 0,
            promotionPlanTotalRecord: 0,
            dailyTotalRecord: 0,
            HarvestTotalRecord: 0,
        }
    }
    UNSAFE_componentWillMount() {
        console.log('=aaaa=UNSAFE_componentWillMount==');
        console.log("=========constants.roleInfo:", constants.roleInfo);

        if (constants.loginUser.welcomePage && constants.loginUser.welcomePage != "Home") {
            this.props.navigation.navigate(constants.loginUser.welcomePage)
        }
        if (!constants.roleInfo || !constants.roleInfo.menuDTOList || constants.roleInfo.menuDTOList.length <= 0) {
            return;
        }

        // console.log("=========menuDTOList2==:", JSON.stringify(constants.roleInfo.menuDTOList));
        var homeMenuDTO;
        var homeMenuList;
        for (var j = 0, len = constants.roleInfo.menuDTOList.length; j < len; j++) {
            homeMenuDTO = constants.roleInfo.menuDTOList[j];
            if (homeMenuDTO && homeMenuDTO.menuCode && "HomeStackScreen" === homeMenuDTO.menuCode) {
                homeMenuList = homeMenuDTO.menuDTOList;
                console.log("===HomeStackScreen==homeMenuDTO===", homeMenuDTO);
                break;
            }
        }
        if (!homeMenuList || homeMenuList.length <= 0) {
            console.log("=========首页子菜单是空的");
            return;
        }
        console.log("=========homeMenuList.length==", homeMenuList.length);
        console.log("=========homeMenuList==", homeMenuList);
        var roleHomeOneLevelMenuDTO;
        for (var index = 0; index < homeMenuList.length; index++) {
            roleHomeOneLevelMenuDTO = homeMenuList[index];
            console.log("=========roleHomeOneLevelMenuDTO.menuCode==", roleHomeOneLevelMenuDTO.menuCode);
            if (!roleHomeOneLevelMenuDTO || !roleHomeOneLevelMenuDTO.menuDTOList || roleHomeOneLevelMenuDTO.menuDTOList.length <= 0) {
                console.log("========roleHomeSonMenuDTO是空的", roleHomeOneLevelMenuDTO);
                continue;
            }
            var roleSecondLevelMenuDTO;
            var homeSecondLevelMenuList = [];
            var sonMenuDTO;
            for (var secondLevelIndex = 0, len = roleHomeOneLevelMenuDTO.menuDTOList.length; secondLevelIndex < len; secondLevelIndex++) {
                roleSecondLevelMenuDTO = roleHomeOneLevelMenuDTO.menuDTOList[secondLevelIndex];
                sonMenuDTO = {
                    "code": roleSecondLevelMenuDTO.menuCode,
                    "icon": { uri: roleSecondLevelMenuDTO.menuIcon.replace("../../assets/icon", "http://image.njjzgk.com/images/jizhi_app/icon") },
                    "title": roleSecondLevelMenuDTO.menuName,
                    "component": roleSecondLevelMenuDTO.menuUrl
                }
                homeSecondLevelMenuList = homeSecondLevelMenuList.concat(sonMenuDTO);
            }
            console.log("=====homeSecondLevelMenuList==", homeSecondLevelMenuList);

            if (roleHomeOneLevelMenuDTO.menuCode === "orderMenuDataSource") {
                this.setState({
                    orderMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "materialMenuDataSource") {
                this.setState({
                    materialMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "auditMgrMenuDataSource") {
                this.setState({
                    auditMgrMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "tenantMenuDataSource") {
                this.setState({
                    tenantMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "productSaleBuyReleaseMenuDataSource") {
                this.setState({
                    productSaleBuyReleaseMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "customerMenuDataSource") {
                this.setState({
                    customerMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "contractMenuDataSource") {
                this.setState({
                    contractMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "contractOutMenuDataSource") {
                this.setState({
                    contractOutMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "paymentMgrMenuDataSource") {
                this.setState({
                    paymentMgrMenuDataSource: homeSecondLevelMenuList,
                })
            }

            else if (roleHomeOneLevelMenuDTO.menuCode === "simeFinishedMenuDataSource") {
                this.setState({
                    simeFinishedMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "dryKoleDataSource") {
                this.setState({
                    dryKoleDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "encastagedMenuDataSource") {
                this.setState({
                    encastagedMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "temperatureMenuDataSource") {
                this.setState({
                    temperatureMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "sinteringDataSource") {
                this.setState({
                    sinteringDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "unloadedMenuDataSource") {
                this.setState({
                    unloadedMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "checkInMenuDataSource") {
                this.setState({
                    checkInMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "verifyInternalMenuDataSource") {
                this.setState({
                    verifyInternalMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "verifyExternalMenuDataSource") {
                this.setState({
                    verifyExternalMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "productCheckDataSource") {
                this.setState({
                    productCheckDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "storageMenuDataSource") {
                this.setState({
                    storageMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "reportDataSource") {
                this.setState({
                    reportDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "equipmentInspectionMenuDataSource") {
                this.setState({
                    equipmentInspectionMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "digitalEmployeesMenuDataSource") {
                this.setState({
                    digitalEmployeesMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "courseMgrMenuDataSource") {
                this.setState({
                    courseMgrMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "askQuestionsMenuDataSource") {
                this.setState({
                    askQuestionsMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "courseSchedulingSettingDataSource") {
                this.setState({
                    courseSchedulingSettingDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "examManageDataSource") {
                this.setState({
                    examManageDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "assessManageDataSource") {
                this.setState({
                    assessManageDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "pointManageDataSource") {
                this.setState({
                    pointManageDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "saleManageMenuDataSource") {
                this.setState({
                    saleManageMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "collegeRecruitingMenuDataSource") {
                this.setState({
                    collegeRecruitingMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "memberShipMenuDataSource") {
                this.setState({
                    memberShipMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "hlSuppliesMgrDataSource") {
                this.setState({
                    hlSuppliesMgrDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "hlMedicineDataSource") {
                this.setState({
                    hlMedicineDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "hlSickPersonDataSource") {
                this.setState({
                    hlSickPersonDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "hospitalLogisticsDataSource") {
                this.setState({
                    hospitalLogisticsDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "settingMenuDataSource") {
                this.setState({
                    settingMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else if (roleHomeOneLevelMenuDTO.menuCode === "systemSettingMenuDataSource") {
                this.setState({
                    systemSettingMenuDataSource: homeSecondLevelMenuList,
                })
            }
            else {
                console.log("=========错误了=", homeSecondLevelMenuList)
            }


        }

        console.log("=====tenantExtAttrJSON==", constants.tenantExtAttrJSON);
        if(null != constants.tenantExtAttrJSON ){
            if(null != constants.tenantExtAttrJSON.menuTypes && (constants.tenantExtAttrJSON.menuTypes.indexOf('D') >= 0 || constants.tenantExtAttrJSON.menuTypes.indexOf('R') >= 0)){
                if(constants.tenantExtAttrJSON.menuTypes.indexOf('M') < 0 && constants.tenantExtAttrJSON.menuTypes.indexOf('H') < 0 && constants.tenantExtAttrJSON.menuTypes.indexOf('S') < 0){
                    this.setState({
                        menuTypeFlagDR: true,
                    })
                    this.loadMyBacklogDailyData();
                    this.loadDailyData();
                    this.loadPromotionPlanData();
                    this.loadHarvestData();
                    console.log("==================D,R==================")
                }
            }
        }

    }

    loadMyBacklogDailyData = () => {
        let url = "/biz/daily/audit/task/list";
        let loadRequest = {
            "userId": constants.loginUser.userId,
            "my_backlog": "Y",
            "dailyState": "0AA"
        };
        httpPost(url, loadRequest, this.loadMyBacklogDailyDataCallBack);
    }

    loadMyBacklogDailyDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                myBacklogDailyTotalRecord: response.data.totalRecord
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadDailyData = () => {
        let url = "/biz/daily/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 15,
            "userId": constants.loginUser.userId
        };
        httpPost(url, loadRequest, this.loadDailyDataCallBack);
    }

    loadDailyDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                dailyTotalRecord: response.data.totalRecord
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadPromotionPlanData = () => {
        let url = "/biz/promotion/plan/list";
        let loadRequest = {
            "userId": constants.loginUser.userId
        };
        httpPost(url, loadRequest, this.loadPromotionPlanDataCallBack);
    }

    loadPromotionPlanDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                promotionPlanTotalRecord: response.data.totalRecord
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadHarvestData = () => {
        let url = "/biz/harvest/list";
        let loadRequest = {
            "userId": constants.loginUser.userId
        };
        httpPost(url, loadRequest, this.loadHarvestDataCallBack);
    }

    loadHarvestDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                HarvestTotalRecord: response.data.totalRecord
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <View></View>
        )
    }
    // 头部中间
    renderTitleItem() {
        return (
            <Text>工作台</Text>
        )
    }

    logout = () => {
        console.log("===logout");
        let url = "/biz/user/logout?a=123&b=234"
        httpGet(url, this.logout_call_back);
        // console.log("数字员工" + JSON.stringify(constants.roleInfo, null, 6))

    }

    logout_call_back = (response) => {
        console.log("=====logout_call_back:", response);
        constants.loginUser = null;
        this.props.navigation.navigate('LoginView');
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={this.logout.bind(this)}>
                <Text style={[CommonStyle.headRightText, { color: '#33333375' }]}>登出</Text>
            </TouchableOpacity>
        )
    }

    // 点击跳转
    _pressJump(item) {
        const { navigation } = this.props;
        if (navigation && item.component != null) {
            navigation.navigate(item.component, {
                // 测试参数
                itemId: 1000000,
                code: item.code,
                title: item.title
            })
        }
    }


    renderRow = (item) => {
        if ("hidden" === item.visibility) {
            return;
        }
        // 登出
        if ("logout" === item.code) {
            return (
                <TouchableOpacity key={item.code} onPress={this.logout.bind(this)}>
                    <View key={item.code} style={styles.innerViewStyle}>
                        <Image style={styles.innerViewImageStyle} source={item.icon}></Image>
                        <Text style={CommonStyle.bodyTextStyle}>{item.title}</Text>
                    </View>
                </TouchableOpacity>
            )
        }
        return (
            <TouchableOpacity key={item.code} onPress={this._pressJump.bind(this, item)} >
                <View style={[styles.innerViewStyle, {}]}>
                    <Image style={styles.innerViewImageStyle} source={item.icon}></Image>
                    <Text style={CommonStyle.bodyTextStyle}>{item.title}</Text>
                </View>
            </TouchableOpacity>

        )
    }

    render() {
        return (
            <View>
                {/* <StatusBar hidden={false} barStyle='light-content' /> */}
                <StatusBar  
                    // animated={true} //指定状态栏的变化是否应以动画形式呈现。目前支持这几种样式：backgroundColor, barStyle和hidden  
                    hidden={false}  //是否隐藏状态栏。
                    backgroundColor={'#FFFFFF'} //状态栏的背景色  
                    // translucent={true}//指定状态栏是否透明。设置为true时，应用会在状态栏之下绘制（即所谓“沉浸式”——被状态栏遮住一部分）。常和带有半透明背景色的状态栏搭配使用。  
                    barStyle={'dark-content'} // enum('default', 'light-content', 'dark-content')   
                >  
                </StatusBar> 
                <CommonHeadScreen title='工作台'
                    style={{}}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()} />

                {/* <ImageBackground source={require('../../assets/image/workbenchBackground.png')} style={{ width: screenWidth, height: 258 }}>
                </ImageBackground> */}
                <ScrollView style={CommonStyle.contentViewStyle}>
                    {/* {this.state.menuTypeFlagDR == true ?
                        <View style={{
                            flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'center',
                            width: screenWidth, marginTop: 2, backgroundColor: '#FFF'
                        }}>
                            <Image style={[{ height: 180, borderRadius: 10, width: screenWidth / 0.9 }]} source={require('../../assets/image/entrepreneurshipGuide.png')} />
                        </View>
                        :
                        <View />
                    } */}
                    
                    {this.state.menuTypeFlagDR == true ?
                        <ImageBackground source={require('../../assets/image/workbenchBackground2.png')} style={{ width: screenWidth, height: 258 }}>
                            <View style={{flexDirection: 'row',justifyContent: 'space-around',flexWrap:'wrap',height: 77, width: screenWidth-32,
                                backgroundColor:"#FFFFFF", marginTop: 175, marginLeft: 16, marginRight: 16, borderRadius: 12}}>

                                <View style={{marginTop: 20, marginLeft:20}}>
                                    <TouchableOpacity onPress={()=>{
                                        this.props.navigation.navigate("MyBacklogDailyList", 
                                        {
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                    }}>
                                        <View style={ { height: 60,flexDirection: "column"}}>
                                            <Text style={{color: '#111111',fontSize: 18}}>{this.state.myBacklogDailyTotalRecord}</Text>
                                            <Text style={{color: 'rgba(0, 10, 32, 0.45)',fontSize: 14,marginTop: 5}}>我的待办</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>

                                {/* 分隔线 */}
                                <View style={styles.columnLineViewStyle}></View>
                                
                                <View style={{marginTop: 20}}>
                                    <TouchableOpacity onPress={()=>{
                                        this.props.navigation.navigate("DailyList", 
                                        {
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                    }}>
                                        <View style={ { height: 60, flexDirection: "column" }}>
                                            <Text style={{color: '#111111',fontSize: 18}}>{this.state.dailyTotalRecord}</Text>
                                            <Text style={{color: 'rgba(0, 10, 32, 0.45)',fontSize: 14,marginTop: 5}}>我的日报</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>

                                {/* 分隔线 */}
                                <View style={styles.columnLineViewStyle}></View>
                                
                                <View style={{marginTop: 20}}>
                                    <TouchableOpacity onPress={()=>{
                                        this.props.navigation.navigate("PromotionPlanList", 
                                        {
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                    }}>
                                        <View style={ { height: 60, flexDirection: "column" }}>
                                            <Text style={{color: '#111111',fontSize: 18}}>{this.state.promotionPlanTotalRecord}</Text>
                                            <Text style={{color: 'rgba(0, 10, 32, 0.45)',fontSize: 14,marginTop: 5}}>我的任务</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>

                                {/* 分隔线 */}
                                <View style={styles.columnLineViewStyle}></View>

                                <View style={{marginTop: 20, marginRight: 14}}>
                                    <TouchableOpacity onPress={()=>{
                                        this.props.navigation.navigate("HarvestMgrList", 
                                        {
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                    }}>
                                        <View style={ { height: 60, flexDirection: "column" }}>
                                            <Text style={{color: '#111111',fontSize: 18}}>{this.state.HarvestTotalRecord}</Text>
                                            <Text style={{color: 'rgba(0, 10, 32, 0.45)',fontSize: 14,marginTop: 5}}>我的成果</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </ImageBackground>
                        :
                        <View />
                    }

                    <View style={[styles.classViewStyle,
                    this.state.tenantMenuDataSource && this.state.tenantMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>租户管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.tenantMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.digitalEmployeesMenuDataSource && this.state.digitalEmployeesMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>数字化管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.digitalEmployeesMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.courseMgrMenuDataSource && this.state.courseMgrMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>课程管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.courseMgrMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.askQuestionsMenuDataSource && this.state.askQuestionsMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>提问管理</Text>
                    </View>

                    <FlatList
                        numColumns={4}
                        data={this.state.askQuestionsMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.courseSchedulingSettingDataSource && this.state.courseSchedulingSettingDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>排课设置</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.courseSchedulingSettingDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.examManageDataSource && this.state.examManageDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>考试管理</Text>
                    </View>

                    <FlatList
                        numColumns={4}
                        data={this.state.examManageDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />


                    <View style={[styles.classViewStyle,
                    this.state.assessManageDataSource && this.state.assessManageDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>考核管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.assessManageDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />
                    <View style={[styles.classViewStyle,
                    this.state.pointManageDataSource && this.state.pointManageDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>积分管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.pointManageDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.saleManageMenuDataSource && this.state.saleManageMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>销售管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.saleManageMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />
                    <View style={[styles.classViewStyle,
                    this.state.memberShipMenuDataSource && this.state.memberShipMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>私域运营</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.memberShipMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />
                    <View style={[styles.classViewStyle,
                    this.state.collegeRecruitingMenuDataSource && this.state.collegeRecruitingMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>就业实习平台</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.collegeRecruitingMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />
                    <View style={[styles.classViewStyle,
                    this.state.hlSuppliesMgrDataSource && this.state.hlSuppliesMgrDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>物资管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.hlSuppliesMgrDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.hlMedicineDataSource && this.state.hlMedicineDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>药品管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.hlMedicineDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.hlSickPersonDataSource && this.state.hlSickPersonDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>病患管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.hlSickPersonDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.hospitalLogisticsDataSource && this.state.hospitalLogisticsDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>平台设置</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.hospitalLogisticsDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.productSaleBuyReleaseMenuDataSource && this.state.productSaleBuyReleaseMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>产销平台</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.productSaleBuyReleaseMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.customerMenuDataSource && this.state.customerMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>客户管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.customerMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.contractMenuDataSource && this.state.contractMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>收入管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.contractMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.contractOutMenuDataSource && this.state.contractOutMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>外协管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.contractOutMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.paymentMgrMenuDataSource && this.state.paymentMgrMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>付款管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.paymentMgrMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />


                    <View style={[styles.classViewStyle,
                    this.state.orderMenuDataSource && this.state.orderMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>订单管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.orderMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.materialMenuDataSource && this.state.materialMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>原料管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.materialMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.auditMgrMenuDataSource && this.state.auditMgrMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>审核管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.auditMgrMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.simeFinishedMenuDataSource && this.state.simeFinishedMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>半成品管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.simeFinishedMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.dryKoleDataSource && this.state.dryKoleDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>干燥洞管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.dryKoleDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.encastagedMenuDataSource && this.state.encastagedMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>装窑管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.encastagedMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.temperatureMenuDataSource && this.state.temperatureMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>隧道窑高温区管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.temperatureMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.sinteringDataSource && this.state.sinteringDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>烧结管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.sinteringDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.unloadedMenuDataSource && this.state.unloadedMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>卸窑管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.unloadedMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.checkInMenuDataSource && this.state.checkInMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>砖料出入库</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.checkInMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.verifyInternalMenuDataSource && this.state.verifyInternalMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>自检管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.verifyInternalMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.verifyExternalMenuDataSource && this.state.verifyExternalMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>质检管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.verifyExternalMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.productCheckDataSource && this.state.productCheckDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>成品检选</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.productCheckDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.storageMenuDataSource && this.state.storageMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>库存管理</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.storageMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.reportDataSource && this.state.reportDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>报表</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.reportDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.equipmentInspectionMenuDataSource && this.state.equipmentInspectionMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>设备巡检</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.equipmentInspectionMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    {/* <View style={[styles.classViewStyle, 
                    this.state.digitalEmployeesMenuDataSource && this.state.digitalEmployeesMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                        ]}>
                        <Text style={styles.classTextStyle}>数字员工</Text>
                    </View>
                    <FlatList 
                    numColumns = {3}
                    data={this.state.digitalEmployeesMenuDataSource}
                    renderItem={({item}) => this.renderRow(item)}
                    /> */}

                    <View style={[styles.classViewStyle,
                    this.state.systemSettingMenuDataSource && this.state.systemSettingMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>系统设置</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.systemSettingMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                    <View style={[styles.classViewStyle,
                    this.state.settingMenuDataSource && this.state.settingMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle
                    ]}>
                        <Text style={styles.classTextStyle}>个人中心</Text>
                    </View>
                    <FlatList
                        numColumns={4}
                        data={this.state.settingMenuDataSource}
                        renderItem={({ item }) => this.renderRow(item)}
                    />

                </ScrollView >
            </View >
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:Platform.OS === 'ios' ? screenHeight - 110 : screenHeight - 140,
    //     backgroundColor:'#FFFFFF'

    // },
    classViewStyle: {
        height: 50,
        alignItems: 'flex-start',
        justifyContent: 'center',
        // borderBottomWidth: 1,
        // borderBottomColor: '#E0E0E0'
    },
    classTextStyle: {
        color: '#000',
        fontSize: 16,
        fontWeight: 'bold',
        marginLeft: 15
    },
    innerViewStyle: {
        width: cellWH,
        height: cellWH,
        marginLeft: vMargin,
        marginTop: hMargin,
        alignItems: 'center',
        justifyContent: 'center'
    },
    innerViewImageStyle: {
        width: cellWH - 50,
        height: cellWH - 50
    },
    columnLineViewStyle:{
        width:1,
        marginTop:17,
        marginBottom:17,
        flexDirection:'row',
        justifyContent:'center',
        borderRightWidth:1,
        borderColor:'rgba(117, 117, 117, 0.10)'
    },
});
module.exports = Home;
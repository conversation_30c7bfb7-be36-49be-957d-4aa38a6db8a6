import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet,Alert, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image, Modal } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../../component/EmptyRowViewComponent';
var CommonStyle = require('../../../assets/css/CommonStyle');
import { saveImage } from '../../../utils/CameraRollUtils';
import { uploadMultiImageLibrary } from '../../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class AskQuestionsSolveTrackingAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            solveTrackingId: "",
            solveTrackingContent:"",
            userId:"",
            compressFileList:[],
            pictureIndex:0,
            isShowImage: false,     //  显示弹窗组件
            urls:[
                // {
                //     url:'http://**************/image_a/10/2021-12/rn_image_picker_lib_temp_a45ead39-8c25-4fb5-b388-12be9d46cf62_200052052_small.jpg'
                // }
            ]
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { askQuestionsId,listTitleName,solveTrackingId } = route.params;
            if (askQuestionsId) {
                this.setState({
                    askQuestionsId:askQuestionsId,
                })
            }
            if (listTitleName) {
                this.setState({
                    listTitleName:listTitleName,
                })
            }
            if (solveTrackingId) {
                console.log("========Edit==solveTrackingId:", solveTrackingId);
                this.setState({
                    solveTrackingId:solveTrackingId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/ask/questions/solve/tracking/get";
                loadRequest={'solveTrackingId':solveTrackingId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditContractTrackDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }

    loadEditContractTrackDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                solveTrackingId:response.data.solveTrackingId,
                askQuestionsId:response.data.askQuestionsId,
                solveTrackingContent: response.data.solveTrackingContent,
                compressFileList:response.data.compressFileList,
                // userId:response.data.userId
            })
            var urls = [];
            if(response.data.compressFileList && response.data.compressFileList.length > 0){
                for(var i=0;i<response.data.compressFileList.length;i++){
                    var url = {
                        url:constants.image_addr + '/' +  response.data.compressFileList[i].compressFile
                    }
                    urls=urls.concat(url)
                    console.log(url)
                }
            }
            this.setState({
                urls:urls
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Image style={{ width: 22, height: 22}} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveSolveTracking =()=> {
        console.log("=======savePointConfig");
        let toastOpts;
        if (!this.state.solveTrackingContent) {
            toastOpts = getFailToastOpts("请输入进展说明");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/ask/questions/solve/tracking/add";
        if (this.state.solveTrackingId) {
            console.log("=========Edit===solveTrackingId", this.state.solveTrackingId)
            url= "/biz/ask/questions/solve/tracking/modify";
        }
        let requestParams={
            solveTrackingId:this.state.solveTrackingId,
            solveTrackingContent: this.state.solveTrackingContent,
            askQuestionsId: this.state.askQuestionsId,
            userId:constants.loginUser.userId,
            compressFileList:this.state.compressFileList,
        };
        httpPost(url, requestParams, this.saveSolveTrackingCallBack);
    }

    // 保存回调函数
    saveSolveTrackingCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
        }
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '进展'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>进展说明</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <View style={[styles.inputRowStyle,{height:240}]}>
                        <TextInput 
                        multiline={true}
                        textAlignVertical="top"
                        style={[CommonStyle.inputRowText,{height:230}]}
                        placeholder={'请输入进展说明'}
                        onChangeText={(text) => this.setState({solveTrackingContent:text})}
                        >
                        {this.state.solveTrackingContent}
                        </TextInput>
                    </View>

                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>附件（最多5张）</Text>
                    </View>
                    <View>

                        {
                            this.state.compressFileList && this.state.compressFileList.length > 0 ?
                            (
                                <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                    {
                                        this.state.compressFileList.map((item,index) =>{
                                            return(
                                                <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex'}]}>
                                                <TouchableOpacity
                                                    style={{position:'absolute',left:110,top:-10,zIndex:1000}}
                                                    onPress={() => {
                                                        console.log("========deletePhoto")
                                                        var urls = this.state.urls;
                                                        var compressFileList = this.state.compressFileList;

                                                        urls.splice(index,1);
                                                        compressFileList.splice(index,1);
                                                        console.log(urls)
                                                        console.log(this.state.compressFileList)

                                                        this.setState({
                                                            urls:urls,
                                                            compressFileList:compressFileList
                                                        })
                                                    }}
                                                >
                                                    <Image style={{ width: 22, height: 22}} source={require('../../../assets/icon/iconfont/deleteRed.png')}></Image>

                                                </TouchableOpacity>
                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        isShowImage:true,
                                                        pictureIndex:index
                                                    })
                                                    // uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                                    //     console.log("========imageUploadResponse", imageUploadResponse)
                                                    //     if (imageUploadResponse.code === 200) {
                                                    //         WToast.show({ data: "上传成功" });
                                                    //         let compressFileList = imageUploadResponse.data
                                                    //         this.setState({
                                                    //             compressFileList: compressFileList
                                                    //         })
                                                    //     }
                                                    //     else {
                                                    //         WToast.show({ data: imageUploadResponse.message });
                                                    //     }
                                                    // });

                                                }}>
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />

                                                </TouchableOpacity>
                                                <Modal visible={this.state.isShowImage} transparent={true}>
                                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}}  index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                        onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls}
                                                        onSave={() => {
                                                        saveImage( this.state.urls[this.state.pictureIndex].url)
                                                        }}/>
                                                </Modal>
                                            </View>
                                            )

                                        })
                                    }

                                    {
                                        this.state.compressFileList.length < 5 ?
                                        (
                                            <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                                <TouchableOpacity onPress={() => {
                                                    uploadMultiImageLibrary(5, "big_size_image_5000", (imageUploadResponse) => {
                                                        console.log("========imageUploadResponse", imageUploadResponse)
                                                        if (imageUploadResponse.code === 200) {
                                                            WToast.show({ data: "上传成功" });
                                                            let compressFileList = imageUploadResponse.data
                                                            this.setState({
                                                                compressFileList: this.state.compressFileList.concat(compressFileList)
                                                            })
                                                            var urls = this.state.urls;
                                                            if(compressFileList && compressFileList.length > 0){
                                                                for(var i=0;i<compressFileList.length;i++){
                                                                    var url = {
                                                                        url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                                    }
                                                                    urls=urls.concat(url)
                                                                    console.log(url)
                                                                }
                                                            }
                                                            this.setState({
                                                                urls:urls
                                                            })
                                                        }
                                                        else {
                                                            WToast.show({ data: imageUploadResponse.message });
                                                        }
                                                    });

                                                }}>
                                                    <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                                        <Image source ={require('../../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                        )
                                        :
                                        <View/>
                                    }                                
                                    
                                </View>
                            )

                            :
                            <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                            <TouchableOpacity onPress={() => {
                                    uploadMultiImageLibrary(5, "big_size_image_5000", (imageUploadResponse) => {
                                        console.log("========imageUploadResponse", imageUploadResponse)
                                        if (imageUploadResponse.code === 200) {
                                            WToast.show({ data: "上传成功" });
                                            let compressFileList = imageUploadResponse.data
                                            this.setState({
                                                compressFileList: compressFileList
                                            })
                                            var urls = [];
                                            if(compressFileList && compressFileList.length > 0){
                                                for(var i=0;i<compressFileList.length;i++){
                                                    var url = {
                                                        url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                    }
                                                    urls=urls.concat(url)
                                                    console.log(url)
                                                }
                                            }
                                            this.setState({
                                                urls:urls
                                            })
                                        }
                                        else {
                                            WToast.show({ data: imageUploadResponse.message });
                                        }
                                    });

                                }}>
                                    <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                        <Image source ={require('../../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        }

                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveSolveTracking.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:160,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
});
import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Linking,Clipboard,
    FlatList,RefreshControl,Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class AssessApplyList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,

            topBlockLayoutHeight:0,
            selAssessRecordStateCode:'all',
        }
    }

        //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let assessRecordState = [
            {
                stateCode:'all',
                stateName:'全部',
            },
            {
                stateCode:'0AA',
                stateName:'未审批',
            },
            {
                stateCode:'0AB',
                stateName:'已审批',
            }
        ]
        this.setState({
            assessRecordState:assessRecordState,
        })
        this.loadAssessApplyList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/assess/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "applyUserId":constants.loginUser.userId,
            "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

      // 下拉触顶刷新到第一页
      _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            // console.log("==========不刷新=====");
            // console.log("=====applyUserId=====",constants.loginUser.userId)
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/assess/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "applyUserId":constants.loginUser.userId,
            "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,

        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadAssessApplyList();
    }

    loadAssessApplyList=()=>{
        let url= "/biz/assess/record/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "applyUserId":constants.loginUser.userId,
            "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,

        };
        httpPost(url, loadRequest, this.loadAssessApplyListCallBack);
    }


    loadAssessApplyListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // console.log(dataNew)
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteAssessApply =(assessRecordId)=> {
        console.log("=======delete=assessRecordId", assessRecordId);
        let url= "/biz/assess/record/delete";
        let requestParams={'assessRecordId':assessRecordId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }
    renderAssessRecordStateRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    let selAssessRecordStateCode = item.stateCode;
                    this.setState({
                        "selAssessRecordStateCode":selAssessRecordStateCode
                    })
                    let loadUrl= "/biz/assess/record/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "applyUserId":constants.loginUser.userId,
                        "assessRecordState": selAssessRecordStateCode === 'all' ? null : selAssessRecordStateCode,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.stateCode} style={[CommonStyle.tabItemViewStyle]}>
                        <Text style={[item.stateCode === this.state.selAssessRecordStateCode ?
                            [CommonStyle.selectedtabItemTextStyle]
                            :
                            [CommonStyle.tabItemTextStyle]
                        ]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.assessRecordId} style={[CommonStyle.innerViewStyle]}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>申请日期：{item.applyDate}</Text>
                    {
                        item.assessRecordState ==="0AB" ? 
                        <View>
                            <Text style={{color:"#3ab240"}}>已审批</Text>
                        </View>
                        :
                        <View>
                            <Text style={{color:'rgba(255,0,0,0.4)'}}>未审批</Text>
                        </View>
                    }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核标题：{item.assessTitle}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核内容：{item.assessContent}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核类别：{item.assessClassName}</Text>

                </View>
                {
                    item.assessRecordState ==="0AA" ?
                    <View></View>
                    :
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>考核难度：{item.assessDifficultyName}</Text>
                    </View>
                }

                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核人：{item.assessUserName}</Text>
                </View>

                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>预计考核日期：{item.expectAssessDate}</Text>
                </View>

                {
                    item.assessRecordState ==="0AA" ?
                    <View></View>
                    :
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>实际考核日期：{item.actualAssessDate}</Text>
                    </View>
                }

                {
                item.assessRecordState =="0AA" ?
                <View></View>
                    :
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核结果：{item.assessResultName}</Text>
                </View>
                }

                {
                item.assessRecordState =="0AA" ?
                <View></View>
                :
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,{marginBottom:10}]}>考核意见：{item.assessOpinion?item.assessOpinion:"无"}</Text>
                </View>
                }


            <View style={[CommonStyle.itemBottomBtnStyle,{ flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                {
                        item.assessRecordState === "0AB" ?
                        <View />                        
                        :
                        <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>    
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该部位吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteAssessApply(item.assessRecordId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle, { width: 64 }]}>
                            <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                        </View>
                    </TouchableOpacity>    
                </View>
                }

                {
                item.assessRecordState === "0AB" ?
                <View />                        
                :
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>    
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("AssessApplyAdd", 
                            {
                                // 传递参数
                                assessRecordId:item.assessRecordId,
                                assessUserName:item.assessUserName,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                            
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64 }]}>
                            <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>     
                }
            </View>
        </View>
    
        )
    }    

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }    

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("AssessApplyAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title='我的申请'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                    {
                        (this.state.assessRecordState && this.state.assessRecordState.length > 0) 
                        ? 
                        this.state.assessRecordState.map((item, index)=>{
                            return this.renderAssessRecordStateRow(item)
                        })
                        : <View/>
                    }  
                    </View> 
                </View>                
                <View style={[CommonStyle.contentViewStyle,{ height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />

                </View>
            </View>
        )
    }
}


const styles = StyleSheet.create({
    innerHeadViewStyle:{
        borderColor:"#ffffff",
        borderWidth:4,
        backgroundColor:"#ffffff"
    },
    // 分段器样式
    blockItemViewStyle: {
        margin: 5,
        width: 60, 
        borderRadius: 0,
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        width: 60, borderRadius: 0, 
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: "#FFFFFF", 
    },
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});
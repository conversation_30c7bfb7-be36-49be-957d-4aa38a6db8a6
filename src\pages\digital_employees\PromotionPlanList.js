import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Linking, Clipboard,
    FlatList, RefreshControl, Image, ImageBackground, Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class PromotionPlanList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            selCompletionStateCode: 'all',
            qryStartTime: null,
            selectedQryStartDate: [],
            currentTime: "",
            userPhotoUrl: constants.image_addr + '/' + constants.loginUser.userPhoto,
            userPhoto: "",
            editModal: false,
            deleteModal: false,
            promotionPlanItem: {},
            exportPdfModal:false,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    initGmtCreated = () => {
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var currentHour = ("0" + (currentDate.getHours() + 8)).slice(-2);
        var currentMinute = ("0" + currentDate.getMinutes()).slice(-2);
        var currentSecond = ("0" + currentDate.getSeconds()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay + " " + currentHour + ":" + currentMinute + ":" + currentSecond;
        return _gmtCreated;
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        var currentTime = this.initGmtCreated();
        this.setState({
            currentTime: currentTime
        })
        let completionStateDataSource = [
            {
                stateCode: 'all',
                stateName: '全部',
            },
            {
                stateCode: 'I',
                stateName: '未完成',
            },
            {
                stateCode: 'C',
                stateName: '已完成',
            }
        ]
        this.setState({
            completionStateDataSource: completionStateDataSource,
        })
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectedQryStartDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            // qryStartTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })
        httpPost("/biz/portal/user/get", { "userId": constants.loginUser.userId }, (response) => {
            if (response.code === 200) {
                let userPhoto = response.data.userPhoto;
                this.setState({
                    userPhotoUrl: constants.image_addr + '/' + userPhoto,
                    userPhoto: userPhoto
                })
            }
        });
        this.loadPromotionPlanList();
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/promotion/plan/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "completionState": this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/promotion/plan/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "completionState": this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadPromotionPlanList();
    }

    loadPromotionPlanList = () => {
        let url = "/biz/promotion/plan/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "completionState": this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this.loadPromotionPlanListCallBack);
    }

    loadPromotionPlanListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deletePromotionPlan = (planId) => {
        console.log("=======delete=planId", planId);
        let url = "/biz/promotion/plan/delete";
        let requestParams = { 'planId': planId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    renderRow = (item, index) => {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PromotionPlanDetail", {
                    // 传递参数
                    planId: item.planId,
                    userName: item.userName,
                    trackFkId: item.planId,
                    trackType: "PP",
                    // 传递回调函数
                    refresh: this.callBackFunction
                })
            }}>

            <View key={item.planId} style={[CommonStyle.innerViewStyle]}>
                {/* 任务顶部信息 */}
                <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
                    {
                        this.state.userPhoto ?
                            <Image source={{ uri: this.state.userPhotoUrl }} style={{ height: 48, width: 48, borderRadius: 50}} />
                            :
                            <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                    {
                                        item.userName.length <= 2 ? 
                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {item.userName}
                                        </Text>
                                        :
                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {item.userName.slice(-2)}
                                        </Text>
                                    }
                                </View>
                            </ImageBackground>
                    }
                    
                    <View style={{marginLeft:11, flexDirection: 'column'}}>
                        <View style={{flexDirection: 'row', marginTop: 4 }}>
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={{ fontSize: 16 }}>{item.userName}的任务</Text>
                            </View>

                            <View style={{flexDirection: 'row'}}>
                                {
                                    item.completionState === 'C' ?
                                        <View style={{ width: 52, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: '#1BBC82' }}>
                                            <Text style={{fontSize: 13, color: '#FFFFFF' }}>已完成</Text>
                                        </View>
                                        :
                                        <View style={{ width: 52, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: '#D50400' }}>
                                            <Text style={{fontSize: 13, color: '#FFFFFF' }}>未完成</Text>
                                        </View>
                                }
                            </View>
                        </View>

                        <View style={{flexDirection: 'row'}}>
                        <Image style={{ height: 13 , width: 12, marginTop: 5, marginLeft: 1, marginRight: 5}} source={require('../../assets/icon/iconfont/clock.png')}></Image> 
                            <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                                <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)'}]}>{item.planCreatedTime} 提交</Text>
                            </View>
                        </View>
                    </View>
                    <View style={{ position:'absolute', right: 13, top: 0}}>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                editModal: true,
                                promotionPlanItem: item
                            })
                        }}>
                            <View style={[{width: 35, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                                <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* 分隔线 */}
                <View style={styles.lineViewStyle}/>

                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>标题</Text>
                    <View style={{ height: 24, paddingLeft: 6, paddingRight: 6, position:'absolute', right: 0, top: -12, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#ECEEF2' }}>
                        <Text style={{fontSize: 14, lineHeight: 20, color: '#404956' }}>{item.belongClassName}</Text>
                    </View>
                </View>
                <View style={styles.itemContentTextStyle}>
                    <Text style={styles.itemContentStyle}>{item.planTitle}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>内容</Text>
                </View>
                <View style={[styles.itemContentTextStyle, { marginBottom: 12 }]}>
                    <Text style={styles.itemContentStyle}>{item.planContent}</Text>
                </View>

                {/* <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                    <TouchableOpacity onPress={() => {
                        this.props.navigation.navigate("PortalTrackingList", {
                            "trackFkId": item.planId,
                            "completionState": item.completionState,
                            "trackType": "PP",
                            "listTitleName": "进展说明",
                        })
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle, {
                            width: 100,
                            borderWidth: 1,
                        }
                        ]}>
                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/progressBlack.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>进展查询</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        if (dateDiffHours(this.state.currentTime, item.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                            return;
                        }
                        Alert.alert('确认', '您确定要删除吗？', [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.deletePromotionPlan(item.planId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle, { width: 64 }
                            , dateDiffHours(this.state.currentTime, item.gmtCreated) > constants.loginUser.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""]}>
                            <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        if (item.completionState === 'C' || dateDiffHours(this.state.currentTime, item.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                            return;
                        }
                        this.props.navigation.navigate("PromotionPlanAdd",
                            {
                                // 传递参数
                                planId: item.planId,
                                checkInUserName: item.checkInUserName,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64, marginRight: 16 }
                            , (item.completionState === 'C' || dateDiffHours(this.state.currentTime, item.gmtCreated) > constants.loginUser.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""]}>
                            <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}

                
            </View>

            </TouchableOpacity>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (

            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PromotionPlanAdd",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/addBlack.png')}></Image>

            </TouchableOpacity>
        )
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }
    renderCompletionStateRow = (item, index) => {
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={() => {
                    let selCompletionStateCode = item.stateCode;
                    this.setState({
                        "selCompletionStateCode": selCompletionStateCode
                    })

                    let loadUrl = "/biz/promotion/plan/list";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "userId": constants.loginUser.userId,
                        "completionState": selCompletionStateCode === 'all' ? null : selCompletionStateCode,
                        "qryStartTime": this.state.qryStartTime,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.stateCode} style={[{width: screenWidth/3 , height: 49, flexDirection: 'row', justifyContent: 'center'}
                    // ,item.stateCode === this.state.selCompletionStateCode ?
                    //     [styles.selectedBlockItemViewStyle]
                    //     :
                    //     [styles.blockItemViewStyle],
                    ]}>
                        <Text style={[item.stateCode === this.state.selCompletionStateCode ?
                            { color: "#255BDA", fontSize: 16, fontWeight: '500', lineHeight: 49, textAlign: 'center', borderColor: "#255BDA", borderBottomWidth: 2, paddingLeft: 5, paddingRight: 5 }
                            :
                            { color: "#2B333F", fontSize: 16, fontWeight: '500', lineHeight: 49, textAlign: 'center'},
                        ]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }


    openQryStartDate() {
        this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate)
    }

    callBackSelectQryStartDateValue(value) {
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryStartDate: value
        })
        if (value && value.length) {
            var qryStartTime = "";
            var vartime;
            for (var index = 0; index < value.length; index++) {
                vartime = value[index];
                if (index === 0) {
                    qryStartTime += vartime;
                }
                else {
                    qryStartTime += "-" + vartime;
                }
            }
            this.setState({
                qryStartTime: qryStartTime
            })

            let loadUrl = "/biz/promotion/plan/list";
            let loadRequest = {
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "userId": constants.loginUser.userId,
                "completionState": this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
                "qryStartTime": qryStartTime,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }
    }

    resetQry() {
        this.setState({
            qryStartTime: null,
            selCompletionStateCode: "all",
        })
        let loadUrl = "/biz/promotion/plan/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "completionState": null,
            "qryStartTime": null,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    exportPdfFile = () => {
        console.log("=======exportPdfFile");
        let url = "/biz/generate/pdf/promotion_plan";
        let requestParams = {
            "currentPage": 1,
            "pageSize": 1000,
            "userId": constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, requestParams, (response) => {
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data);
                WToast.show({ data: "导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data });
                // 直接打开外网链接 
                Linking.openURL(response.data)
                
            }
        });
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='我的任务'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                {/* 头部操作区 */}
                <View style={[CommonStyle.headViewStyle, {width: screenWidth,borderWidth: 0}]} onLayout={this.topBlockLayout.bind(this)}>

                    <View style={{flexDirection: 'row' }}>
                        {
                            (this.state.completionStateDataSource && this.state.completionStateDataSource.length > 0)
                                ?
                                this.state.completionStateDataSource.map((item, index) => {
                                    return this.renderCompletionStateRow(item)
                                })
                                : <View />
                        }
                    </View>

                    <View style={{height: 48, flexDirection: 'row', justifyContent: 'space-evenly', alignItems: 'center', padding: 8, backgroundColor: '#FFFFFF'}}>
                        <View style={[{width: screenWidth - 120, flexDirection: 'row',backgroundColor: '#F2F5FC', height: 32, borderRadius: 15}]}>
                            <TouchableOpacity onPress={() => this.openQryStartDate()}>
                                <View style={{ flexDirection: 'row', alignItems: 'center', height: 30, width: screenWidth - 160}}>
                                    <Image style={{ width: 16, height: 16, marginLeft: 15 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                    <Text style={{ color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 13, marginLeft: 6, lineHeight: 20 }}>
                                        {!this.state.qryStartTime ? "提交日期" : this.state.qryStartTime}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => this.resetQry()}>
                                <View style={[{ width: 30, flex: 1, justifyContent:'center', alignItems: 'center' }]}>
                                    <Image style={{ width: 16, height: 16 }} source={require('../../assets/icon/iconfont/replace.png')}></Image>
                                </View>
                            </TouchableOpacity>
                        </View>
                        {/* <TouchableOpacity onPress={()=>this.resetQry()}>
                            <View style={[CommonStyle.resetBtnViewStyle, { width:70,marginLeft:20, flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/replace.png')}></Image>
                                <Text style={CommonStyle.resetBtntextStyle}>重置</Text>
                            </View>
                        </TouchableOpacity> */}
                        <TouchableOpacity onPress={() => {
                            // 触发-导出弹窗Modal
                            this.setState({
                                exportPdfModal: true
                            })
                            // Alert.alert('确认', '您确定要将查询到的任务导出为PDF文件吗？', [
                            //     {
                            //         text: "取消", onPress: () => {
                            //             WToast.show({ data: '点击了取消' });
                            //         }
                            //     },
                            //     {
                            //         text: "确定", onPress: () => {
                            //             WToast.show({ data: '点击了确定' });
                            //             this.exportPdfFile()
                            //         }
                            //     }
                            // ]);
                        }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, 
                                {height: 32, width: 64, margin: 0, alignItems: 'center', backgroundColor: '#1E6EFA', borderRadius: 20
                            }]}>
                                <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { fontSize: 14 }]}>导出</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>

                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
                <BottomScrollSelect
                    ref={'SelectQryStartDate'}
                    callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
                />

                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.editModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    if (this.state.promotionPlanItem.completionState === 'C' || dateDiffHours(this.state.currentTime, this.state.promotionPlanItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                        WToast.show({ data: '该任务不可编辑' });
                                        return;
                                    }
                                    this.setState({
                                        editModal: false,
                                    })
                                    this.props.navigation.navigate("PromotionPlanAdd",
                                        {
                                            // 传递参数
                                            planId: this.state.promotionPlanItem.planId,
                                            checkInUserName: this.state.promotionPlanItem.checkInUserName,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , (this.state.promotionPlanItem.completionState === 'C' || dateDiffHours(this.state.currentTime, this.state.promotionPlanItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""]}>
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => {
                                    console.log("promotionPlanItem=================",this.state.promotionPlanItem)
                                    if (dateDiffHours(this.state.currentTime, this.state.promotionPlanItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                        WToast.show({ data: '任务已超出删除时限' });
                                        return;
                                    }
                                    // 删除弹窗Modal
                                    this.setState({
                                        editModal: false,
                                        deleteModal: true
                                    })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , dateDiffHours(this.state.currentTime, this.state.promotionPlanItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""]}>
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        editModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该任务?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.64)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deletePromotionPlan(this.state.promotionPlanItem.planId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                {/* 导出pdf弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.exportPdfModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认导出任务？</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>导出地址已复制到粘贴板，使用浏览器打开</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 291, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        exportPdfModal: false
                                    })
                                    WToast.show({ data: '点击了不打开' });
                                }}>

                                    <View style={{ width: 145, height: 56, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#000A20', }}>不打开</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    WToast.show({ data: '点击了打开' });
                                    this.setState({
                                        exportPdfModal: false
                                    })
                                    this.exportPdfFile()
                                }}>

                                    <View style={{ width: 145, height: 56, alignItems: 'center', justifyContent: 'center', borderLeftWidth: 1, borderColor: '#DFE3E8' }}>
                                        <Text style={{ fontSize: 17,fontWeight: '400', color: '#1E6EFA' }}>打开</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        // marginTop:10,
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        borderWidth: 8
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    searchInputText: {
        width: screenWidth - 300,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop: 0
    },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#FFFFFF",
        backgroundColor: "#FFFFFF",
        borderRadius: 5,
        marginTop: 5
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        width: 60, borderRadius: 0,
        paddingTop: 2, paddingBottom: 0,
        paddingLeft: 2, paddingRight: 2,
        justifyContent: 'center',
        backgroundColor: "#FFFFFF",
        // marginTop: 0, 
    },
    // 分段器样式
    blockItemViewStyle: {
        margin: 5,
        width: 60,
        borderRadius: 0,
        paddingTop: 2, paddingBottom: 0,
        paddingLeft: 2, paddingRight: 2,
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
        // marginTop: 0, 
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentTextStyle: {
        marginLeft: 14,
        marginRight: 16,
        marginTop: 3,
        lineHeight: 24,
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 14,
        marginRight: 16,
        marginTop: 5
    },
    titleTextStyle: {
        fontSize: 16,
        lineHeight: 22
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        marginBottom: 6,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
});
import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image, TextInput,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ScrollView } from 'react-native-gesture-handler';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
import <PERSON>Viewer from 'react-native-image-zoom-viewer';
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;

import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

const leftLabWidth = 130;
export default class ConfiguringTenantsParam extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            pictureIndex: 0,
            isShowImage: false,
            image: "",
            currentTenantId: null,
            PortalTenantParamDataSource: [
                {
                    bizModuleCode: 'D',
                    paramName: '日报分享'
                },
                {
                    bizModuleCode: 'R',
                    paramName: '人才库'
                },
                {
                    bizModuleCode: 'E',
                    paramName: '企业库'
                },
                {
                    bizModuleCode: 'V',
                    paramName: '私域共享平台'
                },
                {
                    bizModuleCode: 'J',
                    paramName: '经川财经'
                }
            ],
            configItemSettingList: [],
            // 判断租户是否已配置
            configItemSettingFlag: false
        }
    }


    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { currentTenantId } = route.params;
            console.log("currentTenantId===========", currentTenantId)
            if (currentTenantId) {
                this.setState({
                    currentTenantId: currentTenantId
                })
                this.loadTenantConfigItemSetting(currentTenantId);
            }
        }
        this.loadPortalTenantConfigList();
    }

    // 加载所有配置项
    loadPortalTenantConfigList = () => {
        let url = "/biz/config/item/param/listByModuleCode";
        let loadRequest = {
            "tenantId": constants.loginUser.tenantId,
        };
        httpPost(url, loadRequest, this.loadPortalTenantConfigListCallBack);
    }

    loadPortalTenantConfigListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data) {
            console.log("response.data===========", response.data)
            var dataNew = response.data;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 加载租户配置项
    loadTenantConfigItemSetting = (currentTenantId)=>{
        let url= "/biz/tenant/config/getTenantConfigItemSetting";
        let loadRequest={
            "paramCode": "CONFIG_ITEM_SETTING",
            "currentTenantId": currentTenantId,
        };
        httpPost(url, loadRequest, this.loadTenantConfigItemSettingCallBack);
    }

    loadTenantConfigItemSettingCallBack = (response) => {
        console.log("====loadTenantConfigItemSettingCallBack====", response)
        if (response.code == 200 && response.data) {
            var val = response.data.paramValue;
            if (val) {
                var configItemSetting = val.split(",")
                console.log("====configItemSetting====", configItemSetting)
                this.setState({
                    configItemSettingList: configItemSetting,
                })
            }
            this.setState({
                configItemSettingFlag: true
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={this.saveTenantConfigItemSetting.bind(this)}>
                <Image style={{ width: 28, height: 28, marginRight: 5 }} source={require('../../assets/icon/iconfont/ok1.png')}></Image>
            </TouchableOpacity>
        )
    }

    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    saveTenantConfigItemSetting = () => {
        console.log("=======saveTenantConfigItemSetting");
        let url = "/biz/tenant/config/addTenantConfigItemSetting";
        if (this.state.configItemSettingFlag) {
            url = "/biz/tenant/config/modifyTenantConfigItemSetting";
        }
        console.log("url=======", url)
        let requestParams = {
            "paramCode": "CONFIG_ITEM_SETTING",
            "paramValue": this.state.configItemSettingList.toString(),
            "currentTenantId": this.state.currentTenantId,
            "valueType": "text"
        };
        console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.saveTenantConfigItemSettingCallBack);
    }

    saveTenantConfigItemSettingCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.loadTenantConfigItemSetting(this.state.currentTenantId);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='租户配置项'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    {this.state.dataSource.map((elem, key) => {
                        return (
                            <View style={[{ flexDirection: 'column', width: screenWidth, justifyContent: 'flex-start' }]}>
                                <View style={styles.inputRowStyle}>
                                    <View style={[styles.leftLabView]}>
                                        <Text style={styles.leftLabNameTextStyle}>{elem.paramName}</Text>
                                    </View>
                                </View>
                                <View style={[{ flexDirection: 'row', width: screenWidth, justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                                    {elem.configItemParamList ?
                                        (elem.configItemParamList.map((item, key) => {
                                            return (
                                                <TouchableOpacity onPress={() => {
                                                    var selConfigItemSetting = this.state.configItemSettingList;
                                                    if (selConfigItemSetting.includes(item.paramCode)) {
                                                        arrayRemoveItem(selConfigItemSetting, item.paramCode);
                                                    }
                                                    else {
                                                        selConfigItemSetting = selConfigItemSetting.concat(item.paramCode)
                                                    }
                                                    this.setState({
                                                        configItemSettingList: selConfigItemSetting,
                                                    })
                                                    WToast.show({ data: '点击了' + item.paramName });
                                                    console.log("======selConfigItemSetting:", selConfigItemSetting)
                                                }}>
                                                    <View>
                                                        {
                                                            this.state.configItemSettingList.includes(item.paramCode) ?
                                                                <View key={item.paramCode} style={styles.selectedBlockItemViewStyle}>
                                                                    <Text style={[styles.selectedBlockItemTextStyle]}>{item.paramName}</Text>
                                                                </View>
                                                                :
                                                                <View key={item.paramCode} style={styles.blockItemViewStyle}>
                                                                    <Text style={[styles.blockItemTextStyle]}>{item.paramName}</Text>
                                                                </View>
                                                        }
                                                    </View>
                                                </TouchableOpacity>
                                            )
                                        }))
                                        :
                                        <View></View>
                                    }
                                </View>
                            </View>
                        )
                    })}
                </ScrollView>
            </View>
        );
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    container:{
        flex: 1,
        justifyContent: 'center', // 水平居中
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    lineViewStyle:{
        // height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 2,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 14
    },
    innerRenderViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        backgroundColor: '#FFFFFF',
        borderWidth: 8,
        paddingBottom: 8,
        paddingTop: 8,
        borderRadius: 20
    },
    lineViewStyle:{
        // height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },
    blockItemViewStyle: {
        margin: 5,
        borderRadius: 4,
        padding: 10,
        justifyContent: 'center',
        backgroundColor: '#F5F5F5'
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        borderRadius: 4,
        padding: 10,
        justifyContent: 'center',
        backgroundColor: '#1E6EFA'
    },
    blockItemTextStyle: {
        color: '#000000',
        fontSize: 14
    },
    selectedBlockItemTextStyle: {
        color: '#FFFFFF',
        fontSize: 14
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
});
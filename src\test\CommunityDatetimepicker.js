import React, { useState } from 'react';
import {
    View, Button,
    Text,
    Dimensions,
    ScrollView,
    StyleSheet,
} from 'react-native';
import moment from "moment/moment";

import DateTimePicker from '@react-native-community/datetimepicker';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;


// https://www.npmjs.com/package/@react-native-community/datetimepicker
// https://github.com/react-native-datetimepicker/datetimepicker
export default class CommunityDatetimepicker extends React.Component {


    constructor(props) {
        super(props);
        this.state = {
            time: "21:12",
            date: "2021-12-13 21:12",
        }
    }
    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
    }

    setOpen = (open) => {
        this.setState({
            open: open,
        })
    }
    setDate = (date) => {
        this.setState({
            date: date,
        })
    }

    dateFormatter=(value)=> {
        // https://www.cnblogs.com/catgatp/p/13178934.html
        // YYYY-MM-DD HH:mm
        var date = moment.parseZone(value).local().format('HH:mm');
        return date;
    }

    timeOnChange = (event, selecteTime) => {
        const selTime = this.dateFormatter(selecteTime);
        this.setState({
            time: selTime
        })
        console.log("======onChange", selTime);
    }


    render() {
        return (
            <ScrollView style={{ width: screenWidth, height: screenHeight, overflow: 'scroll', flexDirection: 'column', backgroundColor: '#fff000' }}>
                <View style={{ alignItems: 'center', justifyContent: 'center', backgroundColor: 'red' }}>
                    <View style={{ flexDirection: 'column' }}>
                        <Text style={styles.instructions} onPress={() => this.setOpen(true)}>date: {this.state.time}</Text>
                        <DateTimePicker
                            value={new Date()}
                            mode="time"
                            is24Hour={true}
                            display="default"
                            onChange={this.timeOnChange}
                        />
                    </View>
                </View>
            </ScrollView>
        )
    }

}

const styles = StyleSheet.create({
    instructions: {
        fontSize: 20,
    },
});
import React,{ Component } from 'react';
import { ScrollView } from 'react-native';
import {View, Text, TouchableOpacity,Dimensions} from 'react-native';
import EmptyRowViewComponent from './EmptyRowViewComponent';
var CommonStyle = require('../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class OrderSelectComponent extends Component {

    constructor(props) {
        super(props);
        this.state = {
            showSearchItemOrderBlock:props.showSearchItemOrderBlock,
            ordersDataSource:[],
            selProductionLineId:null,
            selOrderName:null,
            selOrderId:null,
        };
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadUrl = "/biz/order/list";
        let loadRequest = {
            'currentPage':1,
            'pageSize':100,
            "display":"Y",
            "excludeOrderStateList":[
                "A","K"
            ],
            'productionLineId':this.state.selProductionLineId
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadOrder);
    }

     // 订单回调加载
     callBackLoadOrder=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList && response.data.dataList.length > 0) {
            this.setState({
                ordersDataSource:response.data.dataList,
                selBrickTypeId:response.data.dataList[0] ? response.data.dataList[0].brickTypeId : 0,
                customerName:response.data.dataList[0] ? response.data.dataList[0].customerName : 0,
                // selOrderId:response.data.dataList[0] ? response.data.dataList[0].orderId : 0,
            })
            if (!this.state.checkId) {
                this.setState({
                    selOrderId:response.data.dataList[0] ? response.data.dataList[0].orderId : 0,
                })
            }
        }
    }

    renderOrderItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                selOrderId:item.orderId,
                selOrderName:item.orderName,
                }) 
            }}>
                <View key={item.positionId} style={item.orderId===this.state.selOrderId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.orderId===this.state.selOrderId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                {
                this.state.showSearchItemOrderBlock ?
                <ScrollView style={CommonStyle.fullScreenKeepOut}>
                    <View>
                        <View style={CommonStyle.rowLabView}>
                            <Text style={CommonStyle.rowLabTextStyle}>砖型</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.orderDataSource && this.state.orderDataSource.length > 0) 
                                ? 
                                this.state.orderDataSource.map((item, index)=>{
                                    return this.renderOrderItem(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>
                    <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                        <TouchableOpacity onPress={() => { 
                            console.log("asdf=========adfasd");
                                this.props.setState({
                                    showSearchItemOrderBlock:false
                                }) 
                            this.setState({
                                // showSearchItemOrderBlock:false,
                            }) 
                        }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消12取消121取消1211</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => { 
                            // this.props.callbackParent(selOrderId, selOrderName);
                            this.setState({
                                showSearchItemOrderBlock:false,
                            }) 
                        }}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                :
                null}
            </View>
        )
    }

}
import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class ProductionLineMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            operateTenantId:"",
            operate:"",
            productionLineId:"",
            productionLineName:"",
            kilnRoadName:""
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const {productionLineId, operateTenantId } = route.params;
            if(operateTenantId){
                this.setState({
                    operateTenantId:operateTenantId
                })
            }
            if (productionLineId) {
                this.setState({
                    operate:"编辑车间",
                    productionLineId:productionLineId
                })
                loadTypeUrl= "/biz/production/line/get";
                loadRequest={'productionLineId':productionLineId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditProductionLineDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增车间",
                })
            }
        }
    }

    loadEditProductionLineDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {

            this.setState({
                productionLineName:response.data.productionLineName,
                kilnRoadName:response.data.kilnRoadName
            })
        }
    }

    saveProductionLine =()=> {
        console.log("=======saveProductionLine");
        let toastOpts;
        if (!this.state.productionLineName) {
            toastOpts = getFailToastOpts("请填写车间名称");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/production/line/add";
        if (this.state.productionLineId) {
            console.log("=========Edit===productionLineId", this.state.productionLineId)
            url= "/biz/production/line/modify";
        }
        let requestParams={
            productionLineId:this.state.productionLineId,
            productionLineName: this.state.productionLineName,
            operateTenantId: this.state.operateTenantId,
            kilnRoadName: this.state.kilnRoadName
        };
        httpPost(url, requestParams, this.saveProductionLineCallBack);
    }

    // 保存回调函数
    saveProductionLineCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("ProductionLineMgrList")
            }}>
                <Text style={CommonStyle.headRightText}>车间管理</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                 <ScrollView style={[CommonStyle.contentViewStyle]}>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>车间名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入车间名称'}
                            onChangeText={(text) => this.setState({productionLineName:text})}
                        >
                            {this.state.productionLineName}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>窑道名称</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入窑道名称'}
                            onChangeText={(text) => this.setState({kilnRoadName:text})}
                        >
                            {this.state.kilnRoadName}
                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveProductionLine.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({

        // contentViewStyle:{
        //     height:screenHeight - 140,
        //     backgroundColor:'#FFFFFF'
        // },
        itemViewStyle:{
            margin:10,  
            padding:15, 
            borderRadius:2,
            backgroundColor:'#FFFFFF'
        },
        selectedItemViewStyle:{
            margin:10,  
            padding:15, 
            borderRadius:2,
            backgroundColor:"#CB4139"
        },
        itemTextStyle:{
            color:'#000000'
        },
        selectedItemTextStyle:{
            color:'#FFFFFF'
        },
        inputRowStyle:{
            height:45,
            flexDirection:'row',
            marginTop:10,
            // flex: 1,
            // justifyContent: 'space-between',
            // alignContent:'center'
            // backgroundColor:'#000FFF',
            // width:screenWidth,
            // alignContent:'space-between',
            // justifyContent:'center'
        },
    
        rowLabView:{
            height:45,
            flexDirection:'row',
            alignItems:'center',
            paddingLeft:10,
            // alignContent:'flex-start',
            // justifyContent:'center',
            // backgroundColor:'yellow',
        },
        leftLabView:{
            width:leftLabWidth,
            height:45,
            flexDirection:'row',
            alignItems:'center',
            paddingLeft:10,
            // alignContent:'flex-start',
            // justifyContent:'center',
            // backgroundColor:'yellow',
        },
        leftLabNameTextStyle:{
            fontSize:18,
            // color:'red',
            // borderColor:'#000',
            // borderWidth:1,
            // justifyContent:'center',
            // alignContent:'center',
            // backgroundColor:'yellow',
        },
        leftLabRedTextStyle:{
            color:'red',
            marginLeft:5,
            marginRight:5
        },
        inputRightText:{
            width:screenWidth - (leftLabWidth + 5),
            borderRadius:5,
            borderColor:'#F1F1F1',
            borderWidth:1,
            marginRight:5,
            color:'#A0A0A0',
            fontSize:15,
            paddingLeft:10,
            paddingRight:10
        }
    





})
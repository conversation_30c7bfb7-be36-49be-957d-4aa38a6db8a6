import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TextInput,TouchableOpacity,ScrollView, Alert,BottomScrollSelect,
    FlatList, RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class PortalStaffMgrAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            staffId: "",
            staffName: "",
            staffSex: "",
            staffTel: "",
            operate: "",
            // staffDataSource:[],
            // selectStaff:[],
            sexDataSource:[
                {
                    sexId:1,
                    sexType:"男",
                    sexName:"M"
                },
                {
                    sexId:2,
                    sexType:"女",
                    sexName:"L"
                }
            ],
            selSexId:""
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { staffId } = route.params;
            if (staffId) {
                this.setState({
                    operate: "编辑",
                    staffId: staffId,
                })
                loadTypeUrl = "/biz/portal/staff/get";
                loadRequest = { 'staffId': staffId };
                httpPost(loadTypeUrl, loadRequest, this.loadPortalStaffMgrCallBack);

            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
        }
    }

    loadPortalStaffMgrCallBack = (response) => {
        if (response.code == 200 && response.data) {
            if(response.data.staffSex == 'M'){
                this.setState({
                    selSexId:1
                })
            }
            if(response.data.staffSex == 'L'){
                this.setState({
                    selSexId:2
                })
            }
            this.setState({
                staffName: response.data.staffName,
                staffSex: response.data.staffSex,
                staffTel: response.data.staffTel,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PortalStaffMgrList",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Text style={CommonStyle.headRightText}>员工管理</Text>
            </TouchableOpacity>
        )
    }

     //sex列表展示
     renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selSexId:item.sexId,
                    })
                }}>
                <View key={item.sexId} style={[item.sexId===this.state.selSexId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.sexId===this.state.selSexId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.sexType}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    savePortalStaff = () => {
        // console.log("=======savePortalStaff",this.state.sexDataSource[this.state.selSexId].sexName);
        let toastOpts;
        if (!this.state.staffName) {
            toastOpts = getFailToastOpts("请填写员工姓名");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selSexId) {
            toastOpts = getFailToastOpts("请选择员工性别");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/portal/staff/add";
        if (this.state.staffId) {
            console.log("=========Edit===staffId", this.state.staffId)
            url = "/biz/portal/staff/modify";
        }
        let requestParams = {
            staffId: this.state.staffId,
            staffName: this.state.staffName,
            staffSex: this.state.sexDataSource[this.state.selSexId-1].sexName,
            staffTel: this.state.staffTel,
        };
        console.log("======requestParams======",requestParams)
        httpPost(url, requestParams, this.savePortalStaffMgrCallBack);
    }

    // 保存回调函数
    savePortalStaffMgrCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '员工'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>员工姓名</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入员工姓名'}
                            onChangeText={(text) => this.setState({ staffName: text })}
                        >
                            {this.state.staffName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.rowLabView}>
                            <Text style={styles.leftLabNameTextStyle}>员工性别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.sexDataSource && this.state.sexDataSource.length > 0) 
                                ? 
                                this.state.sexDataSource.map((item, index)=>{
                                    return this.renderRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>
                    

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入联系电话'}
                            onChangeText={(text) => this.setState({ staffTel: text })}
                        >
                            {this.state.staffTel}
                        </TextInput>
                    </View>


                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.savePortalStaff.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>           
            </View>
        )
    }
}
const styles = StyleSheet.create({
    leftLabNameTextStyle:{
        fontSize:18,
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight:30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }

});
import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Image, TextInput, Modal, ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class MessageRemind extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            remindStateDataSource: [
                {
                    stateCode: null,
                    stateName: '全部',
                },
                {
                    stateCode: '0',
                    stateName: '未读',
                },
                {
                    stateCode: '1',
                    stateName: '已读',
                }
            ],
            selCompletionStateCode: null,
            // 待审核日报
            syDailyAuditTaskDTO: null,
            // 审核完成的日报
            syDailyDTO: null,
            // 成果留言&优秀成果
            syHarvestDTO: null,
            syHarvestDiscussDTO: null,
            // 日报留言
            portalMessageBoardDTO: null,
            portalMessageBoardDailyDTO: null,
            // 留言回复
            portalMessageBoardReplyDTO: null,
            //任务跟踪
            SyPromotionPlanDTO: null,
            //积分奖励
            SyPointRewardDTO: null,
            //考核待审批&考核审批完成
            SyAssessRecordDTO: null,
            //认领提问&取消认领&提问已解决&发布提问
            PortalAskQuestionsDTO: null,
            //课程已完成&课程超时
            SyCourseTaskDTO: null,

            messageFkType: null,

            auditModal: false,
            dailyModal: false,
            harvestDiscussModal: false,
            messageBoardModal: false,
            harvestModal: false,
            promotionPlanModal: false,
            pointRewardModal: false,
            assessRecordModal: false,
            assessRecordFinishModal: false,
            // 认领提问&取消认领&提问已解决
            reciveAskQuestionsModal: false,
            // 发布提问
            reciveAllAskQuestionsModal: false,
            //课程已完成
            syCourseTaskFinishedModal: false,
            //课程超时
            syCourseTaskOutDateModal: false,
            // 留言回复
            replyMessageBoardModal: false,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        console.log('componentWillMount', constants.noReadMessageCount);
        this.loadMessageRemindList();
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { isPersonalCenter } = route.params;
            if (isPersonalCenter) {
                this.setState({
                    isPersonalCenter: isPersonalCenter
                })
            }
        }
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/portal/message/remind/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "messageFlag": this.state.selCompletionStateCode,
            "messageToUserId": constants.loginUser.userId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/portal/message/remind/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "messageFlag": this.state.selCompletionStateCode,
            "messageToUserId": constants.loginUser.userId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
            console.log( JSON.stringify(dataAll, null, 6))
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadMessageRemindList();
    }

    loadMessageRemindList = () => {
        let url = "/biz/portal/message/remind/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "messageFlag": this.state.selCompletionStateCode,
            "messageToUserId": constants.loginUser.userId
        };
        httpPost(url, loadRequest, this.loadMessageRemindListCallBack);
    }

    loadMessageRemindListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
            console.log( JSON.stringify(dataAll, null, 6))
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    updateKilnCarState = (kilnCarId, kilnCarState) => {
        console.log("=======delete=kilnCarId", kilnCarId);
        let url = "/biz/kiln/car/modify";
        let requestParams = { 'kilnCarId': kilnCarId, "kilnCarState": kilnCarState };
        httpPost(url, requestParams, this.updateKilnCarStateCallBack);
    }

    // 更新状态的回调操作
    updateKilnCarStateCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "操作成功" });
            this._loadFreshData();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    renderRow = (item, index) => {
        return (
            <View key={item.messageId} style={styles.innerViewStyle}>
                {/* A：我的待办（待审核） */}
                {
                    item.messageFkType === 'A' ?
                        <View>
                            <View style={[styles.titleViewStyle, {}]}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    我的待办
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{fontSize:2,paddingTop:0.5, paddingBottom:0.5,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {item.syDailyAuditTaskDTO.syDailyDTO.userName + item.syDailyAuditTaskDTO.syDailyDTO.dailyDate}的日报需要您审核，请及时查看！
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* B：日报审核后的消息 */}
                {
                    item.messageFkType === 'B' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    审核完成
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    您{item.syDailyDTO.dailyDate}提交的日报已审核完成
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* C：成果圈留言 */}
                {
                    item.messageFkType === 'C' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    成果留言
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {item.syHarvestDiscussDTO.discussOperatorName}给您的成果留言了
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* D：日报评论 */}
                {
                    item.messageFkType === 'D' ?
                        <View>
                            <View style={[styles.titleViewStyle, {}]}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    日报留言
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* // <Text style={{fontSize:14,paddingTop:0.3, paddingBottom:0.3,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {item.portalMessageBoardDTO.operatorName}给您的日报留言了
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* E：成果评优 */}
                {
                    item.messageFkType === 'E' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    成果评优
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    您的成果被{item.syHarvestDTO.scoreOperator}评为优秀
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* F：任务需跟踪 */}
                {
                    item.messageFkType === 'F' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    任务跟踪
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {item.syPromotionPlanDTO.userName + " " + item.syPromotionPlanDTO.planCreatedTime}的任务指定您关闭，请即时跟踪。
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* G：获得积分奖励 */}
                {
                    item.messageFkType === 'G' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    积分奖励
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    您获得了积分奖励，点击查看详情。
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* HA：考核待审批 */}
                {
                    item.messageFkType === 'HA' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    考核待审批
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {item.syAssessRecordDTO.applyUserName + " " + item.syAssessRecordDTO.applyDate}的考核申请需要您审批，请即时查看！
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* HB：考核审批完成 */}
                {
                    item.messageFkType === 'HB' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    考核审批完成
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    您{item.syAssessRecordDTO.applyDate}的考核申请已完成审批，点击查看详情。
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* IA：发布提问 */}
                {
                    item.messageFkType === 'IA' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                发布提问
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    您的同事发布了一条提问，请及时查看！
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* IB&IC&IF：提问认领&取消认领&提问已解决 */}
                {
                    item.messageFkType === 'IB' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    提问认领
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {
                                        item.portalAskQuestionsDTO
                                        ?
                                        (item.portalAskQuestionsDTO.askQuestionsToClaimUserName + "认领了您" +( item.portalAskQuestionsDTO.gmtCreated ? item.portalAskQuestionsDTO.gmtCreated : "" ) +"的提问，点击查看详情。")
                                        :
                                        ("提问已删除")
                                    }
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {
                    item.messageFkType === 'IC' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    取消认领
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {
                                        item.portalAskQuestionsDTO
                                        ?
                                        (item.portalAskQuestionsDTO.askQuestionsToClaimUserName + "取消认领了您" +( item.portalAskQuestionsDTO.gmtCreated ? item.portalAskQuestionsDTO.gmtCreated : "" ) +"的提问，点击查看详情。")
                                        :
                                        ("提问已删除")
                                    }
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {
                    item.messageFkType === 'IF' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    提问解决
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {
                                        item.portalAskQuestionsDTO
                                        ?
                                        ("您" + ( item.portalAskQuestionsDTO.gmtCreated ? item.portalAskQuestionsDTO.gmtCreated : "" ) +"的提问已解决，点击查看详情。")
                                        :
                                        ("提问已删除")
                                    }
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* JA：课程已完成 */}
                {
                    item.messageFkType === 'JA' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    课程已完成
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {
                                        item.syCourseTaskDTO
                                        ?
                                        ("您第" + ( item.syCourseTaskDTO.courseSort ) +"课 " +item.syCourseTaskDTO.courseName+" 已完成，点击查看详情。")
                                        :
                                        ("课程已删除")
                                    }
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* JB：课程超时*/}
                {
                    item.messageFkType === 'JB' ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    课程超时
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* <Text style={{paddingTop:1, paddingBottom:1,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {
                                        item.syCourseTaskDTO
                                        ?
                                        ("您第" + ( item.syCourseTaskDTO.courseSort ) +"课 " +item.syCourseTaskDTO.courseName+" 已超时，请尽快完成。")
                                        :
                                        ("课程已删除")
                                    }
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }
                {/* MB：留言回复 */}
                {
                    item.messageFkType === 'MB' ?
                        <View>
                            <View style={[styles.titleViewStyle, {}]}>
                                <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>
                                    留言回复
                                </Text>
                                {
                                    item.messageFlag === '0' ?
                                        <Text style={{ position: 'absolute', fontSize: 12, right: 5, top: 5, paddingTop: 0.5, paddingBottom: 0.5, paddingLeft: 5, paddingRight: 5, borderRadius: 100, backgroundColor: '#CB4139', color: '#FFFFFF' }}>
                                            {/* // <Text style={{fontSize:14,paddingTop:0.3, paddingBottom:0.3,  paddingLeft:6, paddingRight:6, borderRadius:100, backgroundColor:'#CB4139',color:'#FFFFFF'}}> */}
                                            {"1"}
                                        </Text>
                                        :
                                        <View></View>
                                    // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                    //     {"已读"}
                                    // </Text>
                                }
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>
                                    {item.portalMessageBoardDTO.operatorName}回复了您的留言
                                </Text>
                            </View>
                        </View>
                        :
                        <View />
                }

                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.gmtCreated?item.gmtCreated:null}</Text>
                </View>

                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                    <TouchableOpacity onPress={() => {
                        console.log("@@@", this.state.PortalAskQuestionsDTO)
                        if (item.messageFkType === 'A') {
                            this.setState({
                                auditModal: true,
                                syDailyAuditTaskDTO: item.syDailyAuditTaskDTO
                            })
                        }
                        else if (item.messageFkType === 'B') {
                            this.setState({
                                dailyModal: true,
                                syDailyDTO: item.syDailyDTO
                            })
                        }
                        else if (item.messageFkType === 'C') {
                            this.setState({
                                harvestDiscussModal: true,
                                syHarvestDiscussDTO: item.syHarvestDiscussDTO
                            })
                            let url = "/biz/harvest/get";
                            let loadRequest = {
                                "harvestId": item.syHarvestDiscussDTO.harvestId
                            };
                            httpPost(url, loadRequest, (response) => {
                                if (response.code == 200 && response.data) {
                                    this.setState({
                                        syHarvestDTO: response.data
                                    })
                                }
                                else if (response.code == 401) {
                                    WToast.show({ data: response.message });
                                    this.props.navigation.navigate("LoginView");
                                }

                            });
                        }
                        else if (item.messageFkType === 'D') {
                            this.setState({
                                messageBoardModal: true,
                                portalMessageBoardDTO: item.portalMessageBoardDTO
                            })
                            let url = "/biz/daily/get";
                            let loadRequest = {
                                "dailyId": item.portalMessageBoardDTO.messageFkId
                            };
                            httpPost(url, loadRequest, (response) => {
                                if (response.code == 200 && response.data) {
                                    this.setState({
                                        portalMessageBoardDailyDTO: response.data
                                    })
                                }
                                else if (response.code == 401) {
                                    WToast.show({ data: response.message });
                                    this.props.navigation.navigate("LoginView");
                                }
                            });
                        }
                        else if (item.messageFkType === 'E') {
                            this.setState({
                                harvestModal: true,
                                syHarvestDTO: item.syHarvestDTO
                            })
                        }
                        //任务跟踪
                        else if (item.messageFkType === 'F') {
                            this.setState({
                                promotionPlanModal: true,
                                SyPromotionPlanDTO: item.syPromotionPlanDTO
                            })
                        }
                        //积分奖励
                        else if (item.messageFkType === 'G') {
                            this.setState({
                                pointRewardModal: true,
                                SyPointRewardDTO: item.syPointRewardDTO
                            })
                        }
                        //考核待审批
                        else if (item.messageFkType === 'HA') {
                            this.setState({
                                assessRecordModal: true,
                                SyAssessRecordDTO: item.syAssessRecordDTO
                            })
                        }
                        //考核审批完成
                        else if (item.messageFkType === 'HB') {
                            this.setState({
                                assessRecordFinishModal: true,
                                SyAssessRecordDTO: item.syAssessRecordDTO
                            })
                        }
                        //发布提问
                        else if (item.messageFkType === 'IA') {
                            this.setState({
                                reciveAllAskQuestionsModal: true,
                                PortalAskQuestionsDTO: item.portalAskQuestionsDTO,
                            })
                        }
                        //提问认领
                        else if (item.messageFkType === 'IB') {
                            this.setState({
                                reciveAskQuestionsModal: true,
                                PortalAskQuestionsDTO: item.portalAskQuestionsDTO,
                                messageFkType: item.messageFkType
                            })
                        }
                        else if (item.messageFkType === 'IC') {
                            this.setState({
                                reciveAskQuestionsModal: true,
                                PortalAskQuestionsDTO: item.portalAskQuestionsDTO,
                                messageFkType: item.messageFkType
                            })
                        }
                        else if (item.messageFkType === 'IF') {
                            this.setState({
                                reciveAskQuestionsModal: true,
                                PortalAskQuestionsDTO: item.portalAskQuestionsDTO,
                                messageFkType: item.messageFkType
                            })
                        }
                        //课程已完成
                        else if (item.messageFkType === 'JA') {
                            this.setState({
                                syCourseTaskFinishedModal: true,
                                SyCourseTaskDTO: item.syCourseTaskDTO,
                                messageFkType: item.messageFkType
                            })
                        }
                        //课程超时
                        else if (item.messageFkType === 'JB') {
                            this.setState({
                                syCourseTaskOutDateModal: true,
                                SyCourseTaskDTO: item.syCourseTaskDTO,
                                messageFkType: item.messageFkType
                            })
                        }
                        //留言回复
                        else if (item.messageFkType === 'MB') {
                            this.setState({
                                replyMessageBoardModal: true,
                                portalMessageBoardDTO: item.portalMessageBoardDTO
                            })
                            let url = "/biz/daily/get";
                            let loadRequest = {
                                "dailyId": item.portalMessageBoardDTO.messageFkId
                            };
                            httpPost(url, loadRequest, (response) => {
                                if (response.code == 200 && response.data) {
                                    this.setState({
                                        portalMessageBoardDailyDTO: response.data
                                    })
                                }
                                else if (response.code == 401) {
                                    WToast.show({ data: response.message });
                                    this.props.navigation.navigate("LoginView");
                                }
                            });
                        }

                        if (item.messageFlag === '0') {
                            let url = "/biz/portal/message/remind/modify";
                            let loadRequest = {
                                "messageId": item.messageId,
                                "messageFlag": '1',
                                "messageToUserId": constants.loginUser.userId,
                                "messageToType": item.messageToType,
                            };
                            httpPost(url, loadRequest, (response) => {
                                if (response.code == 200 && response.data) {
                                    let list = this.state.dataSource;
                                    list.map((elem, index) => {
                                        if (elem.messageId == item.messageId) {
                                            elem.messageFlag = "1"
                                        }
                                    })
                                    this.setState({
                                        dataSource: list
                                    })
                                    constants.noReadMessageCount = constants.noReadMessageCount - 1;
                                }
                                else if (response.code == 401) {
                                    WToast.show({ data: response.message });
                                    this.props.navigation.navigate("LoginView");
                                }

                            });
                        }
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { marginTop: 0, borderColor: "#3ab240", borderWidth: 1, backgroundColor: 'rgba(0,0,0,0)', width: 75, flexDirection: "row" }]}>
                            <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/detailGreen.png')}></Image>
                            <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { color: "#3ab240" }]}>详情</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.isPersonalCenter == "Y") {
                    if (this.props.route.params.refresh) {
                        this.props.route.params.refresh()
                    }
                }
                this.props.navigation.goBack()
            }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    renderRemindStateRow = (item, index) => {
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={() => {
                    let selCompletionStateCode = item.stateCode;
                    this.setState({
                        selCompletionStateCode: selCompletionStateCode
                    })
                    let loadUrl = "/biz/portal/message/remind/list";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "messageFlag": selCompletionStateCode,
                        "messageToUserId": constants.loginUser.userId
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View
                        key={item.stateCode}
                        style={[item.stateCode === this.state.selCompletionStateCode ? [CommonStyle.selectedBlockItemViewStyle, { borderBottomWidth: 0, borderBottomColor: "#CB4139" }] : [CommonStyle.blockItemViewStyle, {}], { paddingLeft: 8, paddingRight: 8 }]}
                    >
                        <Text style={[item.stateCode === this.state.selCompletionStateCode ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='我的消息'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle, { width: screenWidth, marginTop: 0, flexDirection: 'row' }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.remindStateDataSource && this.state.remindStateDataSource.length > 0)
                                ?
                                this.state.remindStateDataSource.map((item, index) => {
                                    return this.renderRemindStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                    <View style={{ marginTop: 0, index: 1000, flexDirection: 'row-reverse', flex: 1 }}>
                        <TouchableOpacity onPress={() => {
                            console.log("全部已读")
                            let loadUrl = "/biz/portal/message/remind/readAll";
                            let loadRequest = {
                                "messageToUserId": constants.loginUser.userId,
                            };
                            httpPost(loadUrl, loadRequest, (response)=>{
                                if (response.code == 200 ) {
                                    toastOpts = getSuccessToastOpts('全部已读');
                                    WToast.show(toastOpts);
                                    
                                    this.setState({
                                        currentPage: 1
                                    })
                                    let url = "/biz/portal/message/remind/list";
                                    let loadRequest = {
                                        "currentPage": 1,
                                        "pageSize": this.state.pageSize,
                                        "messageFlag": this.state.selCompletionStateCode,
                                        "messageToUserId": constants.loginUser.userId
                                    };
                                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                }
                                else if (response.code == 401) {
                                    WToast.show({ data: response.message });
                                    this.props.navigation.navigate("LoginView");
                                }
                            });
                        }}>
                            <View
                                style={CommonStyle.selectedBlockItemViewStyle}
                            >
                                <Text style={CommonStyle.selectedBlockItemTextStyle16}>
                                    {"全部已读"}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
                {/* 待审核日报 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.auditModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>待审核日报</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        提交日期：{this.state.syDailyAuditTaskDTO ? this.state.syDailyAuditTaskDTO.syDailyDTO.dailyDate : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        提交人：{this.state.syDailyAuditTaskDTO ? this.state.syDailyAuditTaskDTO.syDailyDTO.userName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        完成工作：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.syDailyAuditTaskDTO ? this.state.syDailyAuditTaskDTO.syDailyDTO.finishedWork : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        工作计划：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.syDailyAuditTaskDTO ? this.state.syDailyAuditTaskDTO.syDailyDTO.workPlan : ""}
                                    </Text>
                                </View>
                                {
                                    this.state.syDailyAuditTaskDTO && this.state.syDailyAuditTaskDTO.syDailyDTO.unfinishedWork && this.state.syDailyAuditTaskDTO.syDailyDTO.unfinishedWork != "" ?
                                        <View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    未完成工作：
                                                </Text>
                                            </View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    {this.state.syDailyAuditTaskDTO ? this.state.syDailyAuditTaskDTO.syDailyDTO.unfinishedWork : ""}
                                                </Text>
                                            </View>
                                        </View>
                                        : <View></View>
                                }
                                {
                                    this.state.syDailyAuditTaskDTO && this.state.syDailyAuditTaskDTO.syDailyDTO.requiresCoordinationWork && this.state.syDailyAuditTaskDTO.syDailyDTO.requiresCoordinationWork != "" ?
                                        <View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    需协调工作：
                                                </Text>
                                            </View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    {this.state.syDailyAuditTaskDTO ? this.state.syDailyAuditTaskDTO.syDailyDTO.requiresCoordinationWork : ""}
                                                </Text>
                                            </View>
                                        </View>
                                        : <View></View>
                                }
                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        auditModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 审核完成的日报 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.dailyModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>审核完成的日报</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        提交日期：{this.state.syDailyDTO ? this.state.syDailyDTO.dailyDate : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        完成工作：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.syDailyDTO ? this.state.syDailyDTO.finishedWork : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        工作计划：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.syDailyDTO ? this.state.syDailyDTO.workPlan : ""}
                                    </Text>
                                </View>
                                {
                                    this.state.syDailyDTO && this.state.syDailyDTO.unfinishedWork && this.state.syDailyDTO.unfinishedWork != "" ?
                                        <View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    未完成工作：
                                                </Text>
                                            </View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    {this.state.syDailyDTO ? this.state.syDailyDTO.unfinishedWork : ""}
                                                </Text>
                                            </View>
                                        </View>
                                        : <View></View>
                                }

                                {
                                    this.state.syDailyDTO && this.state.syDailyDTO.requiresCoordinationWork && this.state.syDailyDTO.requiresCoordinationWork != "" ?
                                        <View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    需协调工作：
                                                </Text>
                                            </View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    {this.state.syDailyDTO ? this.state.syDailyDTO.requiresCoordinationWork : ""}
                                                </Text>
                                            </View>
                                        </View>
                                        : <View></View>
                                }
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        审核人：{this.state.syDailyDTO ? this.state.syDailyDTO.auditOperator : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        审核得分：{this.state.syDailyDTO ? this.state.syDailyDTO.auditScore : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        审核意见：{this.state.syDailyDTO && this.state.syDailyDTO.auditOpinion ? this.state.syDailyDTO.auditOpinion : "无"}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        审核时间：{this.state.syDailyDTO ? this.state.syDailyDTO.auditTime : ""}
                                    </Text>
                                </View>
                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        dailyModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 成果留言 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.harvestDiscussModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>成果留言</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        标题：{this.state.syHarvestDTO ? this.state.syHarvestDTO.harvestTitle : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        内容：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.syHarvestDTO ? this.state.syHarvestDTO.harvestContent : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        留言人：{this.state.syHarvestDiscussDTO ? this.state.syHarvestDiscussDTO.discussOperatorName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        留言内容：{this.state.syHarvestDiscussDTO ? this.state.syHarvestDiscussDTO.discussContent : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        留言时间：{this.state.syHarvestDiscussDTO ? this.state.syHarvestDiscussDTO.gmtCreated : ""}
                                    </Text>
                                </View>

                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        harvestDiscussModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 日报留言 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.messageBoardModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>日报留言</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        提交日期：{this.state.portalMessageBoardDailyDTO ? this.state.portalMessageBoardDailyDTO.dailyDate : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        提交人：{this.state.portalMessageBoardDailyDTO ? this.state.portalMessageBoardDailyDTO.userName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        完成工作：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.portalMessageBoardDailyDTO ? this.state.portalMessageBoardDailyDTO.finishedWork : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        工作计划：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.portalMessageBoardDailyDTO ? this.state.portalMessageBoardDailyDTO.workPlan : ""}
                                    </Text>
                                </View>
                                {
                                    this.state.portalMessageBoardDailyDTO && this.state.portalMessageBoardDailyDTO.unfinishedWork && this.state.portalMessageBoardDailyDTO.unfinishedWork != "" ?
                                        <View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    未完成工作：
                                                </Text>
                                            </View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    {this.state.portalMessageBoardDailyDTO ? this.state.portalMessageBoardDailyDTO.unfinishedWork : ""}
                                                </Text>
                                            </View>
                                        </View>
                                        : <View></View>
                                }

                                {
                                    this.state.portalMessageBoardDailyDTO && this.state.portalMessageBoardDailyDTO.requiresCoordinationWork && this.state.portalMessageBoardDailyDTO.requiresCoordinationWork != "" ?
                                        <View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    需协调工作：
                                                </Text>
                                            </View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>
                                                    {this.state.portalMessageBoardDailyDTO ? this.state.portalMessageBoardDailyDTO.requiresCoordinationWork : ""}
                                                </Text>
                                            </View>
                                        </View>
                                        : <View></View>
                                }

                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        留言人：{this.state.portalMessageBoardDTO ? this.state.portalMessageBoardDTO.operatorName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        留言内容：{this.state.portalMessageBoardDTO ? this.state.portalMessageBoardDTO.messageContent : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        留言时间：{this.state.portalMessageBoardDTO ? this.state.portalMessageBoardDTO.gmtCreated : ""}
                                    </Text>
                                </View>
                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        messageBoardModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 优秀成果 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.harvestModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>优秀成果</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        标题：{this.state.syHarvestDTO ? this.state.syHarvestDTO.harvestTitle : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        内容：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.syHarvestDTO ? this.state.syHarvestDTO.harvestContent : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        操作人：{this.state.syHarvestDTO ? this.state.syHarvestDTO.scoreOperator : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        评优时间：{this.state.syHarvestDTO ? this.state.syHarvestDTO.scoreTime : ""}
                                    </Text>
                                </View>

                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        harvestModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 任务跟踪 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.harvestModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>优秀成果</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        标题：{this.state.syHarvestDTO ? this.state.syHarvestDTO.harvestTitle : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        内容：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.syHarvestDTO ? this.state.syHarvestDTO.harvestContent : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        操作人：{this.state.syHarvestDTO ? this.state.syHarvestDTO.scoreOperator : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        评优时间：{this.state.syHarvestDTO ? this.state.syHarvestDTO.scoreTime : ""}
                                    </Text>
                                </View>

                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        harvestModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* F：任务需跟踪 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.promotionPlanModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>任务跟踪</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        标题：{this.state.SyPromotionPlanDTO ? this.state.SyPromotionPlanDTO.planTitle : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        所属类别：{this.state.SyPromotionPlanDTO ? this.state.SyPromotionPlanDTO.belongClassName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        内容：{this.state.SyPromotionPlanDTO ? this.state.SyPromotionPlanDTO.planContent : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        计划完成时间：{this.state.SyPromotionPlanDTO ? this.state.SyPromotionPlanDTO.plannedCompletionTime : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        提交人：{this.state.SyPromotionPlanDTO ? this.state.SyPromotionPlanDTO.userName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        创建时间：{this.state.SyPromotionPlanDTO ? this.state.SyPromotionPlanDTO.planCreatedTime : ""}
                                    </Text>
                                </View>
                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        promotionPlanModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* G：获得积分奖励 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.pointRewardModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>积分奖励</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        积分描述：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.SyPointRewardDTO ? this.state.SyPointRewardDTO.rwardPointDesc : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        获得积分：{this.state.SyPointRewardDTO ? this.state.SyPointRewardDTO.rewardPointValue : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        奖励人：{this.state.SyPointRewardDTO ? this.state.SyPointRewardDTO.rewardUserName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        奖励时间：{this.state.SyPointRewardDTO ? this.state.SyPointRewardDTO.gmtCreated : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        我的积分：{this.state.SyPointRewardDTO ? this.state.SyPointRewardDTO.pointTotalValue : ""}
                                    </Text>
                                </View>
                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        pointRewardModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* HA：考核待审批 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.assessRecordModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>考核待审批</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        申请日期：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.applyDate : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        申请人：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.applyUserName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        考核标题：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.assessTitle : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        考核内容：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.assessContent : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        预计考核时间：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.expectAssessDate : ""}
                                    </Text>
                                </View>
                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        assessRecordModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* HB：考核审批完成 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.assessRecordFinishModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>考核审批完成</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        申请日期：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.applyDate : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        申请人：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.applyUserName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        考核标题：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.assessTitle : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        考核内容：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.assessContent : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        考核难度：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.assessDifficultyName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        考核人：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.assessUserName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        预计考核时间：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.expectAssessDate : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        实际考核时间：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.actualAssessDate : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        考核结果：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.assessResultName : ""}
                                    </Text>
                                </View>
                                {
                                    (this.state.SyAssessRecordDTO != null && this.state.SyAssessRecordDTO.assessOpinion) ?
                                        <View style={styles.titleViewStyle}>
                                            <Text style={styles.titleTextStyle}>
                                                考核意见：{this.state.SyAssessRecordDTO ? this.state.SyAssessRecordDTO.assessOpinion : ""}
                                            </Text>
                                        </View>
                                        :
                                        <View />
                                }

                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        assessRecordFinishModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* IA：发布提问 */}                
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.reciveAllAskQuestionsModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, { height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff', borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10 }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>
                                    发布提问
                                </Text>
                            </View>
                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        标题：{this.state.PortalAskQuestionsDTO ? this.state.PortalAskQuestionsDTO.askQuestionsTitle : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        内容：{this.state.PortalAskQuestionsDTO ? this.state.PortalAskQuestionsDTO.askQuestionsContent : ""}
                                    </Text>
                                </View>
                                {/* <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        附件：{this.state.PortalAskQuestionsDTO ?  "请进入“提问查询”查看":"无"}
                                    </Text>
                                </View> */}
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        提问人：{this.state.PortalAskQuestionsDTO ? (this.state.PortalAskQuestionsDTO.askQuestionsUserName ? this.state.PortalAskQuestionsDTO.askQuestionsUserName : "") : ""}
                                    </Text>
                                </View>                                
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        提问时间：{this.state.PortalAskQuestionsDTO ? (this.state.PortalAskQuestionsDTO.gmtCreated ? this.state.PortalAskQuestionsDTO.gmtCreated : "") : ""}
                                    </Text>
                                </View>
                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        reciveAllAskQuestionsModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* IB&IC&IF：提问认领&取消认领&提问已解决 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.reciveAskQuestionsModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, { height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff', borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10 }]}>
                                {
                                    this.state.messageFkType === 'IB' ?
                                        <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>
                                            提问认领
                                        </Text>
                                        :

                                        this.state.messageFkType === 'IC' ?
                                            <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>
                                                取消认领
                                            </Text>
                                            :
                                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>
                                                    提问解决
                                                </Text>

                                }
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        标题：{this.state.PortalAskQuestionsDTO ? this.state.PortalAskQuestionsDTO.askQuestionsTitle : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        内容：{this.state.PortalAskQuestionsDTO ? this.state.PortalAskQuestionsDTO.askQuestionsContent : ""}
                                    </Text>
                                </View>
                                {/* <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        附件：{this.state.PortalAskQuestionsDTO ?  "请进入“提问查询”查看":"无"}
                                    </Text>
                                </View> */}
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        提问时间：{this.state.PortalAskQuestionsDTO ? (this.state.PortalAskQuestionsDTO.gmtCreated ? this.state.PortalAskQuestionsDTO.gmtCreated : "") : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    {
                                        this.state.messageFkType === 'IC' ?
                                            <Text />
                                            :
                                            <Text style={styles.titleTextStyle}>
                                                认领人：{this.state.PortalAskQuestionsDTO ? this.state.PortalAskQuestionsDTO.askQuestionsUserName : ""}
                                            </Text>
                                    }
                                </View>
                                <View style={styles.titleViewStyle}>
                                    {this.state.messageFkType === 'IF' ?
                                        <Text style={styles.titleTextStyle}>
                                            解决时间：{this.state.PortalAskQuestionsDTO ? this.state.PortalAskQuestionsDTO.gmtModified : ""}
                                        </Text>
                                        :
                                        <Text />
                                    }
                                </View>

                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        reciveAskQuestionsModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* JA：课程已完成 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.syCourseTaskFinishedModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, { height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff', borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10 }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>
                                    课程已完成
                                </Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        课程名称：{this.state.SyCourseTaskDTO ? this.state.SyCourseTaskDTO.courseName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        内容：{this.state.SyCourseTaskDTO ? this.state.SyCourseTaskDTO.courseContent : ""}
                                    </Text>
                                </View>

                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                    开始学习时间：{this.state.SyCourseTaskDTO ? (this.state.SyCourseTaskDTO.gmtCreated ? this.state.SyCourseTaskDTO.gmtCreated : "") : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                    实际完成时间：{this.state.SyCourseTaskDTO ? (this.state.SyCourseTaskDTO.actualCompletionTime ? this.state.SyCourseTaskDTO.actualCompletionTime : "") : ""}
                                    </Text>
                                </View>

                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        syCourseTaskFinishedModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* JB：课程超时*/}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.syCourseTaskOutDateModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, { height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff', borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10 }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>
                                    课程超时
                                </Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        课程名称：{this.state.SyCourseTaskDTO ? this.state.SyCourseTaskDTO.courseName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        内容：{this.state.SyCourseTaskDTO ? this.state.SyCourseTaskDTO.courseContent : ""}
                                    </Text>
                                </View>

                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                    开始学习时间：{this.state.SyCourseTaskDTO ? (this.state.SyCourseTaskDTO.gmtCreated ? this.state.SyCourseTaskDTO.gmtCreated : "") : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                    计划完成时间：{this.state.SyCourseTaskDTO ? (this.state.SyCourseTaskDTO.planCompletionTime ? this.state.SyCourseTaskDTO.planCompletionTime : "") : ""}
                                    </Text>
                                </View>

                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        syCourseTaskOutDateModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 留言回复 */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.replyMessageBoardModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle, {
                                height: 40, borderWidth: 1.5, borderColor: "#3ab240", backgroundColor: '#ffffff',
                                borderRadius: 5, justifyContent: 'center', alignItems: 'center', marginTop: 10
                            }]}>
                                <Text style={[styles.titleTextStyle, { fontSize: 18, fontWeight: 'bold', color: "#3ab240" }]}>留言回复</Text>
                            </View>

                            <ScrollView>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        留言时间：{this.state.portalMessageBoardDTO ? this.state.portalMessageBoardDTO.parentGmtCreated : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        留言人：{this.state.portalMessageBoardDTO ? this.state.portalMessageBoardDTO.parentUserName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        留言内容：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.portalMessageBoardDTO ? this.state.portalMessageBoardDTO.parentMessageContent : ""}
                                    </Text>
                                </View>

                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        回复人：{this.state.portalMessageBoardDTO ? this.state.portalMessageBoardDTO.operatorName : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        回复内容：
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        {this.state.portalMessageBoardDTO ? this.state.portalMessageBoardDTO.messageContent : ""}
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>
                                        回复时间：{this.state.portalMessageBoardDTO ? this.state.portalMessageBoardDTO.gmtCreated : ""}
                                    </Text>
                                </View>
                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        replyMessageBoardModal: false
                                    })
                                    if (this.state.selCompletionStateCode === '0') {
                                        let url = "/biz/portal/message/remind/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "messageFlag": '0',
                                            "messageToUserId": constants.loginUser.userId
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }
                                }}>
                                    <View style={[styles.btnRowLeftCancelBtnView, { height: 35 }]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', fontSize: 18, color: '#a1a1a1' }]}>返       回</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#FFFFFF",
        backgroundColor: "#FFFFFF",
        borderRadius: 5,
        marginTop: 5
    },
    btnRowLeftCancelBtnView: {
        flexDirection: 'row',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#a1a1a1',
        borderRadius: 5,
        height: 40,
    },
    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth - 100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop: 0
    },

    innerViewStyle: {
        // marginTop:10,
        // borderColor:"#F4F4F4",
        // borderWidth:14,
        borderColor: "#F4F4F4",
        borderWidth: 8,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleListViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 20,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    }, titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 20,
        marginTop: 15,
        fontSize: 16
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginTop: 10
    },
    itemContentLeftChildViewStyle: {
        flexDirection: 'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width: screenWidth - 180,
        marginLeft: 20
    },
    itemContentRightChildViewStyle: {
        flexDirection: 'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width: 120,
        marginLeft: -20
    },
});
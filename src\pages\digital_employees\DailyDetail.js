import React,{Component} from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,TextInput,
    FlatList, RefreshControl, Linking, Clipboard, Image, Modal,ScrollView,KeyboardAvoidingView, ImageBackground
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
// import EmptyListComponent from '../../component/EmptyRowViewComponent';
// import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

import moment from 'moment';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
var currentDate = (moment(new Date()).format('YYYY-MM-DD'));
export default class DailyDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,

            showSearchItemBlock: false,
            saveMessage: "",

            dailyId: "",
            userId: "",
            userName: "",
            auditOperator:'',
            auditScore: null,
            editAuditScore: null,
            auditOpinion:'',
            auditTime:'',
            dailyDate:'',
            dailyState:'',
            gmtCreated:'',
            finishedWork:'',
            unfinishedWork:'',
            requiresCoordinationWork:'',
            workPlan:'',
            departmentId:'',
            departmentName:'',
            userPhoto: "",
            userPhotoUrl: "",
            auditUserPhoto: "",
            operatorPhotoUrl:'',
            messageBoardList:[],
            messageId:null,
            auditModal:false,
            moreModal:false,
            deleteModal: false,
            messageModal: false,
            backlogState: false,
            doneState: false,
            messageFkId: "",
            parentMessageId:"",
            messageContent: "",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { dailyId, userName, my_backlog, my_done } = route.params;
            if (dailyId) {
                console.log('dailyId================', dailyId)
                this.setState({
                    dailyId: dailyId,
                    userName: userName
                })
                loadTypeUrl = "/biz/daily/get";
                loadRequest = { 'dailyId': dailyId };
                httpPost(loadTypeUrl, loadRequest, this.loadDailyDataCallBack);
            }
            if (my_backlog) {
                console.log('my_backlog================', my_backlog)
                this.setState({
                    backlogState: true
                })
            }
            if (my_done) {
                console.log('my_done================', my_done)
                this.setState({
                    doneState: true
                })
            }
            this.loadDailyAuditTask(dailyId);
            this.loadMessageBoardList(dailyId);
        }
    }

    loadDailyDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            console.log('response.data=========================', response.data)
            console.log('++++++++++++++' , constants.loginUser.userId);
            console.log('++',response.data.userId);
            this.setState({
                userId: response.data.userId,
                userName: response.data.userName,
                auditOperator: response.data.auditOperator,
                auditScore: response.data.auditScore,
                auditOpinion: response.data.auditOpinion,
                auditTime: response.data.auditTime,
                dailyDate: response.data.dailyDate,
                dailyState : response.data.dailyState,
                gmtCreated: response.data.gmtCreated,
                finishedWork: response.data.finishedWork,
                unfinishedWork: response.data.unfinishedWork,
                requiresCoordinationWork: response.data.requiresCoordinationWork,
                workPlan: response.data.workPlan,
                departmentId: response.data.departmentId,
                departmentName: response.data.departmentName,
                userPhoto: response.data.userPhoto,
            })
        }
    }

    loadDailyAuditTask = (dailyId) =>{
        let url = "/biz/daily/audit/task/list";
        let loadRequest = {
            "dailyId": dailyId
        }
        httpPost(url, loadRequest, this.loadDailyAuditTaskCallBack);
    }

    loadDailyAuditTaskCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var auditTaskData = response.data.dataList;
            this.setState({
                auditUserPhoto: auditTaskData[0].auditUserPhoto,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadMessageBoardList = (dailyId) =>{
        let url = "/biz/portal/message/board/allList";
        let loadRequest = {
            "messageFkType":"D",
            "messageFkId":dailyId,
        }
        httpPost(url, loadRequest, this.loadMessageBoardListCallBack);
    }

    loadMessageBoardListCallBack = (response) => {
        // console.log("1"+response.data);
        if (response.code == 200 && response.data && response.data) {
            console.log("2");
            console.log("messageBoardList=========",response.data);
            this.setState({
                messageBoardList:response.data,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    //头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    moreModal: true,
                })
            }}>
                <Image style={{ width: 28, height: 28}} source={require('../../assets/icon/iconfont/more.png')}></Image>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    // 删除日报
    deleteDaily = (dailyId) => {
        console.log("=======delete=dailyId", dailyId);
        let url = "/biz/daily/delete";
        let requestParams = { 'dailyId': dailyId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            if (this.props.route.params.refresh) {
                this.props.route.params.refresh();
            }
            WToast.show({ data: "删除完成" });
            this.props.navigation.goBack()
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    // 保存留言
    saveDailyMessage =()=> {
        console.log("=======saveDailyMessage");
        let toastOpts;
        if (!this.state.messageContent) {
            toastOpts = getFailToastOpts("请输入留言内容");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/portal/message/board/add";
        let requestParams={
            messageContent:this.state.messageContent,
            messageFkId: this.state.dailyId,
            parentMessageId: this.state.parentMessageId,
            messageFkType:"D"
        };
        httpPost(url, requestParams, this.saveDailyMessageCallBack);
    }
    
    // 保存留言的回调函数
    saveDailyMessageCallBack=(response)=>{
        this.setState({
            messageContent: "",
            parentMessageId:""
        })
        let toastOpts;
        switch (response.code) {
            case 200:
                WToast.show({ data: "留言发送成功" });
                this.loadMessageBoardList(this.state.dailyId);
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    
    // 审核日报
    auditDaily =()=> {
        console.log("=======auditDaily");
        let toastOpts;
        if (!this.state.editAuditScore && this.state.editAuditScore !==0) {
            toastOpts = getFailToastOpts("请输入审核得分");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/daily/audit";
        let requestParams={
            dailyId: this.state.dailyId,
            auditScore: this.state.editAuditScore,
            auditOpinion: this.state.auditOpinion,
        };
        httpPost(url, requestParams, this.saveAuditCallBack);
    }
    
    // 审核日报回调函数
    saveAuditCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('审核完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return (
            <View style={{height:screenHeight}}>
                <CommonHeadScreen title={this.state.userName + "的日报"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <ScrollView style={[CommonStyle.formContentViewStyle]} >

                    {/* 日报顶部信息 */}
                    <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
                        {
                            this.state.userPhoto ?
                                <Image source={{ uri: (constants.image_addr + '/' + this.state.userPhoto) }} style={{ height: 48, width: 48, borderRadius: 50}} />
                                :
                                <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                    <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                        {
                                            this.state.userName.length <= 2 ? 
                                            <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                {this.state.userName}
                                            </Text>
                                            :
                                            <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                {this.state.userName.slice(-2)}
                                            </Text>
                                        }
                                    </View>
                                </ImageBackground>
                        }
                        
                        <View style={{marginLeft:11, flexDirection: 'column'}}>
                            <View style={{flexDirection: 'row', marginTop: 4 }}>
                                <View style={{ flexDirection: 'row' }}>
                                    <Text style={{ fontSize: 16 }}>{this.state.userName}的日报</Text>
                                </View>

                                <View style={{flexDirection: 'row'}}>
                                    {
                                        this.state.auditScore !== null || this.state.auditScore == 0 ?
                                            <View>
                                                <View style={{ width: 68, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#1BBC82' }}>
                                                    <Text style={{fontSize: 13, color: '#FFFFFF' }}>审核通过</Text>
                                                </View>
                                            </View>
                                            :
                                            <View>
                                                {
                                                    this.state.dailyState === "0BB" ?
                                                        <View style={{ width: 44, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: '#E63633'}}>
                                                            <Text style={{ height: 17, width: 36, color: '#FFFFFF', textAlign: 'center'}}>草稿</Text>
                                                        </View>
                                                        :
                                                        <View style={{ width: 52, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: '#FF8C28' }}>
                                                            <Text style={{fontSize: 13, color: '#FFFFFF' }}>待审核</Text>
                                                        </View>
                                                }
                                            </View>
                                    }
                                </View>
                            </View>

                            <View style={{flexDirection: 'row'}}>
                            <Image style={{ height: 13 , width: 12, marginTop: 5, marginLeft: 1, marginRight: 5}} source={require('../../assets/icon/iconfont/clock.png')}></Image> 
                                <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                                    <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)' }]}>{this.state.dailyDate} 提交</Text>
                                </View>
                            </View>
                        </View>
                        {
                            this.state.auditScore !== null || this.state.auditScore == 0 ? 
                                <View style={{ flexDirection: 'column', justifyContent:'center', alignContent: 'center', position: 'absolute', right: 52, top: 0}}>
                                    <Text style={{height: 30, color: "#FC0000", fontSize: 24, flexDirection: 'row', alignItems: 'center', fontWeight: '600', paddingLeft: 5}}>{this.state.auditScore}</Text>
                                    <Image style={{width: 46,height:14}} source={require('../../assets/icon/iconfont/scoreLine2.png')}></Image>
                                </View>
                                :
                                <View/>
                        }
                    </View>

                    {/* 分隔线 */}
                    <View style={styles.lineViewStyle}/>
                    
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>完成的工作</Text>
                    </View>
                    <View style={styles.itemContentTextStyle}>
                        <Text style={styles.itemContentStyle}>{this.state.finishedWork}</Text>
                    </View>
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>工作计划</Text>
                    </View>
                    <View style={[styles.itemContentTextStyle, { marginBottom: 5 }]}>
                        <Text style={styles.itemContentStyle}>{this.state.workPlan}</Text>
                    </View>
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>未完成工作</Text>
                    </View>
                    <View style={styles.itemContentTextStyle}>
                        <Text style={styles.itemContentStyle}>{this.state.unfinishedWork ? this.state.unfinishedWork:"-"}</Text>
                    </View>
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>需协调工作</Text>
                    </View>
                    <View style={styles.itemContentTextStyle}>
                        <Text style={styles.itemContentStyle}>{this.state.requiresCoordinationWork? this.state.requiresCoordinationWork:"-"}</Text>
                    </View>

                    <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                        {
                            this.state.backlogState  || this.state.doneState ?
                            <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        messageModal: true
                                    })
                                }}>
                                    <View style={[{width: 78, height: 28, flexDirection: "row", alignItems: 'center', margin: 10, 
                                        marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                                    }]}>
                                        <Image style={{ width: 20, height: 20, marginRight: 8, marginLeft: 12 }} source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.65)', fontSize: 14, lineHeight: 24 }]}>留言</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        auditModal:true
                                    })
                                }}>
                                    <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{flex: 1,width: 64, height: 32, backgroundColor: '#1E6EFA', borderRadius: 20 }]}>
                                        <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { textAlign: 'center', fontSize: 14 }]}>审核</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            :
                            <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        messageModal: true
                                    })
                                }}>
                                    <View style={[{width: 78, height: 28, flexDirection: "row", alignItems: 'center', margin: 10, 
                                        marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                                    }]}>
                                        <Image style={{ width: 20, height: 20, marginRight: 8, marginLeft: 12 }} source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.65)', fontSize: 14, lineHeight: 24 }]}>留言</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        }
                    </View>
              
                    {/* 分隔线 */}
                    <View style={styles.lineViewStyle}/>

                    {/* 流程 */}
                    <View style={[styles.titleViewStyle, { marginLeft: 21, alignItems: 'center' }]}>
                        <Text style={styles.titleTextStyle}>流程</Text>
                    </View>

                    <View style={{flexDirection: 'row', marginLeft: 21, marginTop: 14, marginRight: 16, marginBottom: 30}}>
                        {
                            this.state.userPhoto ?
                                <Image source={{ uri: (constants.image_addr + '/' + this.state.userPhoto) }} style={{ height: 48, width: 48, borderRadius: 50}} />
                                :
                                <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                    <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                        {
                                            this.state.userName.length <= 2 ? 
                                            <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                {this.state.userName}
                                            </Text>
                                            :
                                            <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                {this.state.userName.slice(-2)}
                                            </Text>
                                        }
                                    </View>
                                </ImageBackground>
                        }
                        
                        <View style={{marginLeft:8, flexDirection: 'column'}}>
                            <View style={{flexDirection: 'row', marginTop: 1}}>
                                <Text style={{ fontSize: 16, lineHeight: 20, color: 'rgba(0, 10, 32, 0.85)' }}>提交日报</Text>
                            </View>

                            <View style={{flexDirection: 'row', marginTop: 4}}>
                                <Text style={{fontSize: 14, lineHeight: 20, color: 'rgba(0, 10, 32, 0.65)' }}>{this.state.userName}</Text>
                            </View>
                        </View>

                        <View style={{flexDirection: 'row', position: 'absolute', right: 0, top: 5}}>
                            <Text style={[{fontSize: 12, color:'rgba(0,10,32,0.45)'}]}>{this.state.gmtCreated.slice(0,16)}</Text>
                        </View>
                    </View>
                    {
                        this.state.auditScore !== null || this.state.auditScore == 0 ?
                            <View style={{flexDirection: 'row', marginLeft: 21, marginRight: 16, marginBottom: 4}}>
                                {
                                    this.state.auditUserPhoto ?
                                        <Image source={{ uri: (constants.image_addr + '/' + this.state.auditUserPhoto) }} style={{ height: 48, width: 48, borderRadius: 50}} />
                                        :
                                        <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                            <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                                {
                                                    this.state.auditOperator.length <= 2 ? 
                                                    <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                        {this.state.auditOperator}
                                                    </Text>
                                                    :
                                                    <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                        {this.state.auditOperator.slice(-2)}
                                                    </Text>
                                                }
                                            </View>
                                        </ImageBackground>
                                }

                                <View style={{marginLeft:8, flexDirection: 'column', flex: 1}}>
                                    <View style={{flexDirection: 'row', marginTop: 1}}>
                                        <Text style={{fontSize: 14, lineHeight: 20, color: 'rgba(0, 10, 32, 0.65)' }}>{this.state.auditOperator}（审核通过）</Text>
                                    </View>

                                    <View style={[{marginTop: 9, flex: 1, backgroundColor: 'rgba(242, 245, 252, 0.5)', borderRadius: 10, paddingLeft: 12, paddingTop: 5, paddingRight: 18, paddingBottom: 5}]}>
                                        <Text style={{fontSize: 13, lineHeight: 24, color:'rgba(0,10,32,0.65)'}}>{this.state.auditOpinion ? this.state.auditOpinion : "无审核意见"}</Text>
                                    </View>
                                </View>

                                <View style={{flexDirection: 'row', position: 'absolute', right: 0, top: 5}}>
                                    <Text style={[{fontSize: 12, color:'rgba(0,10,32,0.45)'}]}>{this.state.auditTime.slice(0,16)}</Text>
                                </View>
                            </View>
                            :
                            <View style={{flexDirection: 'row', marginLeft: 21, marginTop: 11, marginRight: 16}}>
                                {
                                    this.state.auditUserPhoto ?
                                        <Image source={{ uri: (constants.image_addr + '/' + this.state.auditUserPhoto) }} style={{ height: 48, width: 48, borderRadius: 50}} />
                                        :
                                        <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                            <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                                {
                                                    this.state.auditOperator.length <= 2 ? 
                                                    <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                        {this.state.auditOperator}
                                                    </Text>
                                                    :
                                                    <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                        {this.state.auditOperator.slice(-2)}
                                                    </Text>
                                                }
                                            </View>
                                        </ImageBackground>
                                }
                                
                                <View style={{marginLeft:8, flexDirection: 'column'}}>
                                    <View style={{flexDirection: 'row', marginTop: 1}}>
                                        <Text style={{ fontSize: 16, lineHeight: 20, color: 'rgba(0, 10, 32, 0.85)' }}>审批人</Text>
                                    </View>

                                    <View style={{flexDirection: 'row', marginTop: 4}}>
                                        <Text style={[{fontSize: 14, lineHeight: 20, color: 'rgba(0, 10, 32, 0.65)' }]}>{this.state.auditOperator}（审批中）</Text>
                                    </View>
                                </View>
                            </View>
                    }
                    
                    {/* 分隔线 */}
                    <View style={styles.lineViewStyle}/>

                    {/* 留言 */}
                    {
                        (this.state.messageBoardList && this.state.messageBoardList.length > 0) ?
                            <View style={{backgroundColor:'rgba(242, 245, 252, 0.5)', borderRadius:10,width:screenWidth-24, marginLeft: 12, marginRight: 12, paddingTop: 5, marginBottom: 5}}>
                                {
                                    this.state.messageBoardList.map((item, index)=>{
                                        return(
                                            <View key={item.messageId} style={{ flexDirection: 'row', marginLeft: 10, marginTop: 10, marginRight: 6, marginBottom: 10}}>
                                                {
                                                    item.operatorPhoto ?
                                                        <Image source={{ uri: (constants.image_addr + '/' + item.operatorPhoto) }} style={{ height: 36, width: 36, borderRadius: 50}} />
                                                        :
                                                        <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ height: 36, width: 36}}>
                                                            <View style={{height: 36, width: 36,justifyContent: "center",alignItems: "center"}}>
                                                                {
                                                                    item.operatorName <= 2 ? 
                                                                    <Text style={{color:'#FFFFFF',fontSize:13,fontFamily:'PingFangSC',fontWeight:"normal",textAlign:'center', lineHeight:20}}>
                                                                        {item.operatorName}
                                                                    </Text>
                                                                    :
                                                                    <Text style={{color:'#FFFFFF',fontSize:13,fontFamily:'PingFangSC',fontWeight:"normal",textAlign:'center', lineHeight:20,}}>
                                                                        {item.operatorName.slice(-2)}
                                                                    </Text>
                                                                }
                                                            </View>
                                                        </ImageBackground>
                                                }

                                                <View style={{ flexDirection: 'column', marginLeft: 10, flex: 1}}>
                                                    <View style={{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'center', paddingTop: 4 }}>
                                                        <View style={{ flexDirection: 'row'}}>
                                                            <Text style={{ fontSize: 16 }}>{item.operatorName}</Text>
                                                        </View>
                                                        <View style={{ flexDirection: 'row', marginLeft: 6}}>
                                                            <Text style={[{ fontSize: 12, color: 'rgba(0,10,32,0.45)' }]}>{item.gmtCreated.slice(0,16)}</Text>
                                                        </View>
                                                        
                                                    </View>

                                                    {
                                                        item.parentMessageId ?
                                                            <View style={[{flexDirection: 'column', justifyContent: 'flex-start'}]}>
                                                                <View style={[{flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'flex-start', marginLeft: 9, marginTop: 11}]}>
                                                                    <Text style={[styles.itemContentStyle, {color:'rgba(0,10,32,0.45)'}]}>{"回复 "+ item.parentUserName + ": "+ item.parentMessageContent}</Text>
                                                                </View>
                                                                <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 8}]}>
                                                                    <Text style={styles.itemContentStyle}>{item.messageContent ? item.messageContent : "无"}</Text>
                                                                </View>
                                                            </View>
                                                            :
                                                            <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 10}]}>
                                                                <Text style={styles.itemContentStyle}>{item.messageContent ? item.messageContent : "无"}</Text>
                                                            </View>
                                                    }
                                                </View>

                                                <View style={{ position:'absolute', right: 0, top: 0}}>
                                                    <TouchableOpacity onPress={() => {
                                                        this.setState({
                                                            parentMessageId:item.messageId,
                                                            messageModal: true,
                                                        })
                                                    }}>
                                                        <View style={[{width: 35, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                                                            <Image style={{ width: 24, height: 24 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                        )                           
                                    })
                                }
                            </View>
                            :
                            <View/>
                    }

                </ScrollView>
                
                {/* 分隔线 */}
                <View style={[styles.lineViewStyle, {paddingBottom: 60, backgroundColor: '#FFFFFF'}]}/>

                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false,
                                    })
                                    if (this.props.route.params.moreDaily) {
                                        this.props.route.params.moreDaily(this.state.departmentId, this.state.departmentName, this.state.userId, this.state.userName);
                                    }
                                    this.props.navigation.goBack()
                                    // this.props.navigation.navigate("QueryDaily",
                                    //     {
                                    //         // 传递参数
                                    //         departmentId: this.state.departmentId,
                                    //         departmentName: this.state.departmentName,
                                    //         userId: this.state.userId,
                                    //         userName: this.state.userName,
                                    //     })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}]}>
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>查看更多日报</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {
                                constants.loginUser.userId == this.state.userId ?
                                <View>
                                    <View>
                                        <TouchableOpacity onPress={() => {
                                            if (this.state.dailyState != "0BB" || this.state.auditScore || dateDiffHours(this.state.currentTime, this.state.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                                WToast.show({ data: '该日报不可编辑' });
                                                return;
                                            }
                                            this.setState({
                                                moreModal: false,
                                            })
                                            this.props.navigation.navigate("DailyAdd",
                                                {
                                                    // 传递参数
                                                    dailyId: this.state.dailyId,
                                                    // 传递回调函数
                                                    refresh: this.callBackFunction
                                                })
                                        }}>
                                            <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                                , (this.state.dailyState != "0BB" || this.state.auditScore || dateDiffHours(this.state.currentTime, this.state.dailyDate) > constants.loginUser.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""]}>
                                                {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                                                <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View>
                                        <TouchableOpacity onPress={() => {
                                            if (this.state.dailyState != "0BB" && this.state.auditScore) {
                                                WToast.show({ data: '日报已审核不可删除' });
                                                return;
                                            }
                                            if (this.state.dailyState != "0BB" && dateDiffHours(this.state.currentTime, this.state.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                                WToast.show({ data: '日报已超出删除时限' });
                                                return;
                                            }
                                            // 删除弹窗Modal
                                            this.setState({
                                                moreModal: false,
                                                deleteModal: true
                                            })
                                            
                                        }}>
                                            <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                                , (this.state.auditScore || (this.state.dailyState != "0BB" && dateDiffHours(this.state.currentTime, this.state.dailyDate)) > constants.loginUser.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""]}>
                                                {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                                                <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                                :
                                <View></View>
                            }
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该日报?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteDaily(this.state.dailyId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17,fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                
                {/* 审核快捷操作Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.auditModal}
                 //  onShow={this.onShow.bind(this)}
                    onRequestClose={() =>console.log('onRequestClose...')}
                >
                    <KeyboardAvoidingView
                        behavior={Platform.OS == "ios" ? "padding" : "height"}
                        style={[{backgroundColor: 'rgba(0,0,0,0.64)' ,flex:1}]}
                    >
                        <View style={{flex:1}}>
                            <ScrollView style={{ width: screenWidth, height: 360, bottom: 0,position:'absolute', backgroundColor: '#FFFFFF', borderTopLeftRadius: 10,borderTopRightRadius: 10}}>
                                    <View style={{ height: 50, justifyContent: 'center', alignItems: 'center' }}>
                                        <Text style={{ fontSize: 18 }}>{this.state.doneState ? "重新审核" : "确认审核"}</Text>
                                    </View>
                                    <View style={{flexDirection:'row', height:50, borderBottomWidth: 0.8, borderColor:'#E8E9EC'}}>
                                        <View style={styles.leftLabView}>
                                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                                <Text style={styles.leftLabNameTextStyle}>审核得分</Text>
                                        </View>
                                        <TextInput 
                                            keyboardType='numeric'
                                            style={styles.inputRightText}
                                            placeholder={'请输入审核得分'}
                                            onChangeText={(text) => this.setState({ editAuditScore: text})}
                                        >
                                            {this.state.auditScore}
                                        </TextInput>
                                    </View>
                                    
                                    <View style={{flexDirection:'row',marginTop:6}}>
                                        <View style={{ width:leftLabWidth,height:30,flexDirection:'row',alignItems:'center',paddingLeft:10,}}>
                                            <Text style={{fontSize:18,marginLeft:5,}}>审核意见</Text>
                                        </View>
                                    </View>
                                    <View style={{flexDirection:'row',height:120,borderBottomWidth: 0.8,borderColor:'#E8E9EC'}}>
                                        <TextInput 
                                            multiline={true}
                                            textAlignVertical="top"
                                            style={[CommonStyle.inputRowText, {height:100, borderWidth: 0}]}
                                            placeholder={'请输入审核意见'}
                                            onChangeText={(text) => this.setState({ auditOpinion: text})}
                                        >
                                            {this.state.auditOpinion}
                                        </TextInput>
                                    </View>

                                    <View style={[CommonStyle.btnRowStyle, {width:screenWidth, marginLeft: 0, marginTop: 6}]}>
                                        <TouchableOpacity 
                                            onPress={() => { 
                                                this.setState({
                                                    auditModal: false
                                                })
                                        }}>
                                            <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginLeft: 20, width: (screenWidth - 56)/2}]} >
                                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                                            </View>
                                        </TouchableOpacity>
                                        <TouchableOpacity onPress={() => {
                                            if (!this.state.editAuditScore && this.state.editAuditScore !==0) {
                                                let toastOpts = getFailToastOpts("请输入审核得分");
                                                WToast.show(toastOpts)
                                                return;
                                            }
                                            this.setState({
                                                auditModal: false,
                                            })
                                            this.auditDaily();
                                        }}>
                                            <View style={[CommonStyle.btnRowRightSaveBtnView, {marginRight: 20, width: (screenWidth - 56)/2}]}>
                                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确认</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                            </ScrollView>
                            </View>
                    </KeyboardAvoidingView>
                </Modal>

                {/* 留言输入框弹窗 */}
                <Modal
                    animationType='slide'
                    transparent={true}
                    visible={this.state.messageModal}
                >
                    <KeyboardAvoidingView
                    behavior={Platform.OS == "ios" ? "padding" : "height"}
                    style={{flex:1}}
                    >
                    <TouchableOpacity style={{flex: 1, position: 'relative'}}
                        onPress={() => {
                            this.setState({
                                messageModal: false,
                                messageContent: "",
                                parentMessageId:""
                            })
                    }}>
                        <View style={{backgroundColor: '#FFFFFF', flexDirection: 'row', alignItems: 'center',
                            position: 'absolute', width: '100%', left: 0, bottom: 0, padding: 5
                        }}>
                            <TextInput 
                                autoFocus
                                multiline={true}
                                placeholder="小小鼓励，让团队更凝聚"
                                style={{backgroundColor: '#F2F5FC', flex: 5, borderRadius: 15, height: 40, marginLeft: 10,paddingLeft: 15}} 
                                onChangeText={(text) => this.setState({ messageContent: text })}
                            />
                            <TouchableOpacity onPress={() => {
                                if (!this.state.messageContent) {
                                    return;
                                }
                                this.setState({
                                    messageModal: false,
                                })
                                this.saveDailyMessage();
                            }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{flex: 1,width: 64, height: 32, backgroundColor: '#1E6EFA', borderRadius: 20 }, 
                                    (this.state.messageContent) ? "" : CommonStyle.disableViewStyle]}>
                                    <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { textAlign: 'center', fontSize: 14 }]}>发送</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </TouchableOpacity>
                    </KeyboardAvoidingView>
                </Modal>

                {/* 底部留言、审核固定框 */}
                {/* <Modal
                    animationType='slide'
                    transparent={true}
                    visible={true}
                >
                    <View style={{backgroundColor: '#FFFFFF', flexDirection: 'row', alignItems: 'center',
                        position: 'absolute', width: '100%', left: 0, bottom: 0, paddingRight: 5
                    }}>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                messageModal: true
                            })
                        }}>
                            <View style={[{width: 78, height: 28, flexDirection: "row", alignItems: 'center', margin: 10, 
                                marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                            }]}>
                                <Image style={{ width: 20, height: 20, marginRight: 8, marginLeft: 12 }} source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                                <Text style={[{ color: 'rgba(0, 10, 32, 0.65)', fontSize: 14, lineHeight: 24 }]}>留言</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                auditModal:true
                            })
                            this.saveDailyMessage();
                        }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{flex: 1,width: 64, height: 32, backgroundColor: '#1E6EFA', borderRadius: 20 }]}>
                                <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { textAlign: 'center', fontSize: 14 }]}>审核</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </Modal> */}
            </View>
        )
    }
}
const styles = StyleSheet.create({
    headline:{
        height:75,
        // paddingLeft:13,
        // paddingRight:13,
        flexDirection:'row',
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 14,
        marginRight: 16,
        marginTop: 5
    },
    titleTextStyle: {
        fontSize: 16,
        lineHeight: 22
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        // color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },
    holdbtnView: {
        fontSize: 16, width: 60, height: 30,
        borderWidth: 1,
        borderColor: 'rgba(250, 250, 250, 1)',
        backgroundColor: 'rgba(30, 110, 250, 1)',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 5,
        borderRadius: 4,
        flexDirection: 'row'
    },
    holdBtnText: {
        color: 'rgba(250, 250, 250, 1)', fontSize: 16
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        marginBottom: 6,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
    itemContentTextStyle: {
        marginLeft: 14,
        marginRight: 16,
        marginTop: 3
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
});
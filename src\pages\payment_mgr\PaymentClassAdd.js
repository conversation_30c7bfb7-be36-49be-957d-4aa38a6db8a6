import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image,KeyboardAvoidingView} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class StorageLocationAdd extends Component {
    constructor(){
        super()
        this.state = {
            paymentClassId:"",
            paymentClassName:"",
            paymentClassSort:0 
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { paymentClassId}= route.params;
            if (paymentClassId) {
                console.log("========Edit==paymentClassId:", paymentClassId);
                this.setState({
                    paymentClassId:paymentClassId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/payment/class/get";
                loadRequest={'paymentClassId':paymentClassId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditPaymentClassDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }

    loadEditPaymentClassDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                paymentClassId:response.data.paymentClassId,
                paymentClassName:response.data.paymentClassName,
                paymentClassSort:response.data.paymentClassSort,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("PaymentClassList")
            }}>
                <Text style={CommonStyle.headRightText}>支付类别</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }
    
    savePaymentClass =()=> {
        console.log("=======savePaymentClass");
        let toastOpts;
        if (!this.state.paymentClassName) {
            toastOpts = getFailToastOpts("请输入支付类别");
            WToast.show(toastOpts)
            return;
        }

        let url= "/biz/payment/class/add";
        if (this.state.paymentClassId) {
            console.log("=========Edit===paymentClassId", this.state.paymentClassId)
            url= "/biz/payment/class/modify";
        }
        let requestParams={
            "paymentClassId":this.state.paymentClassId,
            "paymentClassName":this.state.paymentClassName,
            "paymentClassSort":this.state.paymentClassSort
        };
        httpPost(url, requestParams, this.savePaymentClassCallBack);
    }
    
    // 保存回调函数
    savePaymentClassCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen  title='新增类别'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>支付类别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入支付类别'}
                            onChangeText={(text) => this.setState({paymentClassName:text})}
                        >
                            {this.state.paymentClassName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'0'}
                            onChangeText={(text) => this.setState({paymentClassSort:text})}
                        >
                            {this.state.paymentClassSort}
                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.savePaymentClass.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})
import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions,Modal, KeyboardAvoidingView, Image ,Alert} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class PointRewardAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            rewardId:"",
            staffId:"",
            staffName:"",
            rewardPointValue:"",
            rwardPointDesc:"",
            rewardUserName:constants.loginUser.userName,
            rewardUserId:constants.loginUser.userId,
            pointClassIdDataSource: [],

            //部门相关数据
            departmentModal:false,
            departmentSearchKeyWord:"",
            departmentId:"",
            departmentName:"",
            selDepartmentId:"",
            selDepartmentName:"",
            departmentDataSource:[],
            _departmentDataSource:[],

            //员工相关数据
            userModal:false,
            userSearchKeyWord:"",
            userId:"",
            userName:"",
            selUserId:"",
            selUserName:"",
            userDataSource:[],
            _userDataSource:[],
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 加载员工
        // this.loadUserList();
        //加载积分
        this.loadPointClassList();
        //加载部门
        this.loadDepartmentList();
        console.log("==========积分类别数据源1：", this.state.pointClassIdDataSource);
        console.log("==========部门数据源2：", this.state.departmentList);
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { rewardId, staffName } = route.params;
            if (rewardId) {
                console.log("========Edit==rewardId:", rewardId);
                this.setState({
                    rewardId:rewardId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/point/reward/get";
                loadRequest={'rewardId':rewardId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditPointRewardDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    
    //查询部门方法
    loadDepartmentList=()=>{
        let url= "/biz/department/list_for_tenant";
        let loadRequest={
            // "qryAll_NoPower":"Y",
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadDepartmentListCallBack);
    }
    loadDepartmentListCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            var data = response.data;
            this.setState({
                departmentDataSource:response.data
            })
            console.log("这是部门数据" , data)
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadDepartment = () => {
        var _departmentDataSource = copyArr(this.state.departmentDataSource);
        console.log("查询时部门数据" ,this.state.departmentDataSource)
        if (this.state.departmentSearchKeyWord && this.state.departmentSearchKeyWord.length > 0) {
            _departmentDataSource = _departmentDataSource.filter(item => item.departmentName.indexOf(this.state.departmentSearchKeyWord) > -1);
        }
        this.setState({
            _departmentDataSource: _departmentDataSource,
        })
    }

    //查询员工方法
    loadUserList=()=>{
        let url= "/biz/job/user/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            "departmentId":this.state.selDepartmentId,
        };
        httpPost(url, loadRequest, this.loadUserListCallBack);
    }
    loadUserListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log("当前的员工数据",response.data.dataList)
            console.log(this.state.selDepartmentId);
            this.setState({
                userDataSource:response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadUser = () => {
        var _userDataSource = copyArr(this.state.userDataSource);
        console.log("查询时部门数据" ,this.state.userDataSource)
        if (this.state.userSearchKeyWord && this.state.userSearchKeyWord.length > 0) {
            _userDataSource = _userDataSource.filter(item => item.userName.indexOf(this.state.userSearchKeyWord) > -1);
        }
        this.setState({
            _userDataSource: _userDataSource,
        })
    }

    //加载积分方法
    loadPointClassList = () => {
        let url = "/biz/point/class/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadPointClassListCallBack);
    }

    loadPointClassListCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                pointClassIdDataSource: response.data,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    loadEditPointRewardDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                rewardId:response.data.rewardId,
                rewardPointValue:response.data.rewardPointValue,
                rwardPointDesc:response.data.rwardPointDesc,
                selStaffId: response.data.staffId,
                selUserId: response.data.userId,
                selUserName:response.data.staffName,
                selDepartmentId:response.data.departmentId,
                selDepartmentName:response.data.departmentName,
                rewardUserName:response.data.rewardUserName,
                pointClassName:response.data.pointClassName,
                pointClassId:response.data.pointClassId
            })
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("PointReward")
            }}>
                <Text style={CommonStyle.headRightText}>积分奖励</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }
    
    savePointReward =()=> {
        console.log("=======savePointReward");
        let toastOpts;
        if (!this.state.selUserId) {
            toastOpts = getFailToastOpts("请选择员工");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.rewardPointValue) {
            toastOpts = getFailToastOpts("请输入奖励积分");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.rwardPointDesc) {
            toastOpts = getFailToastOpts("请输入积分描述");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.rewardUserName) {
            toastOpts = getFailToastOpts("请输入奖励人");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/point/reward/add";
        if (this.state.rewardId) {
            console.log("=========Edit===rewardId", this.state.rewardId)
            url= "/biz/point/reward/modify";
        }
        let requestParams={
            "rewardId":this.state.rewardId,
            "staffId":this.state.selStaffId,
            "userId":this.state.selUserId,
            "rewardPointValue":this.state.rewardPointValue,
            "rwardPointDesc":this.state.rwardPointDesc,
            "departmentId":this.state.selDepartmentId,
            "pointClassId":14,
            "rewardUserName":this.state.rewardUserName,
            "rewardUserId":this.state.rewardUserId,
        };
        httpPost(url, requestParams, this.savePointRewardCallBack);
    }
    
    // 保存回调函数
    savePointRewardCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    //员工显示组件
    renderUserRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selUserId: item.userId,
                    selUserName:item.staffName,
                    selStaffId:item.staffId,
                })
                console.log(item.userId,item.staffId,item.staffName)
            }}>
                <View key={item.userId} style={[item.userId === this.state.selUserId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.userId === this.state.selUserId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.staffName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    //部门显示组件 点击部门时记录部门 Id、部门名称、部门下员工数据列 表，将所选员工 id 和 name 赋值为空，用于新部门的员工选择
    renderDepartmentRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selDepartmentId: item.departmentId,
                    selDepartmentName:item.departmentName,
                    selStaffId:null,
                    selStaffName:null,
                    selUserId: null,
                })
            }}>
                <View key={item.departmentId} style={[item.departmentId === this.state.selDepartmentId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.departmentId === this.state.selDepartmentId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.departmentName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 渲染底部积分类别选择器
    openPointClassIdSelect() {
        if (!this.state.pointClassIdDataSource || this.state.pointClassIdDataSource.length < 1) {
            WToast.show({ data: "请先添加积分类别" });
            return
        }
            this.refs.SelectPointClassId.showPointClassId(this.state.selectedPointClass, this.state.pointClassIdDataSource)
    }
        
    callBackPointClassIdValue(value) {
        console.log("==========积分类别选择的结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedPointClass: value,
        })
        var pointClassName = value.toString();
        // this.setState({
        //      pointClassName: pointClassName
        // })
        let loadUrl = "/biz/point/class/getPointClassByName";
        let loadRequest = {
            "pointClassName": pointClassName
        };
            httpPost(loadUrl, loadRequest, this.callBackLoadPointClassData);
    }
    
    callBackLoadPointClassData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                pointClassName: response.data.pointClassName,
                pointClassId:response.data.pointClassId
            })
        }
        else if (response.code == 401) {
        WToast.show({ data: response.message });
        this.props.navigation.navigate("LoginView");
        }
    }
        
    

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '积分奖励'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                
                <ScrollView style={CommonStyle.contentViewStyle}>
                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>部门名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[{flexWrap:'wrap'}, this.state.rewardId? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (this.state.rewardId) {
                                    return;
                                }
                                if (this.state.departmentDataSource && this.state.departmentDataSource.length > 0) {
                                    this.setState({
                                        _departmentDataSource: copyArr(this.state.departmentDataSource),
                                    })
                                }
                                if (!this.state.departmentDataSource || this.state.departmentDataSource.length === 0) {
                                    
                                    let errorMsg = '暂无部门';
                                    Alert.alert('确认', errorMsg, [
                                        {
                                            text: "确定", onPress: () => {
                                                WToast.show({ data: '点击了确定' });
                                            }
                                        }
                                    ]);
                                    return;
                                }
                                this.setState({
                                    departmentModal: true,
                                    departmentSearchKeyWord: ""  //部门的搜索关键字
                                })

                                if ((!this.state.selDepartmentId || this.state.selDepartmentId === 0) && this.state.departmentDataSource && this.state.departmentDataSource.length > 0) {
                                    this.setState({
                                        selDepartmentId: this.state.departmentDataSource[0].departmentId,
                                        selDepartmentName: this.state.departmentDataSource[0].departmentName,
                                        // selStaffId: this.state.userList[0].staffId,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, { flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                {
                                    this.state.selDepartmentId && this.state.selDepartmentName ?
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>{this.state.selDepartmentName}</Text>
                                    :
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>选择部门</Text>
                                }
                                </View>
                            </TouchableOpacity>
                        </View>
                        <Modal
                            animationType={'slide'}
                            transparent={true}
                            onRequestClose={() => console.log('onRequestClose...')}
                            visible={this.state.departmentModal}>
                            <View style={CommonStyle.fullScreenKeepOut}>
                                <View style={CommonStyle.modalContentViewStyle}>
                                    <View style={CommonStyle.rowLabView}>
                                        <View style={styles.leftLabViewImage}>
                                            <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                            <TextInput
                                                style={[styles.searchInputText]}
                                                returnKeyType="search"
                                                returnKeyLabel="搜索"
                                                onSubmitEditing={e => {
                                                    this.loadDepartment();
                                            }}        
                                                placeholder={'输入部门名称'}
                                                onChangeText={(text) => this.setState({ departmentSearchKeyWord: text })}
                                            >
                                                {this.state.departmentSearchKeyWord}
                                            </TextInput>
                                        </View>
                                    </View>
                                    {/* 显示部门数据 */}
                                    <ScrollView style={{}}>
                                        <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                            {
                                                (this.state._departmentDataSource && this.state._departmentDataSource.length > 0)
                                                    ?
                                                    this.state._departmentDataSource.map((item, index) => {
                                                        if (index < 1000) {
                                                            return this.renderDepartmentRow(item)
                                                        }
                                                    })
                                                    : <EmptyRowViewComponent />
                                            }
                                        </View>
                                    </ScrollView>
                                    {/*  */}
                                    <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                        {/* 取消按钮点击事件 */}
                                        <TouchableOpacity onPress={() => {
                                            this.setState({
                                                departmentModal: false,
                                            })
                                        }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                        </TouchableOpacity>
                                        {/* 确定按钮点击事件 */}
                                        <TouchableOpacity onPress={() => {
                                            if (!this.state.selDepartmentId && this.state.selDepartmentId ===0) {
                                                let toastOpts = getFailToastOpts("您还没有选择部门");
                                                WToast.show(toastOpts);
                                                return;
                                            }
                                            this.loadUserList(this.state.selDepartmentId)
                                            this.setState({
                                                departmentModal: false,
                                            })
                                        }}>
                                            <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                                <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                                <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>
                        </Modal>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>员工姓名</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        
                        <View style={[{flexWrap:'wrap'}, this.state.rewardId? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                 if (this.state.rewardId) {
                                    return;
                                }
                                if (!this.state.selDepartmentId) {
                                    WToast.show({ data: '请先选择部门' });
                                    return;
                                }
                                if (this.state.userDataSource && this.state.userDataSource.length > 0) {
                                    this.setState({
                                        _userDataSource: copyArr(this.state.userDataSource)
                                    })
                                    console.log("我现在在选择的以员工数据",this.state.userDataSource)
                                }
                                this.setState({
                                    userModal: true,
                                    userSearchKeyWord: ""  //部门的搜索关键字
                                })
                                if (!this.state.selUserId && this.state.userDataSource && this.state.userDataSource.length > 0) {
                                    this.setState({
                                        selUserId: this.state.userDataSource[0].userId,
                                        selUserName: this.state.userDataSource[0].staffName,
                                        selStaffId:this.state.userDataSource[0].staffId,
                                        // selStaffId: this.state.userList[0].staffId,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, { flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                {
                                    this.state.selUserId && this.state.selUserName ?
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>{this.state.selUserName}</Text>
                                    :
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>选择员工</Text>
                                }
                                </View>
                            </TouchableOpacity>
                        </View>
                        <Modal
                            animationType={'slide'}
                            transparent={true}
                            onRequestClose={() => console.log('onRequestClose...')}
                            visible={this.state.userModal}>
                            <View style={CommonStyle.fullScreenKeepOut}>
                                <View style={CommonStyle.modalContentViewStyle}>
                                    <View style={CommonStyle.rowLabView}>
                                        <View style={styles.leftLabViewImage}>
                                            <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                            <TextInput
                                                style={[styles.searchInputText]}
                                                returnKeyType="search"
                                                returnKeyLabel="搜索"
                                                onSubmitEditing={e => {
                                                    this.loadUser();
                                            }}        
                                                placeholder={'输入员工姓名'}
                                                onChangeText={(text) => this.setState({ userSearchKeyWord: text })}
                                            >
                                                {this.state.userSearchKeyWord}
                                            </TextInput>
                                        </View>
                                    </View>
                                    <ScrollView style={{}}>
                                        <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                            {
                                                (this.state._userDataSource && this.state._userDataSource.length > 0)
                                                    ?
                                                    this.state._userDataSource.map((item, index) => {
                                                        if (index < 1000) {
                                                            return this.renderUserRow(item)
                                                        }
                                                    })
                                                    : <EmptyRowViewComponent />
                                            }
                                        </View>
                                    </ScrollView>
                                    <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                        <TouchableOpacity onPress={() => {
                                            this.setState({
                                                userModal: false,
                                            })
                                        }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                        </TouchableOpacity>
                                        <TouchableOpacity onPress={() => {
                                            if (!this.state.selUserId) {
                                                let toastOpts = getFailToastOpts("您还没有选择员工");
                                                WToast.show(toastOpts);
                                                return;
                                            }
                                            this.setState({
                                                userModal: false,
                                            })
                                        }}>
                                            <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                                <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                                <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>
                        </Modal>
                    </View>
                    
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>积分类别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openPointClassIdSelect()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.pointClassName ? "请选择积分类别" : this.state.pointClassName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>奖励积分</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            // //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入奖励积分'}
                            onChangeText={(text) => this.setState({rewardPointValue:text})}
                        >
                            {this.state.rewardPointValue}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>积分描述</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                            placeholder={'请输入积分描述'}
                            onChangeText={(text) => this.setState({rwardPointDesc:text})}
                        >
                            {this.state.rwardPointDesc}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>奖励人</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入奖励人'}
                            editable={false}
                            onChangeText={(text) => this.setState({rewardUserName:text})}
                            >
                            {this.state.rewardUserName}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row'}]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.savePointReward.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    
                </ScrollView>
                <BottomScrollSelect
                    ref={'SelectPointClassId'}
                    callBackPointClassIdValue={this.callBackPointClassIdValue.bind(this)}
                /> 
               
            </View>
        )
    }
}
const styles = StyleSheet.create({

    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    searchInputText: {
        width: screenWidth -100,
        // borderColor: '#000000',
        // borderBottomWidth: 1,
        // marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        // marginLeft: 0,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabViewImage: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        borderWidth:1,
        borderColor:"#E4E4E4",
        borderRadius:5,
        marginTop:5,
        // marginRight:5
    },  
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }

});
import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, TextInput,
    FlatList, RefreshControl, Image, Switch, ScrollView,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import ClassHeadScreen from '../../component/ClassHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class CourseMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            courseLevelDataSource: [],
            topBlockLayoutHeight: 0,
            selCourseLevelCode: 'all',
            isEnabled: false,
            scene: "D",
            orCourseSort: "",
            searchKeyWord: "",
            showSearchItemBlock: false,
            courseTypeDataSource: null,
            selcourseTypeId: null,
            selcourseTypeName: null,

            showCourseLevelSearchItemBlock: false,
            courseLevelDataSource: null,
            selcourseLevelId: null,
            selcourseLevelName: null,
            flag: true,
            moreModal:false,
            deleteModal:false,
            modalDataItem:null,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');

        // 课程类型
        let loadCourseTypeUrl = "/biz/course/type/list";
        let loadCourseTypeRequest = { "qryAll": "Y", "currentPage": 1, "pageSize": 1000 };
        httpPost(loadCourseTypeUrl, loadCourseTypeRequest, (response) => {
            if (response.code == 200 && response.data.dataList) {
                var courseTypeData = response.data.dataList;
                courseTypeData.unshift({ "courseTypeId": 0, "courseTypeName": "全部" })
                this.setState({
                    courseTypeDataSource: courseTypeData,
                })
                console.log("==========k课程类型数据源：", this.state.courseTypeDataSource);
            }
        });

        //所属职级
        let loadTypeUrl = "/biz/course/level/list";
        let loadRequest = { "qryAll": "Y", "currentPage": 1, "pageSize": 1000 };
        httpPost(loadTypeUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data.dataList) {
                var courseLevelData = response.data.dataList;
                courseLevelData.unshift({ "courseLevelId": 0, "courseLevelName": "全部" })
                this.setState({
                    courseLevelDataSource: response.data.dataList,
                })
                console.log("==========s所属职级数据源：", this.state.courseLevelDataSource);
            }
        });
        this.loadCourseList();
    }

    // 回调函数
    callBackFunction = (scene) => {
        console.log("回调函数")
        let url = "/biz/course/join/task/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "courseLevelId": this.state.selcourseLevelId,
            "courseTypeId": this.state.selcourseTypeId,
            "scene": scene ? scene : this.state.scene
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }


    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/course/join/task/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "courseLevelId": this.state.selcourseLevelId,
            "courseTypeId": this.state.selcourseTypeId,
            "scene": this.state.scene
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            console.log("dataAll1" + JSON.stringify(response.data, null, 6))
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        console.log(this.state.currentPage + "&----" + this.state.totalPage)
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadCourseList();
    }

    loadCourseList = () => {
        let url = "/biz/course/join/task/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "courseLevelId": this.state.selcourseLevelId,
            "courseTypeId": this.state.selcourseTypeId,
            "scene": this.state.scene
        };
        httpPost(url, loadRequest, this.loadCourseListCallBack);
    }

    loadCourseListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            console.log("dataAll" + JSON.stringify(response.data, null, 6))
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteCourse = (courseId) => {
        console.log("=======delete=courseId", courseId);
        // let url = "/biz/course/delete";
        let url = "/biz/course/join/task/delete";
        let requestParams = { 'courseId': courseId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    renderRow = (item, index) => {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("CourseMgrAdd",
                {
                    // 传递参数
                    dataItem:item,
                    operate:"详情",
                    // 传递回调函数
                    refresh: this.callBackFunction
                })
            }}>
            <View key={item.courseId} style={{backgroundColor:'rgba(255, 255, 255, 1)'}}>

                <View style={{ position:'absolute', right: 13, top: 12,zIndex:10}}>
                    <TouchableOpacity onPress={() => {
                        this.setState({
                            moreModal: true,
                            modalDataItem:item
                        })
                    }}>
                        <View style={[{width: 35, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                            <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                        </View>
                    </TouchableOpacity>
                </View>

                <View style={{
                    flexDirection:"row",
                    height:129,
                    width:screenWidth-35-13,
                    // backgroundColor:"red"
                }}>
                    <View style={{ width:130+28,alignItems:"center",justifyContent:"center"}}>
                        {
                            item.coursePhoto?
                            <View>
                            <Image source={{ uri:constants.image_addr + '/' + item.coursePhoto }} style={{ width: 130, height: 93, borderRadius:10}}></Image>
                            </View>
                            :
                            <View>
                                <View style={{position:'absolute',zIndex:10,width: 130, height: 93,alignItems:"center",justifyContent:"center"}}>
                                    <Text style={{fontSize:20,color:"white"}}>{item.courseName}</Text>
                                </View>
                                <Image style={{ width: 130, height: 93,borderRadius:10}} source={require('../../assets/image/defaultCover.png')}></Image>
                            </View>
                        }
                    </View>
                    <View style={{ flex:1 ,alignItems:"center",justifyContent:"center"}}>

                        <View style={{height: 93,width:"100%"}}>
                        {/* 自定义组件 */}
                        <ClassHeadScreen 
                            redTitle={item.courseLevelName}
                            blackTitle={" 第" + item.courseSort + "课 " + item.courseName}
                        />
                        {/* <View style={{backgroundColor:"white", height:44 ,flexDirection:"row"}}>
                            <View style={{
                                position:'absolute',
                                backgroundColor:"rgba(253, 66, 70, 1)",
                                borderRadius:6,
                                borderBottomLeftRadius:0,
                                height:20,
                                // width:19,
                                justifyContent: 'center',
                                alignItems:"center",
                                paddingLeft:2,
                                paddingRight:2,
                                zIndex:10
                            }}>
                                <Text style={{fontSize:12,color:"white",}}>{item.courseLevelName?item.courseLevelName:"?"}</Text>
                            </View>
                            <Text style={{fontSize:16}}>
                                <Text style={{
                                    fontSize:12,
                                    color:"white",
                                }}>
                                    {
                                        (item.courseLevelName?item.courseLevelName:"")+" "
                                    }
                                </Text >
                                {
                                    " 第" + item.courseSort + "课 " + item.courseName
                                }
                            </Text>
                        </View> */}

                        <View style={{ height:20 ,flexDirection: 'row',}}>
                            <View  style={{         
                                    //外边距
                                    borderRadius:10,
                                    backgroundColor:'rgba(27,188,130,0.2)',
                                    height:20,
                                    paddingLeft:10,paddingRight:10,
                                    justifyContent: 'center',
                                    alignItems:"center"
                                }}>
                                <Text
                                    style={{fontSize:12,color:"#1BBC82"}}
                                >
                                    {item.courseTypeName}
                                </Text>
                            </View>  
                        </View>
                        <View style={{ height:17 ,marginTop:12}}>
                            <Text>时长：{item.courseDuration}天</Text>
                        </View>

                        </View>
                    </View>
                </View>

                <View style={styles.lineViewStyle}/> 
            </View>
            </TouchableOpacity>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }

    // 课程类型
    renderCourseTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selcourseTypeId: item.courseTypeId,
                    selcourseTypeName: item.courseTypeName,
                })
            }}>
                <View key={"department_" + item.courseTypeId} style={[item.courseTypeId === this.state.selcourseTypeId ? CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                CommonStyle.choseToSearchItemsViewSize
                ]}>
                    <Text style={[item.courseTypeId === this.state.selcourseTypeId ?
                        CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.courseTypeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    //所属职级
    renderCourseLevelRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selcourseLevelId: item.courseLevelId,
                    selcourseLevelName: item.courseLevelName,
                })
            }}>
                <View key={"department_" + item.courseLevelId} style={[item.courseLevelId === this.state.selcourseLevelId ?
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                CommonStyle.choseToSearchItemsViewSize
                ]}>
                    <Text style={[item.courseLevelId === this.state.selcourseLevelId ?
                        CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.courseLevelName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    toggleSwitch = () => {
        var toastOpts = getSuccessToastOpts('已切换为' + (this.state.scene === "D" ? "培训" : "数字化") + "课程");
        WToast.show(toastOpts);
        var scene = (this.state.scene === "D" ? "T" : "D")
        this.setState({
            isEnabled: !this.state.isEnabled,
            scene: scene
        })
        this.callBackFunction(scene);
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                <Switch
                    trackColor={{ false: "#767577", true: "#81b0ff" }}
                    thumbColor={this.state.isEnabled ? "#f5dd4b" : "#f4f3f4"}
                    ios_backgroundColor="#3e3e3e"
                    onValueChange={this.toggleSwitch}
                    value={this.state.isEnabled}
                />
                <TouchableOpacity onPress={() => {
                    this.props.navigation.navigate("CourseMgrAdd",
                        {
                            operate:"新增",
                            dataItem:{
                                selCourseLevelCode: this.state.selCourseLevelCode,
                                scene: this.state.scene,
                            },
                            // 传递回调函数
                            refresh: this.callBackFunction
                        })
                }}>
                    <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>

                </TouchableOpacity>
            </View>

        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/course/join/task/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "courseLevelId": this.state.selcourseLevelId,
            "courseTypeId": this.state.selcourseTypeId,
            "scene": this.state.scene
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    // 显示搜索项目
    showSearchItemSelect() {
        if (this.state.flag == false) {
            return
        }
        if (!this.state.courseTypeDataSource || this.state.courseTypeDataSource.length < 1) {
            WToast.show({ data: "请先添加课程类型" });
            return
        }
        this.setState({
            showSearchItemBlock: true,
            flag: false
        })
    }
    showLevelSearchItemSelect() {
        if (this.state.flag == false) {
            return
        }
        if (!this.state.courseLevelDataSource || this.state.courseLevelDataSource.length < 1) {
            WToast.show({ data: "请先添加课程所属职级" });
            return
        }
        this.setState({
            showCourseLevelSearchItemBlock: true,
            flag: false
        })
    }


    render() {
        return (
            <View>
                <CommonHeadScreen title='课程管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.headViewStyle, { borderLeftWidth: 0, borderRightWidth: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ flexDirection: 'row', justifyContent: "flex-start", flexWrap: 'wrap', flexDirection: 'row' }}>
                        <View style={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                            <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                                {
                                    this.state.showSearchItemBlock ?

                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                                {this.state.selcourseTypeId && this.state.selcourseTypeName ? (this.state.selcourseTypeName) : "课程类型"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchClosedIconSize]} source={require('../../assets/icon/iconfont/arrow-up.png')}></Image>

                                        </View>
                                        :
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                                {this.state.selcourseTypeId && this.state.selcourseTypeName ? (this.state.selcourseTypeName) : "课程类型"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchOpenedIconSize]} source={require('../../assets/icon/iconfont/arrow_down.png')}></Image>
                                        </View>
                                }
                            </TouchableOpacity>
                        </View>

                        <View style={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                            <TouchableOpacity onPress={() => this.showLevelSearchItemSelect()}>
                                {
                                    this.state.showCourseLevelSearchItemBlock ?
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                                {this.state.selcourseLevelId && this.state.selcourseLevelName ? (this.state.selcourseLevelName) : "所属职级"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchClosedIconSize]} source={require('../../assets/icon/iconfont/arrow-up.png')}></Image>
                                        </View>
                                        :
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                                {this.state.selcourseLevelId && this.state.selcourseLevelName ? (this.state.selcourseLevelName) : "所属职级"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchOpenedIconSize]} source={require('../../assets/icon/iconfont/arrow_down.png')}></Image>
                                        </View>

                                }
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={CommonStyle.singleSearchBox}>
                        <View style={CommonStyle.searchBoxWithoutOthers}>
                            <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            <TextInput
                                style={{ color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 15, paddingTop: 0, paddingBottom: 0, paddingRight: 0, paddingLeft: 0 }}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'搜索课程名称/课程内容'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>


                </View>





                <View>

                    {
                        this.state.showSearchItemBlock ?
                            <View style={[CommonStyle.choseToSearchBigBoxViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                                <View style={CommonStyle.heightLimited}>
                                    <ScrollView>
                                        <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                            <View style={[{ backgroundColor: 'rgba(255,255,255,1)' }, CommonStyle.choseToSearchItemsViewSize]}>
                                                <Text style={{ fontSize: 16, fontWeight: 'bold' }}>课程类型：</Text>
                                            </View>
                                            {
                                                (this.state.courseTypeDataSource && this.state.courseTypeDataSource.length > 0)
                                                    ?
                                                    this.state.courseTypeDataSource.map((item, index) => {
                                                        return this.renderCourseTypeRow(item)
                                                    })
                                                    : null
                                            }
                                        </View>
                                    </ScrollView>
                                </View>
                                <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showSearchItemBlock: false,
                                            flag: true
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        let loadUrl = "/biz/course/join/task/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "searchKeyWord": this.state.searchKeyWord,
                                            "courseLevelId": this.state.selcourseLevelId,
                                            "courseTypeId": this.state.selcourseTypeId,
                                            // "courseState":"0AA"
                                        };
                                        console.log("选择的课程类型=====" + this.state.selcourseTypeId)
                                        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                        this.setState({
                                            showSearchItemBlock: false,
                                            flag: true
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            null
                    }

                    {
                        this.state.showCourseLevelSearchItemBlock ?
                            <View style={[CommonStyle.choseToSearchBigBoxViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                                <View style={CommonStyle.heightLimited}>
                                    <ScrollView>
                                        <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                            <View style={[{ backgroundColor: 'rgba(255,255,255,1)' }, CommonStyle.choseToSearchItemsViewSize]}>
                                                <Text style={{ fontSize: 16, fontWeight: 'bold' }}>所属职级：</Text>
                                            </View>
                                            {
                                                (this.state.courseLevelDataSource && this.state.courseLevelDataSource.length > 0)
                                                    ?
                                                    this.state.courseLevelDataSource.map((item, index) => {
                                                        return this.renderCourseLevelRow(item)
                                                    })
                                                    : null
                                            }
                                        </View>
                                    </ScrollView>
                                </View>
                                <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showCourseLevelSearchItemBlock: false,
                                            flag: true
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        let loadUrl = "/biz/course/join/task/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "courseTypeId": this.state.selcourseTypeId,
                                            "searchKeyWord": this.state.searchKeyWord,
                                            "courseLevelId": this.state.selcourseLevelId,
                                            // "courseState":"0AA"
                                        };
                                        console.log("选择的职级=====" + this.state.selcourseLevelId)
                                        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                        this.setState({
                                            showCourseLevelSearchItemBlock: false,
                                            flag: true
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            null
                    }

                    <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                        <FlatList
                            data={this.state.dataSource}
                            renderItem={({ item, index }) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            keyExtractor={item => item.courseId}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                    tintColor="#FF0000"
                                    title="loading"
                                    colors={['#FF0000', '#00FF00', '#0000FF']}
                                    progressBackgroundColor="#FFFF00"
                                    refreshing={this.state.refreshing}
                                    onRefresh={() => {
                                        this._loadFreshData()
                                    }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={() => this.flatListFooterComponent()}
                            onEndReached={() => this._loadNextData()}
                        // onEndReachedThreshold={0.2}
                        />
                    </View>
                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                        this.setState({
                                            moreModal: false,
                                        })
                                        this.props.navigation.navigate("CourseMgrAdd",
                                            {
                                                // 传递参数
                                                // courseId:  this.state.modalDataItem.courseId,
                                                // scene: this.state.scene,
                                                // orCourseSort: this.state.modalDataItem.courseSort,

                                                dataItem:this.state.modalDataItem,
                                                operate:"详情",
                                                // 传递回调函数
                                                refresh: this.callBackFunction
                                            })
                                    }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}]}>
                                        {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>详情</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View>
                                <TouchableOpacity onPress={() => {
                                        this.setState({
                                            moreModal: false,
                                        })
                                        this.props.navigation.navigate("CourseMgrAdd",
                                            {
                                                // 传递参数
                                                // courseId:  this.state.modalDataItem.courseId,
                                                // scene: this.state.scene,
                                                // orCourseSort: this.state.modalDataItem.courseSort,

                                                dataItem:this.state.modalDataItem,
                                                operate:"编辑",
                                                // 传递回调函数
                                                refresh: this.callBackFunction
                                            })
                                    }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}]}>
                                        {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => {
                                    if (this.state.modalDataItem.taskNum) {
                                        WToast.show({ data: "课程任务已开始，无法删除" });
                                        return;
                                    }
                                    this.setState({
                                        moreModal: false,
                                        deleteModal: true
                                    })
                                    }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5},(this.state.modalDataItem&&this.state.modalDataItem.taskNum?CommonStyle.disableViewStyle : "")]}>
                                        {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.08)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该课程?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteCourse(this.state.modalDataItem.courseId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentTextStyle: {
        marginLeft: 12,
        marginRight: 16,
        marginTop: 3,
        lineHeight: 24,
    },
    lineViewStyle:{
        // height:1,
        marginLeft: 13,
        marginRight: 13,
        // marginTop: 15,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },
});
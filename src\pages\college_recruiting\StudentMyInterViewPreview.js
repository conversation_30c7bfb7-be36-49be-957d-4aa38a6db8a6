import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class StudentMyInterViewPreview extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            basicInfo:"",
            personalHonor:"",
            collegeEvaluation:"",
            selExtList:[],
            selStaffId:null,
            staffId:"",
            pointTotalValue:"",
            staffName: "",
            staffSex: "",
            resumeClass:"",
            staffTel: "",
            staffBirthday: "",
            nationality: "",
            height: "",
            politicsStatus: "",
            englishLevelName: "",
            className: "",
            nativePlace: "",
            address: "",
            email: "",
            electronicPhotos:"",
            comprehensivePoint: "",
            education: "",
            professionalName:  "",
            graduateInstitutions:"",
            userPhotoUrl:"",
            pointRanking:"",
            classPointAvg:"",
            classSize:"",
            crEducation:"",
            crProfessionalName:"",
            userId:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 获取积分情况

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { personalHonor, collegeEvaluation,selExtList,staffId,staffName,staffSex,staffBirthday,nationality,height,politicsStatus,
                professionalName,comprehensivePoint,englishLevel,graduateInstitutions,education,nativePlace,address,staffTel,email,electronicPhotos,userPhotoUrl,crEducation,crProfessionalName,userId,resumeClass,} = route.params;
            if (personalHonor) {
                this.setState({
                    personalHonor:personalHonor
                })
            }
            if (collegeEvaluation) {
                this.setState({
                    collegeEvaluation:collegeEvaluation
                })
            }
            if (staffName) {
                this.setState({
                    staffName:staffName
                })
            }

            if (staffSex) {
                this.setState({
                    staffSex:staffSex
                })
            }

            if (staffBirthday) {
                this.setState({
                    staffBirthday:staffBirthday
                })
            }

            if (nationality) {
                this.setState({
                    nationality:nationality
                })
            }

            if (height) {
                this.setState({
                    height:height
                })
            }

            if (politicsStatus) {
                this.setState({
                    politicsStatus:politicsStatus
                })
            }

            if (professionalName) {
                this.setState({
                    professionalName:professionalName
                })
            }

            if (comprehensivePoint) {
                this.setState({
                    comprehensivePoint:comprehensivePoint
                })
            }

            if (englishLevel) {
                this.setState({
                    englishLevelName:englishLevel
                })
            }

            if (graduateInstitutions) {
                this.setState({
                    graduateInstitutions:graduateInstitutions
                })
            }

            if (education) {
                this.setState({
                    education:education
                })
            }

            if (nativePlace) {
                this.setState({
                    nativePlace:nativePlace
                })
            }

            if (address) {
                this.setState({
                    address:address
                })
            }

            if (staffTel) {
                this.setState({
                    staffTel:staffTel
                })
            }

            if (email) {
                this.setState({
                    email:email
                })
            }

            if (electronicPhotos) {
                this.setState({
                    electronicPhotos: electronicPhotos
                })
            }

            if (userPhotoUrl) {
                this.setState({
                    userPhotoUrl:userPhotoUrl
                })
            }

            if (crEducation) {
                this.setState({
                    crEducation:crEducation
                })
            }

            if (crProfessionalName) {
                this.setState({
                    crProfessionalName:crProfessionalName
                })
            }

            if (resumeClass) {
                this.setState({
                    resumeClass:resumeClass
                })
            }
            
            if (userId) {
                this.setState({
                    userId:userId
                })
            }

            if (selExtList) {
                let _spExtDTOList = [];
                selExtList.map((elem, index)=>{
                    var extDTO = {
                        "extId": elem.extId,
                        "extTitle": elem.extTitle,
                        "extContent":elem.extContent,
                    }
                    _spExtDTOList.push(extDTO);
                })
                console.log("=============selExtList" + selExtList + "");
                this.setState({
                    selExtList:_spExtDTOList,
                })
            }

            if(staffId) {
                console.log("====传入的员工id是" + staffId);
                this.setState({
                    staffId : staffId
                })
                this.loadStudentInterViewList(staffId);
            }

            this.loadPointInfo(staffId ? staffId : constants.loginUser.staffId);
        }
    }

    loadPointInfo=(staffId)=>{
        let url = "/biz/point/record/ranking";
        let loadRequest = {
            "staffId":staffId
        };
        httpPost(url, loadRequest, this.loadPointInfoCallBack);

    }

    loadPointInfoCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                pointRanking: response.data.pointRanking,
                classPointAvg: response.data.classPointAvg,
                classSize: response.data.classSize,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadStudentInterViewList=(staffId) =>{
        let url = "/biz/cr/staff/get";
        let loadRequest = {
           "staffId" : staffId
        };
        httpPost(url, loadRequest, this._loadStudentInterViewListcallback);

    }

    _loadStudentInterViewListcallback=(response) =>{
        if (response.code == 200 && response.data) {
            this.setState({
            staffId: response.data.staffId,
            staffName: response.data.staffName,
            staffSex: response.data.staffSex == 'M' ? "男" : (response.data.staffSex == 'L' ? "女" : null),
            resumeClass:response.data.resumeClass == 'P' ? "实习" : (response.data.resumeClass == 'E' ? "全职" : null),
            staffTel: response.data.staffTel,
            staffBirthday: response.data.staffBirthday,
            nationality: response.data.nationality,
            height: response.data.height,
            politicsStatus: response.data.politicsStatus,
            englishLevelName: response.data.englishLevelName,
            className: response.data.className,
            nativePlace: response.data.nativePlace,
            address: response.data.address,
            email: response.data.email,
            userPhotoUrl:constants.image_addr + '/' +  response.data.electronicPhotos,
            electronicPhotos:response.data.electronicPhotos,
            comprehensivePoint: response.data.comprehensivePoint/100,
            education: response.data.education,
            professionalName:  response.data.professionalName,
            personalHonor: response.data.personalHonor,
            collegeEvaluation: response.data.collegeEvaluation,
            selExtList:response.data.crStaffExtDTOList,
            graduateInstitutions:response.data.graduateInstitutions,
            userId:response.data.userId,
            pointTotalValue:response.data.pointTotalValue,
            crEducation:response.data.crEducation,
            crProfessionalName:response.data.crProfessionalName,
            userId:response.data.userId
            })

            console.log("现在的照片",constants.image_addr + '/' +  response.data.electronicPhotos)
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='简历预览'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={[CommonStyle.addItemSplitRowText,{fontWeight:'bold'}]}>基本信息</Text>
                    </View>
                    {
                        this.state.electronicPhotos?
                        <View style = {[styles.titleViewStyle,{marginLeft:(screenWidth/2 * 1.25),marginTop:screenHeight * 0.1,position:'absolute'}]}>
                            {/* <Text style={styles.titleTextStyle}>电子照片</Text> */}
                            <Image style={[styles.photos,{width:120,height:160}]} source={{uri:this.state.userPhotoUrl}} />
                        </View>
                        :
                        <View style={{width:120,height:160,borderColor:'#AAAAAA',borderWidth:0.3,justifyContent:'center',alignItems:'center'
                        ,marginLeft:(screenWidth/2 * 1.25),marginTop:screenHeight * 0.1,position:'absolute'}}>
                            <Text style={[styles.titleTextStyle,{color:'#aaaaaa'}]}>无</Text>
                        </View>
                    }
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>姓名：{this.state.staffName}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>性别：{this.state.staffSex}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>出生年月：{this.state.staffBirthday}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>民族：{this.state.nationality}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>身高(cm)：{this.state.height}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>政治面貌：{this.state.politicsStatus}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>专业：{this.state.crProfessionalName ? this.state.crProfessionalName : this.state.professionalName}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>综合绩点：{this.state.comprehensivePoint}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>英语能力：{this.state.englishLevelName}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>毕业院校：{this.state.graduateInstitutions}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>学历：{this.state.crEducation ? this.state.crEducation : this.state.education}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>籍贯：{this.state.nativePlace}</Text>
                    </View>
                    <View style={[styles.titleViewStyle]}>
                        <Text style={[styles.titleTextStyle]}>住址：{this.state.address}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>联系电话：{this.state.staffTel}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>电子邮箱：{this.state.email}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>求职类型：{this.state.resumeClass}</Text>
                    </View>

                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={[CommonStyle.addItemSplitRowText,{fontWeight:'bold'}]}>个人荣誉</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>{this.state.personalHonor}</Text>
                    </View>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={[CommonStyle.addItemSplitRowText,{fontWeight:'bold'}]}>学院评价</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>{this.state.collegeEvaluation}</Text>
                    </View>
                    {/* <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>新增项目</Text>
                    </View> */}
                    {
                        (this.state.selExtList && this.state.selExtList.length > 0)
                        ?
                        <View>
                            {
                                this.state.selExtList.map((item, index)=>{
                                    return(
                                    <View key={item.extId} style={[styles.innerViewStyle]}>
                                        <View style={CommonStyle.addItemSplitRowView}>
                                            <Text style={[CommonStyle.addItemSplitRowText,{fontWeight:'bold'}]}>{item.extTitle ? item.extTitle : "无"}</Text>
                                        </View>
                                        <View style={styles.titleViewStyle}>
                                            <Text style={styles.titleTextStyle}>{item.extContent ? item.extContent : "无" }</Text>
                                        </View>
                                    </View>
                                    )                           
                                })
                            }
                        </View> : <View/>
                    }
                    {
                        this.state.professionalName ? 
                        <View>
                            <View style={CommonStyle.addItemSplitRowView}>
                                <Text style={[CommonStyle.addItemSplitRowText,{fontWeight:'bold'}]}>积分情况</Text>
                            </View>
                            <View style={{flexDirection:'row'}}>
                                <View style={[{margin:10,width:screenWidth/2}]}>
                                    <Text style={[styles.titleTextStyle,{marginBottom:10}]}>学员积分：{this.state.pointTotalValue ? this.state.pointTotalValue : 0}</Text>
                                    <Text style={styles.titleTextStyle}>平均积分：{this.state.classPointAvg?(this.state.classPointAvg).toFixed(2):0}</Text>
                                </View>
                                <View style={[{margin:10,width:screenWidth/2}]}>
                                    <Text style={[styles.titleTextStyle,,{marginBottom:10}]}>积分排名：{this.state.pointRanking}</Text>
                                    <Text style={styles.titleTextStyle}>班级人数：{this.state.classSize ? this.state.classSize : 0}</Text>
                                </View>

                            </View>
                        </View>
                        : <View/>
                    } 
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={[CommonStyle.addItemSplitRowText,{fontWeight:'bold'}]}>成长轨迹</Text>
                    </View>
                    <View style={{flexDirection:'row',justifyContent:'flex-start'}}>
                        <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("WorkDaily", 
                                {
                                    // 传递参数
                                    selStaffId: this.state.userId,
                                    // 传递回调函数
                                    // refresh: this.callBackFunction
                                })
                                console.log("所选中的userId是========"+this.state.userId)
                            }}>
                            <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{borderColor:"#53A4FC",width:screenWidth/3.7,flexDirection:"row",marginLeft:10 }]}>
                                    <Image  style={{width:22, height:22,marginRight:3}} source={require('../../assets/icon/iconfont/WorkDaily1.png')}></Image>
                                <Text style={[CommonStyle.itemBottomDeleteBtnTextStyle,{color:"#53A4FC"}]}>工作日报</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("QueryHarvest", 
                                {
                                    // 传递参数
                                    selStaffId: this.state.userId,
                                    // 传递回调函数
                                    // refresh: this.callBackFunction
                                })
                            }}>
                            <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{borderColor:"#EA5C20",width:screenWidth/3.7,flexDirection:"row",marginLeft:0 }]}>
                                    <Image  style={{width:24, height:24,marginRight:3}} source={require('../../assets/icon/iconfont/harvest.png')}></Image>
                                <Text style={[CommonStyle.itemBottomDeleteBtnTextStyle,{color:"#EA5C20"}]}>学习成果</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("QueryPromotionPlan", 
                                {
                                    // 传递参数
                                    selStaffId: this.state.userId,
                                    // 传递回调函数
                                    // refresh: this.callBackFunction
                                })
                            }}>
                            <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{borderColor:"#3ab240",width:screenWidth/3.7,flexDirection:"row",marginLeft:0 }]}>
                                    <Image  style={{width:22, height:22,marginRight:3}} source={require('../../assets/icon/iconfont/growUp.png')}></Image>
                                <Text style={[CommonStyle.itemBottomDeleteBtnTextStyle,{color:"#3ab240"}]}>成长明细</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', width:100, alignItems:'center', alignContent:'flex-end', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginTop:5,
        marginBottom:5,
    },
    innerViewStyle:{
        marginTop:0,
    },
    photos:{
        width:150,
        height:200,
        // borderRadius:50,
        borderWidth:0,
        // marginTop:80,
        // marginBottom:30
    },
});
import React, {Component} from 'react';
import PropTypes from 'prop-types';
import {
    StyleSheet,
    Text,
    View,
    Dimensions,
    Platform, TouchableOpacity
} from 'react-native';
import { ifIphoneX, ifIphoneXHeaderHeight } from '../utils/ScreenUtil';

// 取得屏幕的宽高Dimensions
const {width,height} = Dimensions.get('window');

export default class ClassHeadScreen extends Component {

    constructor(props) {
        super(props);
        this.state = {  
            redTitle:props.redTitle,
            redFont:Number(props.redFont),
            blackTitle:props.blackTitle,
            blackFont:Number(props.blackFont),
            titleHeight:Number(props.titleHeight),
            titleWidth:Number(props.titleWidth),
        };
    }

    static propTypes = {
        redTitleItem:PropTypes.func,
        redFontItem:PropTypes.func,
        blackTitleItem:PropTypes.func,
        blackFontItem:PropTypes.func,
        titleHeightItem:PropTypes.func,
        titleWidthItem:PropTypes.func,
    };

    renderRedTitleItem(){
        if (this.props.redTitleItem === undefined) return;
        return this.props.redTitleItem();
    }

    renderBlackTitleItem(){
        if (this.props.blackTitleItem === undefined) return;
        return this.props.blackTitleItem();
    }

    render() {
        return (
            <View style={
                this.state.titleHeight ?
                {backgroundColor:"white", height:this.state.titleHeight ,flexDirection:"row"}
                :
                {backgroundColor:"white", height:44 ,flexDirection:"row",}
            }>
                <View style={
                    this.state.titleWidth ?
                    {backgroundColor:"white", width:this.state.titleWidth ,flexDirection:"row"}
                    :
                    {backgroundColor:"white", flexDirection:"row",}
                }>
                <View style={{
                    position:'absolute',
                    backgroundColor:"rgba(253, 66, 70, 1)",
                    borderRadius:6,
                    borderBottomLeftRadius:0,
                    height:20,
                    justifyContent: 'center',
                    alignItems:"center",
                    paddingLeft:2,
                    paddingRight:2,
                    zIndex:10
                }}>
                    <Text style={
                            this.state.redFont ?
                            {fontSize:this.state.redFont,color:'white'}   
                            :
                            {fontSize:12,color:'white'}
                            
                                                     
                    }>
                        {
                            this.state.redTitle ?
                            this.state.redTitle
                            :
                            this.renderRedTitleItem()
                        }   
                    </Text>
                </View>

                <Text style={
                    this.state.blackFont ?
                    {fontSize:this.state.blackFont}
                    :
                    {fontSize:16}
                   
                }>
                    <Text style={{fontSize:12,color:"white",}}>
                        {
                            (this.state.redTitle ? this.state.redTitle : "")+" "
                        }
                    </Text>
                    {
                        this.state.blackTitle ?
                        <Text>{this.state.blackTitle}</Text>
                        :
                        this.renderBlackTitleItem()
                    }   
                    
                </Text>
                
                </View>
            </View>
        );
    }
}
const styles = StyleSheet.create({
    container: {

    },
    headCenterTitleText:{
       fontSize:20,
       color:'#000000',
       fontWeight:'600',
    },
    headRightView:{
        height:50,
        paddingRight:10,
        alignItems:'center',
        justifyContent:'center'
    },
    headRightText:{
        color:'#33333375',
        fontSize:14,
    }
});
import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    BackHandler,DeviceEventEmitter
} from 'react-native';

import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-community/async-storage';

import LinearGradinet from 'react-native-linear-gradient';

import { WToast } from 'react-native-smart-tip';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;


export default class UserPrivacyComponent extends Component {
    constructor(props) {
        super(props);
        this.state = {
            appFirstStartPopup: false,
            isAgree:true,
            showUserAgreement:false,
            showPrivacy:false,
            topBlockLayoutHeight:0,
            errorMessage:null
        }
    }

    onBackPress = () => {
        Alert.alert(
            '退出应用',
            '确认退出应用吗?',
            [
                { text: '取消', onPress: () => console.log('Cancel Pressed'), style: 'cancel' },
                { text: '确认', onPress: () => {console.log("=====确定？？？"); 2/0; BackHandler.exitApp() }
                },
            ],
            { cancelable: false }
        );
        return true;
    };

    UNSAFE_componentDidMount() {
        BackHandler.addEventListener("hardwareBackPress", this.onBackPress);
    }

    UNSAFE_componentWillMount() {
        BackHandler.removeEventListener("hardwareBackPress", this.onBackPress);

        AsyncStorage.getItem("clientInfoStorage", (err, result)=> {
            if (err) {
               console.log('AsyncStorage.getItem error' + err);
               this.setState({
                   appFirstStartPopup: false
                })
                return;
            }
            let clientInfoStorage = JSON.parse(result);
            if (clientInfoStorage != null && clientInfoStorage.appFirstStartPopup != null && clientInfoStorage.appFirstStartPopup === 'Y') {
                console.log("==========已经同意过《隐私政策》");
                this.setState({
                    appFirstStartPopup: true
                })
            }
            else {
                this.setState({
                    appFirstStartPopup: false
                })
            }
            return result;
        });

    }



    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }


    onPrivacy =()=> {
        this.setState({
            showPrivacy:true,
        })
        // this.state.props.navigation.navigate("PrivacyScreen2");
        // let netWork = constants.privacyUrl;
        // Linking.openURL(netWork)
    }
    onUserAgreement =()=> {
        this.setState({
            showUserAgreement:true
        })
        // this.state.props.navigation.navigate("UserAgreementScreen2");
        // let netWork = constants.userAgreementUrl;
        // Linking.openURL(netWork);
    }

    onBack =()=> {
        this.setState({
            showPrivacy:false,
            showUserAgreement:false,
        })
    }

    notAgreePrivacy =()=> {
        this.setState({
            appFirstStartPopup: false,
            isAgree:false
        })

        AsyncStorage.removeItem('clientInfoStorage', (error) => {
            if (error) {
                console.log("==删除本地储存[clientInfoStorage].失败==", error);
            }
            else  {
                console.log("==删除本地储存[clientInfoStorage].成功==");
            }
        });
    }

    agreePrivacy =()=> {
        this.setState({
            appFirstStartPopup: true,
            isAgree:true
        })

        let clientInfoStorage = {
            appFirstStartPopup: "Y"
        }
        // 本地储存
        AsyncStorage.setItem('clientInfoStorage', JSON.stringify(clientInfoStorage), (error) => {
            if (error) {
                console.log("==设置本地储存[clientInfoStorage]失败==", error);
            } else {
                console.log("==设置本地储存[clientInfoStorage]成功==");
                //发送通知
                //两个参数分别为：通知名称，通知内容或携带参数
                DeviceEventEmitter.emit('action', true, new Date());
            }
        });
    }

    render(){
        return(
            <View onLayout={this.topBlockLayout.bind(this)} 
                style={[{
                    position: 'absolute', 
                    borderRadius: 10,  
                    backgroundColor:'#FFF',
                    overflow:'hidden',
                    borderColor:"#F5F5F5",
                    borderWidth:1,
                    padding: 30, 
                    paddingTop: 30, 
                    paddingBottom: 30,
                    width: screenWidth * 0.9,
                    marginLeft:screenWidth * 0.05,
                    marginTop:(screenHeight - this.state.topBlockLayoutHeight) / 2 - 30,
                    alignItems:'center',
                    justifyContent:'center',
                    zIndex:1000
                    }, this.state.appFirstStartPopup ? styles.blockHidden : styles.blockShow]}>
                        {
                        (!this.state.appFirstStartPopup && this.state.showUserAgreement) ?
                        <View style={{marginTop:-15, marginBottom:-15}}>
                            <TouchableOpacity onPress={() => {
                                    this.onBack();
                                }}>
                                <LinearGradinet start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#479EE6', '#54B7EB']} 
                                style={{ marginBottom:10, marginLeft:20,  width: 45, padding:(15, 5), borderRadius:10}}>
                                    <Text style={{ color: '#FFF', fontSize: 16 }}>返回</Text>
                                </LinearGradinet>
                            </TouchableOpacity>
                            <View style={{height:screenHeight*0.7, width:screenWidth * 0.9}}>
                                <WebView 
                                    source={{uri:constants.userAgreementUrl}}
                                    scalesPageToFit={true}
                                    style={{width:'100%',height:'100%'}} 
                                />
                            </View>
                        </View>
                        :null
                        }
                        {
                        (!this.state.appFirstStartPopup && this.state.showPrivacy) ?
                        <View style={{marginTop:-15, marginBottom:-15}}>
                            <TouchableOpacity onPress={() => {
                                    this.onBack();
                                }}>
                                <LinearGradinet start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#479EE6', '#54B7EB']} 
                                style={{ marginBottom:10, marginLeft:20,  width: 45, padding:(15, 5), borderRadius:10}}>
                                    <Text style={{ color: '#FFF', fontSize: 16 }}>返回</Text>
                                </LinearGradinet>
                            </TouchableOpacity>
                            <View style={{height:screenHeight*0.7, width:screenWidth * 0.9}}>
                                <WebView 
                                    source={{uri:constants.privacyUrl}}
                                    scalesPageToFit={true}
                                    style={{width:'100%',height:'100%'}} 
                                />
                            </View>
                        </View>
                        :null
                        }
                        {
                        (!this.state.appFirstStartPopup && !this.state.isAgree && !this.state.showPrivacy && !this.state.showUserAgreement) ?
                            <View>
                                <View style={{ alignItems: 'center' }}>
                                    <Text style={{ fontSize: 25, color: '#000' }}>警告提示</Text>
                                </View>
                                
                                <View style={{ flexDirection: 'column', alignItems: 'flex-start', marginTop: 10 }}>
                                    <Text style={{ fontSize: 15, color: '#666', lineHeight: 20 }}>
                                        需要同意用户协议和隐私政策，再想想去阅读相应的文件，或点击退出应用
                                    </Text>
                                </View>

                                <TouchableOpacity onPress={() => {
                                    this.onBackPress();
                                }}>
                                    <LinearGradinet start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#DF2910', '#CE3C27']} style={{ margin: 10, marginTop: 20, padding: 10, alignItems: 'center', borderRadius: 10 }}>
                                        <Text style={{ color: '#FFF', fontSize: 18 }}>退出应用</Text>
                                    </LinearGradinet>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        isAgree:true
                                    })
                                }}>
                                    <LinearGradinet start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#9b63cd', '#6D96E6']} style={{ margin: 10, marginTop: 20, padding: 10, alignItems: 'center', borderRadius: 10 }}>
                                        <Text style={{ color: '#FFF', fontSize: 18 }}>我再想想</Text>
                                    </LinearGradinet>
                                </TouchableOpacity>

                            </View>
                            : null
                        }
                        {
                            (!this.state.appFirstStartPopup && this.state.isAgree && !this.state.showPrivacy && !this.state.showUserAgreement) ?
                            <View>
                                <View style={{ alignItems: 'center' }}>
                                    <Text style={{ fontSize: 25, color: '#000' }}>温馨提示</Text>
                                </View>
                                <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginTop: 20 }}>
                                    <Text style={{ fontSize: 15, color: '#666' }}>请充分阅读并理解</Text>
                                    <TouchableOpacity onPress={this.onUserAgreement.bind(this)}>
                                        <Text style={{ fontSize: 15, color: 'red' }}>《用户协议》</Text>
                                    </TouchableOpacity>
                                    <Text style={{ fontSize: 15 }}>与</Text>
                                    <TouchableOpacity onPress={this.onPrivacy.bind(this)}>
                                        <Text style={{ fontSize: 15, color: 'red' }}>《隐私政策》</Text>
                                    </TouchableOpacity>
                                </View>
                                <View style={{ flexDirection: 'column', 
                                alignItems: 'flex-start', 
                                marginTop: 10 }}>
                                    <Text style={{ fontSize: 15, color: '#666', lineHeight: 30 }}>
                                        1.为保障系统运行，我们会申请您的手机号码、设备信息、连接网络权限、阿里推送标识、读存SD卡（上传和保存图片使用）{"\n"}
                                        2.您在使用{constants.appName}服务时，我们会收集并汇总您记录的操作信息{"\n"}
                                        3.应用目前集成了facebook.fresco、google谷歌等SDK{"\n"}
                                        4.请您谨慎并留意个人敏感信息，您同意您的个人敏感信息我们可以按本《用户协议》与《隐私政策》所述的目的和方式来处理。
                                    </Text>
                                </View>
                                <TouchableOpacity onPress={() => {
                                    this.agreePrivacy();
                                }}>
                                    <LinearGradinet start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#9b63cd', '#e0708c']} style={{ margin: 10, marginTop: 20, padding: 10, alignItems: 'center', borderRadius: 10 }}>
                                        <Text style={{ color: '#FFF', fontSize: 18 }}>同意并继续</Text>
                                    </LinearGradinet>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => {
                                    WToast.show({ data: '只有同意并继续才能享有我们的服务噢' });
                                    this.notAgreePrivacy();
                                }}>
                                    <View style={{ alignItems: 'center', marginTop: 20 }}>
                                        <Text style={{ color: '#666', fontSize: 18 }}>不同意</Text>
                                    </View>
                                </TouchableOpacity>

                            </View>
                            : null
                         }

                </View>
            // <View style={{ position: 'absolute', borderRadius: 10, height:100, backgroundColor:'red',borderColor:"#333",borderWidth:1, width: '90%', padding: 15, paddingTop: 30, paddingBottom: 30, justifyContent: 'center', alignContent: 'center',  }}>
            //     {
            //             (this.state.isAgree && !this.state.appFirstStartPopup) ?
            //                 <View>
            //                     <View style={{ alignItems: 'center' }}>
            //                         <Text style={{ fontSize: 25, color: '#000' }}>警告提示</Text>
            //                     </View>
                                
            //                     <View style={{ flexDirection: 'column', alignItems: 'flex-start', marginTop: 10 }}>
            //                         <Text style={{ fontSize: 15, color: '#666', lineHeight: 20 }}>
            //                             需要同意用户协议和隐私政策，再想想去阅读相应的文件，或点击退出应用
            //                         </Text>
            //                     </View>

            //                     <TouchableOpacity onPress={() => {
            //                         this.onBackPress();
            //                     }}>
            //                         <LinearGradinet start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#DF2910', '#CE3C27']} style={{ margin: 10, marginTop: 20, padding: 10, alignItems: 'center', borderRadius: 10 }}>
            //                             <Text style={{ color: '#FFF', fontSize: 18 }}>退出应用</Text>
            //                         </LinearGradinet>
            //                     </TouchableOpacity>
            //                     <TouchableOpacity onPress={() => {
            //                         this.setState({
            //                             isAgree:false
            //                         })
            //                     }}>
            //                         <LinearGradinet start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#9b63cd', '#6D96E6']} style={{ margin: 10, marginTop: 20, padding: 10, alignItems: 'center', borderRadius: 10 }}>
            //                             <Text style={{ color: '#FFF', fontSize: 18 }}>我再想想</Text>
            //                         </LinearGradinet>
            //                     </TouchableOpacity>

            //                 </View>
            //                 : null
            //             }
            //             {
            //                 (!this.state.appFirstStartPopup && !this.state.isAgree) ?
            //                 <View>
            //                     <View style={{ alignItems: 'center' }}>
            //                         <Text style={{ fontSize: 25, color: '#000' }}>温馨提示</Text>
            //                     </View>
            //                     <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginTop: 20 }}>
            //                         <Text style={{ fontSize: 15, color: '#666' }}>请充分阅读并理解</Text>
            //                         <TouchableOpacity onPress={this.onUserAgreement.bind(this)}>
            //                             <Text style={{ fontSize: 15, color: 'red' }}>《用户协议》</Text>
            //                         </TouchableOpacity>
            //                         <Text style={{ fontSize: 15 }}>与</Text>
            //                         <TouchableOpacity onPress={this.onPrivacy.bind(this)}>
            //                             <Text style={{ fontSize: 15, color: 'red' }}>《隐私政策》</Text>
            //                         </TouchableOpacity>
            //                     </View>
            //                     <View style={{ flexDirection: 'column', alignItems: 'flex-start', marginTop: 10 }}>
            //                         <Text style={{ fontSize: 15, color: '#666', lineHeight: 20 }}>
            //                             1.为保障系统运行，我们会申请您的手机号码、设备信息、连接网络权限等
            //                         </Text>
            //                         <Text style={{ fontSize: 15, color: '#666', lineHeight: 20 }}>
            //                             2.您在使用{constants.appName}服务时，我们会收集并汇总您记录的操作信息
            //                         </Text>
            //                         <Text style={{ fontSize: 15, color: '#666', lineHeight: 20 }}>
            //                             3.应用目前集成了facebook.fresco、google谷歌等SDK
            //                         </Text>
            //                         <Text style={{ fontSize: 15, color: '#666', lineHeight: 20 }}>
            //                             4.请您谨慎并留意个人敏感信息，您同意您的个人敏感信息我们可以按本《用户协议》与《隐私政策》所述的目的和方式来处理。
            //                         </Text>
            //                     </View>
            //                     <TouchableOpacity onPress={() => {
            //                         this.agreePrivacy();
            //                     }}>
            //                         <LinearGradinet start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#9b63cd', '#e0708c']} style={{ margin: 10, marginTop: 20, padding: 10, alignItems: 'center', borderRadius: 10 }}>
            //                             <Text style={{ color: '#FFF', fontSize: 18 }}>同意并继续</Text>
            //                         </LinearGradinet>
            //                     </TouchableOpacity>
            //                     <TouchableOpacity onPress={() => {
            //                         WToast.show({ data: '只有同意并继续才能享有我们的服务噢' });
            //                         this.notAgreePrivacy();
            //                     }}>
            //                         <View style={{ alignItems: 'center', marginTop: 20 }}>
            //                             <Text style={{ color: '#666', fontSize: 18 }}>不同意</Text>
            //                         </View>
            //                     </TouchableOpacity>

            //                 </View>
            //                 : null
            //             }

            // </View>
        );
    }
};

const styles = StyleSheet.create({
    blockShow:{
        borderWidth:1,
        padding: 30, 
        paddingTop: 30, 
        paddingBottom: 30,
    },
    blockHidden:{
        borderWidth:0,
        padding: 0, 
        paddingTop: 0, 
        paddingBottom: 0,
    }
});
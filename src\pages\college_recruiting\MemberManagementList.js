import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Modal, ScrollView,
    FlatList, RefreshControl, TextInput, Image, Clipboard
} from 'react-native';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
const { ifIphoneXContentViewHeight, ifIphoneXBodyViewHeight, isIphoneX, ifIphoneXHeaderHeight } = require('../../utils/ScreenUtil');
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../utils/CameraRollUtils';
import * as WeChat from 'react-native-wechat-lib';

var screenHeight = Dimensions.get('window').height;
export default class MemberManagementList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            // adjustFactor:"",
            searchKeyWordThree: "",
            classId: null,
            topBlockLayoutHeight: 0,
            modal: false,
            businessCard: "",
            showBusinessCard: false,
            shareModal: false,
            releaseModal: false,
            moreModal: false,
            deleteModal: false,
            memberItem: {},
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { staffId, classId } = route.params;
            this.loadResumeConfigList();
            this.loadMemberManagementList();

        }
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/cr/member/management/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "classId": this.state.classId,
            "staffType": "M",
            "staffState": "0AA",
            "sign": true,
            "searchKeyWordThree": this.state.searchKeyWordThree,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/cr/crmember/management/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "classId": this.state.classId,
            "staffType": "M",
            "staffState": "0AA",
            "sign": true,
            "searchKeyWordThree": this.state.searchKeyWordThree,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadMemberManagementList();
    }

    loadMemberManagementList = (classId) => {
        let url = "/biz/cr/member/management/list";
        let data = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "classId": classId ? classId : this.state.classId,
            "staffType": "M",
            "staffState": "0AA",
            "sign": true,
            "searchKeyWordThree": this.state.searchKeyWordThree,
        };
        httpPost(url, data, this.callBackLoadMemberManagementList);
    }

    callBackLoadMemberManagementList = (response) => {
        console.log('response.data.dataList==================', response.data.dataList)
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteStaff = (staffId) => {
        console.log("=======delete=staffId", staffId);
        let url = "/biz/cr/member/delete";
        let requestParams = { 'staffId': staffId, "staffType": "M" };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除成功" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    staffDisplaySetting = (item, index) => {
        console.log("=======staffDisplaySetting=staffId", item.staffId);
        let requestUrl = "/biz/cr/member/update_member_display";
        let requestParams = {
            'staffId': item.staffId,
            'resumeDisplay': item.resumeDisplay === 'Y' ? 'N' : 'Y',
            "staffType": "M",
        };
        httpPost(requestUrl, requestParams, (response) => {
            if (response.code == 200) {
                // 更新页面上订单状态
                item.resumeDisplay = (item.resumeDisplay === 'Y' ? 'N' : 'Y');
                WToast.show({ data: (item.resumeDisplay === 'Y' ? '显示' : '隐藏') + "会员完成" });
                let staffDataSource = this.state.dataSource;
                // JS 数组遍历
                staffDataSource.forEach((staffObj) => {
                    if (staffObj.staffId === item.staffId) {
                        staffObj.resumeDisplay = item.resumeDisplay;
                    }
                })
                this.setState({
                    dataSource: staffDataSource,
                })
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }


    exportPdfFile = (staffId) => {
        console.log("=======exportPdfFile");
        let url = "/biz/generate/pdf/business_card";
        let requestParams = {
            "staffId": staffId,
            "staffType": "M"
        };
        httpPost(url, requestParams, (response) => {
            if (response.code == 200 && response.data) {
                // Clipboard.setString(response.data); 
                // WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});

                // let ticket = response.data
                // let Ticket = value.setString(response.data)
                // console.log("zhunkai1",Ticket);
                this.setState({
                    businessCard: response.data,
                    modal: true,
                })

            }
        });
    }

    // 所属行业
    renderPortraitNameList = (value) => {
        return (
            <View key={value} style={[{
                backgroundColor: '#ECEEF2', marginRight: 8, marginTop: 5, paddingLeft: 6, paddingTop: 3, paddingRight: 6,
                paddingBottom: 3, borderRadius: 2, justifyContent: 'center', alignContent: 'center'
            }
            ]}>
                <Text style={[{ fontSize: 14, color: 'rgba(0, 10, 32, 0.65)', lineHeight: 20, textAlign: 'center' }]}>
                    {value}
                </Text>
            </View>
        )
    }

    renderRow = (memberItem) => {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("MemberManagementDetail", {
                    // 传递参数
                    staffName: memberItem.staffName,
                    staffId: memberItem.staffId,
                    searchKeyWordThree: this.state.searchKeyWordThree,
                    // 传递回调函数
                    refresh: this.callBackFunction
                })
            }}>

                <View key={memberItem.staffId} style={styles.innerViewStyle}>
                    <View style={{ position: 'absolute', left: 0, top: 0 }}>
                        {
                            // memberItem.resumeDisplay === "Y" ?
                            //     <View>
                            //         {/* <View style={[{ width: 0, height: 0, borderTopWidth: 40, borderTopColor: '#FF8C2860', borderRightWidth: 50, borderRightColor: 'transparent' }]}>
                            //         </View>
                            //         <View style={{ position: 'absolute', left: 2, top: 0 }}>
                            //             <Text style={{ fontSize: 12, color: '#FFFFFF',marginTop:1,marginLeft:1 }}>显示</Text>
                            //         </View> */}
                            //     </View>
                            //     :
                            //     <View>
                            //         {/* <View style={[{ width: 0, height: 0, borderTopWidth: 40, borderTopColor: 'rgba(173,172,173,0.4)', borderRightWidth: 50, borderRightColor: 'transparent' }]}>
                            //         </View>
                            //         <View style={{ position: 'absolute', left: 2, top: 0 }}>
                            //             <Text style={{ fontSize: 12, color: '#FFFFFF',marginTop:1,marginLeft:1 }}>已隐藏</Text>
                            //         </View> */}
                            //         <View style={{ width: 48, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#1BBC82' }}>
                            //             <Text style={{fontSize: 13, color: '#FFFFFF' }}>已隐藏</Text>
                            //         </View>
                            //     </View>
                        }
                    </View>

                    <View style={{ width: screenWidth - 31, flexDirection: 'row', marginLeft: 16, marginTop: 16, marginRight: 15 }}>
                        {
                            memberItem.electronicPhotos ?
                                <Image source={{ uri: constants.image_addr + '/' + memberItem.electronicPhotos }} style={{ height: 80, width: 80, borderRadius: 100 }} />
                                :
                                <Image style={{ height: 80, width: 80, borderRadius: 100 }} source={require('../../assets/icon/iconfont/head.png')}></Image>
                        }

                        <View style={{ marginLeft: 16, flexDirection: 'column' }}>
                            <View style={{ flexDirection: 'row', alignItems: 'flex-start', width: screenWidth - 180, flexWrap: 'wrap' }}>
                                <View style={{ flexDirection: 'row', marginRight: 16, flexWrap: 'wrap' }}>
                                    <Text style={{ fontSize: 18, fontWeight: 'bold', color: 'rgba(0, 10, 32, 0.85)', lineHeight: 24 }}>{memberItem.staffName}</Text>
                                    {
                                        memberItem.resumeDisplay === "Y" ?
                                            <View></View>
                                            :
                                            <View>
                                                <View style={{ width: 48, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'rgba(173,172,173,0.4)' }}>
                                                    <Text style={{fontSize: 13, color: '#FFFFFF' }}>已隐藏</Text>
                                                </View>
                                            </View>
                                    }
                                </View>
                            </View>
                            
                            <View style={{ flexDirection: 'row', width: screenWidth - 140, marginTop: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0, 10, 32, 0.85)', lineHeight: 20 }}>{memberItem.graduateInstitutions ? memberItem.graduateInstitutions : "暂无信息"}</Text>
                            </View>
                            <View style={{ flexDirection: 'row', marginTop: 2, flexWrap: 'wrap' }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0, 10, 32, 0.65)', lineHeight: 24 }}>{memberItem.staffPosition}</Text>
                            </View>
                            <View style={{ flexDirection: 'row', width: screenWidth - 128, flexWrap: 'wrap' }}>
                                {
                                    (memberItem.portraitNameList && memberItem.portraitNameList.length > 0)
                                        ?
                                        memberItem.portraitNameList.map((value, index) => {
                                            return this.renderPortraitNameList(value)
                                        })
                                        :
                                        <Text style={{ fontSize: 14, color: 'rgba(0, 10, 32, 0.65)', lineHeight: 20 }}>{memberItem.portraitName}</Text>
                                }
                            </View>


                        </View>

                        <View style={{ position: 'absolute', right: 8, top: 0 }}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    moreModal: true,
                                    memberItem: memberItem
                                })
                            }}>
                                <View style={[{ width: 35, height: 35, flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }]}>
                                    <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={styles.bodyViewStyle}>
                        <Text numberOfLines={3} style={{ fontSize: 14, fontWeight: "400", color: 'rgba(0, 10, 32, 0.65)', lineHeight: 24 }}>{memberItem.collegeEvaluation ? memberItem.collegeEvaluation : "暂无信息"}</Text>
                    </View>

                    <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>

                        <TouchableOpacity onPress={() => {
                            let message = '您确定要在会员系统网页中' + (memberItem.resumeDisplay === 'Y' ? '隐藏' : '显示') + '该会员吗？';
                            Alert.alert('确认', message, [
                                {
                                    text: "取消", onPress: () => {
                                        WToast.show({ data: '点击了取消' });
                                    }
                                },
                                {
                                    text: "确定", onPress: () => {
                                        WToast.show({ data: '点击了确定' });
                                        this.staffDisplaySetting(memberItem)
                                    }
                                }
                            ]);
                        }}>
                            <View style={[
                                {
                                    width: 80,
                                    height: 28,
                                    flexDirection: "row",
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    margin: 10,
                                    marginRight: 0,
                                    width: 75, flexDirection: "row", marginLeft: 10
                                }
                            ]}>
                                {/* {
                                staffItem.resumeDisplay === 'Y' ?
                                <Image style={{ width: 25, height: 30, marginRight: 5 }} source={require('../../assets/icon/iconfont/hide.png')}></Image>
                                :
                                <Image style={{ width: 25, height: 30, marginRight: 5 }} source={require('../../assets/icon/iconfont/show.png')}></Image>
                            } */}
                                {
                                    memberItem.resumeDisplay === 'Y' ?
                                        <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/password_hide.png')}></Image>
                                        :
                                        <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/password_show.png')}></Image>
                                }
                                <Text style={memberItem.resumeDisplay === 'Y' ?
                                    [CommonStyle.itemBottomDeleteBtnTextStyle, { color: 'rgba(0, 10, 32, 0.65)', fontSize: 14, lineHeight: 20 }]
                                    :
                                    [CommonStyle.itemBottomDetailBtnTextStyle, {color: 'rgba(0, 10, 32, 0.65)', fontSize: 14, lineHeight: 20 }]}>
                                    {memberItem.resumeDisplay === 'Y' ? '设为隐藏' : '设为显示'}
                                </Text>
                            </View>
                        </TouchableOpacity>
                        {/* <TouchableOpacity onPress={()=>{this.props.navigation.navigate("MemberManagementDetail", 
                        {
                            // 传递回调函数
                            staffName:memberItem.staffName,
                            staffId: memberItem.staffId,
                            // userPhotoUrl:constants.image_addr + '/' + item.electronicPhotos,
                            refresh: this.callBackFunction,
                            
                        })}}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {height: 28,backgroundColor:'rgba(27, 188, 130, 1)',marginRight:0, width: 75 ,flexDirection:"row"}]}>
                            <Image  style={{width:17, height:17,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                            <Text style={[CommonStyle.itemBottomDetailBtnTextStyle,{fontSize: 14, lineHeight: 20 }]}>详情</Text>
                        </View>
                    </TouchableOpacity> */}


                        <TouchableOpacity onPress={() => {
                            // Alert.alert('确认','您确定要导出准考证吗？',[
                            //     {
                            //         text:"取消", onPress:()=>{
                            //         WToast.show({data:'点击了取消'});
                            //         }
                            //     },
                            //     {
                            //         text:"确定", onPress:()=>{
                            //             WToast.show({data:'点击了确定'});
                            //             this.exportPdfFile(item.applyId)
                            //         }
                            //     }
                            // ]);

                            this.exportPdfFile(memberItem.staffId);
                        }}>
                            <View style={[{
                                fontSize: 16,
                                height: 30,
                                justifyContent: 'center',
                                alignItems: 'center',
                                margin: 10,
                                width: 95, flexDirection: "row", marginRight: 2
                            }]}>
                                <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/outputBlack.png')}></Image>
                                <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { fontSize: 14, lineHeight: 20, color: 'rgba(0, 10, 32, 0.65)' }]}>生成名片</Text>
                            </View>
                        </TouchableOpacity>

                        {/* <TouchableOpacity onPress={()=>{
                        console.log('===loadUserPWD:', memberItem.userId);
                        let loadTypeUrl= "/biz/portal/user/send_user_pwd";
                        let loadRequest={tenantId: constants.loginUser.tenantId, userId:memberItem.userId};
                        httpPost(loadTypeUrl, loadRequest, (response)=>{
                            if (response.code == 200 && response.data) {
                                WToast.show({data:"发送成功"});
                            }
                            else {
                                WToast.show({data:response.message});
                            }
                        });
                    }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { height:28,width:105,backgroundColor:"#5DD421",flexDirection:"row"}]}>
                            <Image style={{width:16, height:16,marginRight:5}} source={require('../../assets/icon/iconfont/sendPwd.png')}></Image>
                            <Text style={[CommonStyle.itemBottomDetailBtnTextStyle,{fontSize: 14, lineHeight: 20}]}>发送密码</Text>
                        </View>
                    </TouchableOpacity>

                     <TouchableOpacity onPress={()=>{
                        console.log('===loadUserId:', memberItem.userId);
                        let loadTypeUrl= "/biz/portal/user/get_pwd";
                        let loadRequest={tenantId: constants.loginUser.tenantId, userId:memberItem.userId};;
                        httpPost(loadTypeUrl, loadRequest, (response)=>{
                            if (response.code == 200 && response.data) {
                                console.log('===loadUserPWD:', response.data);
                                let userPwd = response.data;
                                Clipboard.setString(userPwd);
                                WToast.show({data:"复制成功"});
                            }
                            else {
                                WToast.show({data:response.message});
                            }
                            this.setState({
                                userPwd: response.data,
                            }) 
                        });
                    }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { height:28,width:105,marginRight:10,backgroundColor:"#FF8C28",flexDirection:"row"}]}>
                            <Image style={{width:16, height:16,marginRight:5}} source={require('../../assets/icon/iconfont/copyPwd.png')}></Image>
                            <Text style={[CommonStyle.itemBottomDetailBtnTextStyle,{fontSize: 14, lineHeight: 20}]}>复制密码</Text>
                        </View>
                    </TouchableOpacity> */}

                        {/* <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该会员吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteStaff(memberItem.staffId)
                                }
                            }
                        ]);
                    }}>

                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle,{width:75,flexDirection:"row",marginRight:0}]}>
                            <Image style={{ width: 24, height: 24, marginRight: 5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity onPress={()=>this.props.navigation.navigate("MemberManagementAdd",
                    {
                        staffId:memberItem.staffId,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle,{width:75,marginRight:10,flexDirection:"row"}]}>
                            <Image style={{ width: 17, height: 17, marginRight: 5 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20}}>编辑</Text>
                        </View>
                    </TouchableOpacity> */}

                    </View>
                </View>
            </TouchableOpacity>
        )
    }

    // 分隔线
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View style={{ flexDirection: 'row' }}>

                {/* <TouchableOpacity onPress={() => {
                    this.props.navigation.navigate("MemberManagementExamine",
                        {
                            // 传递回调函数
                            refresh: this.callBackFunction
                        })
                }}>
                    <Text style={CommonStyle.headRightText}>审核入库</Text>
                    <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/shenheruku.png')}></Image>
                
                </TouchableOpacity> */}

                {/* <TouchableOpacity onPress={() => {
                    this.setState({
                        shareModal:true
                })
                }}> */}
                {/* <Text style={CommonStyle.headRightText}>审核入库</Text> */}
                {/* <Image style={{ width:27, height:27,marginLeft:5 }} source={require('../../assets/icon/iconfont/shareBlack.png')}></Image> */}

                {/* </TouchableOpacity> */}

                <TouchableOpacity onPress={() => {
                    this.props.navigation.navigate("MemberManagementAdd",
                        {
                            // 传递回调函数
                            refresh: this.callBackFunction
                        })
                }}>
                    {/* <Text style={CommonStyle.headRightText}>半成品点验</Text> */}
                    <Image style={{ width: 27, height: 27, marginLeft: 5 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>

        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/cr/member/management/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "staffType": "M",
            "classId": this.state.classId,
            "staffState": "0AA",
            "searchKeyWordThree": this.state.searchKeyWordThree,
            "sign": true
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    loadResumeConfigList = () => {
        let url = "/biz/tenant/config/list";
        let loadRequest = {
        };
        httpPost(url, loadRequest, this.loadResumeConfigListCallBack);
    }

    loadResumeConfigListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            var resumeQueryTitle = data.filter(item => (item.paramCode == 'MEMBER_SHARE_TITLE'));
            var resumeQuerySubTitle = data.filter(item => (item.paramCode == 'MEMBER_SHARE_SUB_TITLE'));
            var resumeQueryLogo = data.filter(item => (item.paramCode == 'MEMBER_SHARE_LOGO'));
            console.log("resumeQueryTitle==", resumeQueryTitle && resumeQueryTitle.length == 1 ? resumeQueryTitle[0].paramValue : null);
            console.log("resumeQuerySubTitle==", resumeQuerySubTitle && resumeQuerySubTitle.length == 1 ? resumeQuerySubTitle[0].paramValue : null);
            console.log("resumeQueryLogo==", resumeQueryLogo && resumeQueryLogo.length == 1 ? resumeQueryLogo[0].paramValue : null);
            this.setState({
                resumeQueryTitle: (resumeQueryTitle && resumeQueryTitle.length == 1) ? resumeQueryTitle[0].paramValue : null,
                resumeQuerySubTitle: (resumeQuerySubTitle && resumeQuerySubTitle.length == 1) ? resumeQuerySubTitle[0].paramValue : null,
                resumeQueryLogo: (resumeQueryLogo && resumeQueryLogo.length == 1) ? resumeQueryLogo[0].paramValue : null
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='会员管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <View style={[styles.innerViewStyle, { marginTop: 0, borderColor: "#FFFFFF", backgroundColor: "#FFFFFF"}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ flexDirection: 'column' }}>
                        <View style={[styles.inputRowStyle, { width: screenWidth - 16, height: 40, flexDirection: 'row', justifyContent: "space-around", alignItems: "center" }]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    shareModal: true
                                })
                            }}>
                                <View style={[{
                                    fontSize: 16,
                                    height: 30,
                                    borderWidth: 0.85,
                                    borderColor: '#1BBC82',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    borderRadius: 6,
                                    flexDirection: "row",
                                    paddingLeft: 5,
                                    paddingRight: 5
                                }]}>

                                    <Image style={{ width: 18, height: 18, marginRight: 5 }} source={require('../../assets/icon/iconfont/shareGreen.png')}></Image>
                                    <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { color: '#1BBC82', fontSize: 16 }]}>分享会员系统</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    releaseModal: true
                                })
                            }}>
                                <View style={[{
                                    fontSize: 16,
                                    height: 30,
                                    borderWidth: 0.85,
                                    borderColor: '#1E6EFA',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    borderRadius: 6,
                                    flexDirection: "row",
                                    paddingLeft: 5,
                                    paddingRight: 5
                                }]}>
                                    <Image style={{ width: 18, height: 18, marginRight: 5 }} source={require('../../assets/icon/iconfont/outputBlue.png')}></Image>
                                    <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { color: '#1E6EFA', fontSize: 16 }]}>发布申请入库</Text>
                                </View>
                            </TouchableOpacity>
                        </View>

                        <View style={[styles.inputRowStyle , { backgroundColor: "#F2F5FC", borderRadius: 15}]}>
                            <View style={styles.leftLabView}>
                                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                placeholderTextColor= "rgba(0, 10, 32, 0.5)"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'姓名/公司/组织/行业'}
                                onChangeText={(text) => this.setState({ searchKeyWordThree: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                            {/*<TouchableOpacity onPress={() => {
                                this.searchByKeyWord();
                            }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 70,backgroundColor:'#DEB887',borderColor:'#DEB887' }]}>
                                    <Text style={[CommonStyle.itemBottomDeleteBtnTextStyle,{color:'#FFFFFF'}]}>查询</Text>
                                </View>
                        </TouchableOpacity>*/}
                        </View>


                    </View>


                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        ItemSeparatorComponent={this.space}
                        ListEmptyComponent={this.emptyComponent}
                        renderItem={({ item }) => this.renderRow(item)}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>



                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.modal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle, { width: screenWidth, padding: 0, height: ifIphoneXBodyViewHeight() - 60 }]}>
                            <ScrollView>
                                <View styles={{ width: screenWidth }}>
                                    {
                                        this.state.businessCard
                                            ?
                                            <View>
                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        showBusinessCard: true,
                                                    })
                                                    console.log("个人名片地址", this.state.businessCard);
                                                }}

                                                    onLongPress={() => {

                                                        Alert.alert('确认', '您确定要保存该名片吗？', [
                                                            {
                                                                text: "取消", onPress: () => {
                                                                    WToast.show({ data: '点击了取消' });
                                                                    // this在这里可用，传到方法里还有问题
                                                                    // this.props.navigation.goBack();
                                                                }
                                                            },
                                                            {
                                                                text: "确定", onPress: () => {
                                                                    WToast.show({ data: '点击了确定' });
                                                                    var BusinessCard = this.state.businessCard;
                                                                    saveImage(BusinessCard);
                                                                }
                                                            }
                                                        ]);


                                                    }}
                                                >
                                                    <Image source={{ uri: this.state.businessCard }} style={{ width: screenWidth, height: ifIphoneXContentViewHeight(), justifyContent: 'center', alignItems: 'center' }} />
                                                    <TouchableOpacity style={{ position: 'absolute', justifyContent: 'center', width: screenWidth, alignItems: "center", bottom: 50 }}
                                                        onPress={() => {
                                                            this.setState({
                                                                modal: false,
                                                            })
                                                        }}
                                                    >
                                                        <Image style={{ width: 50, height: 50, justifyContent: 'center' }} source={require('../../assets/icon/iconfont/cancel2.png')}></Image>
                                                    </TouchableOpacity>

                                                </TouchableOpacity>
                                                <Modal visible={this.state.showBusinessCard} transparent={true}>
                                                    <ImageViewer enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                        saveToLocalByLongPress={true}
                                                        onClick={() => { // 图片单击事件
                                                            this.setState({
                                                                showBusinessCard: false,
                                                            })
                                                        }}
                                                        imageUrls={[{ url: this.state.businessCard }]}
                                                        onSave={() => {
                                                            var BusinessCard = this.state.businessCard;
                                                            saveImage(BusinessCard);
                                                        }}
                                                    />
                                                </Modal>
                                            </View>

                                            : <EmptyListComponent />
                                    }
                                </View>
                            </ScrollView>
                        </View>
                    </View>
                </Modal>

                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.shareModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
                        <View style={{ width: screenWidth, height: 250, bottom: 0, position: 'absolute', backgroundColor: '#f0f0f0' }}>
                            <View style={{ height: 55, justifyContent: 'center', alignItems: 'center' }}>
                                <Text style={{ fontSize: 18 }}>
                                    选择分享方式
                                </Text>
                            </View>
                            <View style={{ height: 105, flexDirection: 'row', justifyContent: 'center', index: 1000 }}>
                                <TouchableOpacity
                                    onPress={() => {
                                        console.log("标题=====", this.state.resumeQueryTitle ? this.state.resumeQueryTitle : '江苏省数字经济联合会-会员库')
                                        console.log("副标题=====", this.state.resumeQuerySubTitle ? this.state.resumeQuerySubTitle : '江苏省数字经济联合会-会员库')
                                        console.log("log=====", this.state.resumeQueryLogo ? this.state.resumeQueryLogo : '江苏省数字经济联合会-会员库')
                                        // 分享微信好友
                                        WeChat.shareWebpage({
                                            title: this.state.resumeQueryTitle ? this.state.resumeQueryTitle : '江苏省数字经济联合会-会员库',
                                            description: this.state.resumeQuerySubTitle ? this.state.resumeQuerySubTitle : '江苏省数字经济联合会-会员库',
                                            thumbImageUrl: this.state.resumeQueryLogo ? (constants.image_addr + '/' + this.state.resumeQueryLogo) : 'https://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/logo/jiangsu_digital_economy_ederation_logo.jpg',
                                            webpageUrl: 'https://jzxs.njjzgk.com/html/memberQuery/list.html?tenantId=' + constants.loginUser.tenantId,
                                            scene: 0
                                        })
                                            .then((respJSON) => {
                                                WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                            })
                                            .catch((error) => {
                                                WToast.show({ data: error });
                                                Alert.alert(error.message);
                                            });
                                    }}>
                                    <View style={[{ width: 100, flexDirection: "column", margin: 5, justifyContent: 'center', alignItems: 'center' }]}>
                                        <Image style={{ width: 40, height: 40, marginRight: 2 }} source={require('../../assets/icon/iconfont/WeChat.png')}></Image>
                                        <Text style={[CommonStyle.itemBottomEditBtnTextStyle, { color: '#000000' }]}>微信好友</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', borderTopWidth: 1, borderTopColor: '#cccccc' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        shareModal: false
                                    })
                                }}>
                                    <View style={{ width: screenWidth, justifyContent: 'center', alignItems: 'center' }}>
                                        <Text style={[{ fontSize: 18 }]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>


                        </View>
                    </View>
                </Modal>

                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.releaseModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
                        <View style={{ width: screenWidth, height: 250, bottom: 0, position: 'absolute', backgroundColor: '#f0f0f0' }}>
                            <View style={{ height: 55, justifyContent: 'center', alignItems: 'center' }}>
                                <Text style={{ fontSize: 18 }}>
                                    选择分享方式
                                </Text>
                            </View>
                            <View style={{ height: 105, flexDirection: 'row', justifyContent: 'center', index: 1000 }}>
                                <TouchableOpacity
                                    onPress={() => {
                                        // console.log("标题=====",this.state.resumeQueryTitle?this.state.resumeQueryTitle:'极致私域资源运营服务平台')
                                        // console.log("副标题=====",this.state.resumeQuerySubTitle?this.state.resumeQuerySubTitle:'增加会员获得感，加强会员粘性，吸纳新会员')
                                        // console.log("log=====",this.state.resumeQueryLogo?this.state.resumeQueryLogo:'极致教育logo')
                                        // 分享微信好友
                                        WeChat.shareWebpage({
                                            title: '申请入库',
                                            //description: '增加会员获得感，加强会员粘性，吸纳新会员',
                                            thumbImageUrl: this.state.resumeQueryLogo ? this.state.resumeQueryLogo : 'https://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/logo/jiangsu_digital_economy_ederation_logo.jpg',
                                            webpageUrl: 'https://jzxs.njjzgk.com/html/memberQuery/applyStorage.html?tenantId=' + constants.loginUser.tenantId,
                                            scene: 0
                                        })
                                            .then((respJSON) => {
                                                WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                            })
                                            .catch((error) => {
                                                WToast.show({ data: error });
                                                Alert.alert(error.message);
                                            });
                                    }}>
                                    <View style={[{ width: 100, flexDirection: "column", margin: 5, justifyContent: 'center', alignItems: 'center' }]}>
                                        <Image style={{ width: 40, height: 40, marginRight: 2 }} source={require('../../assets/icon/iconfont/WeChat.png')}></Image>
                                        <Text style={[CommonStyle.itemBottomEditBtnTextStyle, { color: '#000000' }]}>微信好友</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => {
                                        // 分享朋友圈
                                        WeChat.shareWebpage({
                                            title: '申请入库',
                                            //description: '增加会员获得感，加强会员粘性，吸纳新会员',
                                            thumbImageUrl: this.state.resumeQueryLogo ? this.state.resumeQueryLogo : 'https://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/logo/jiangsu_digital_economy_ederation_logo.jpg',
                                            webpageUrl: 'https://jzxs.njjzgk.com/html/memberQuery/applyStorage.html?tenantId=' + constants.loginUser.tenantId,
                                            scene: 1
                                        })
                                            .catch((error) => {
                                                WToast.show({ data: error });
                                                Alert.alert(error.message);
                                            });
                                    }}>
                                    <View style={[{ width: 100, flexDirection: "column", margin: 5, justifyContent: 'center', alignItems: 'center' }]}>
                                        <Image style={{ width: 40, height: 40 }} source={require('../../assets/icon/iconfont/WeChatFriendsCircle2.png')}></Image>
                                        <Text style={[CommonStyle.itemBottomEditBtnTextStyle, { color: '#000000' }]}>朋友圈</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', borderTopWidth: 1, borderTopColor: '#cccccc' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        releaseModal: false
                                    })
                                }}>
                                    <View style={{ width: screenWidth, justifyContent: 'center', alignItems: 'center' }}>
                                        <Text style={[{ fontSize: 18 }]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>


                        </View>
                    </View>
                </Modal>

                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false,
                                    })
                                    this.props.navigation.navigate("MemberManagementAdd",
                                        {
                                            // 传递参数
                                            staffId: this.state.memberItem.staffId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }}>
                                    <View style={[{ width: 145, height: 50, paddingLeft: 30, marginTop: 5 }]}>
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => {
                                    // 删除弹窗Modal
                                    this.setState({
                                        moreModal: false,
                                        deleteModal: true
                                    })

                                }}>
                                    <View style={[{ width: 145, height: 50, paddingLeft: 30, marginTop: 5 }]}>
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {/* <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false,
                                    })
                                    console.log('===loadUserPWD:', this.state.memberItem.userId);
                                    let loadTypeUrl = "/biz/portal/user/send_user_pwd";
                                    let loadRequest = { tenantId: constants.loginUser.tenantId, userId: this.state.memberItem.userId };
                                    httpPost(loadTypeUrl, loadRequest, (response) => {
                                        if (response.code == 200 && response.data) {
                                            WToast.show({ data: "发送成功" });
                                        }
                                        else {
                                            WToast.show({ data: response.message });
                                        }
                                    });
                                }}>
                                    <View style={[{ width: 145, height: 50, paddingLeft: 30, marginTop: 5 }]}>
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>发送密码</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false,
                                    })
                                    console.log('===loadUserId:', this.state.memberItem.userId);
                                    let loadTypeUrl = "/biz/portal/user/get_pwd";
                                    let loadRequest = { tenantId: constants.loginUser.tenantId, userId: this.state.memberItem.userId };;
                                    httpPost(loadTypeUrl, loadRequest, (response) => {
                                        if (response.code == 200 && response.data) {
                                            console.log('===loadUserPWD:', response.data);
                                            let userPwd = response.data;
                                            Clipboard.setString(userPwd);
                                            WToast.show({ data: "复制成功" });
                                        }
                                        else {
                                            WToast.show({ data: response.message });
                                        }
                                        this.setState({
                                            userPwd: response.data,
                                        })
                                    });
                                }}>
                                    <View style={[{ width: 145, height: 50, paddingLeft: 30, marginTop: 5 }]}>
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>复制密码</Text>
                                    </View>
                                </TouchableOpacity>
                            </View> */}

                            <View style={{ width: 291, height: 50, alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该会员?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteStaff(this.state.memberItem.staffId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

            </View>
        )
    }
}

const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#FFFFFF",
        backgroundColor: "#FFFFFF",
        borderRadius: 5,
        marginTop: 2
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 1.2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop: 0
    },
    innerViewStyle: {
        // marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10
    },
    titleTextStyle: {
        fontSize: 23
    },
    bodyViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 16,
        marginRight: 15,
        marginBottom: 8,
        marginTop: 8
    },
    bodyRowLeftView: {
        width: screenWidth / 2 - 40,
        flexDirection: 'row'
    },
    bodyRowRightView: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        paddingLeft: 10,
        marginRight: 5,
        justifyContent: 'flex-start',
        alignContent: 'flex-start'
    },
    photos: {
        width: 150,
        height: 200,
        // borderRadius:50,
        borderWidth: 0,
        // marginTop:80,
        // marginBottom:30
    },
})
module.exports = MemberManagementList;
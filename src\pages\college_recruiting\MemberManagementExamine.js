import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl,TextInput,Image,Clipboard
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class MemberManagementExamine extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            // adjustFactor:"",
            topBlockLayoutHeight: 0, 
            selExaminetateCode: 'all',
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');

        let examineStateDataSource = [
            {
                stateCode: 'all',
                stateName: '全部',
            },
            {
                stateCode: '0AB',
                stateName: '待审核',
            },
            {
                stateCode: '0AA',
                stateName: '已通过',
            },
            {
                stateCode: '0BB',
                stateName: '未通过',
            }
        ]
        this.setState({
            examineStateDataSource: examineStateDataSource,
        })

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { staffId ,classId} = route.params;
    
            this.loadMemberManagementList();
        
    }

    
}

    // loadAdjustFactor=()=>{
    //     let url = "/biz/tenant/get";
    //     let loadRequest = {
    //         "operateTenantId":constants.loginUser.tenantId
    //     };
    //     httpPost(url, loadRequest, this.loadAdjustFactorCallBack);
    // }

    // loadAdjustFactorCallBack=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             adjustFactor: response.data.adjustFactor,
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

     // 回调函数
     callBackFunction=()=>{
        let url= "/biz/cr/member/management/listByTime";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            //"classId":this.state.classId,
            "staffType":"M",
            "sign":true,
            "staffState": this.state.selExaminetateCode === 'all' ? null : this.state.selExaminetateCode,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/cr/crmember/management/listByTime";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            //"classId": this.state.classId,
            "staffType":"M",
            "sign":true,
            "staffState": this.state.selExaminetateCode === 'all' ? null : this.state.selExaminetateCode,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // console.log(response.data.dataList);
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadMemberManagementList();
    }

    loadMemberManagementList=(classId)=>{
        let url= "/biz/cr/member/management/listByTime";
        let data={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "staffType":"M",
            "sign":true,
            "staffState": this.state.selExaminetateCode === 'all' ? null : this.state.selExaminetateCode,

        };
        httpPost(url, data, this.callBackLoadMemberManagementList);
    }

    callBackLoadMemberManagementList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }


    staffDisplaySetting = (item, index) => {
        console.log("=======staffDisplaySetting=staffId", item.staffId);
        let requestUrl = "/biz/cr/member/update_member_display";
        let requestParams = {
            'staffId': item.staffId,
            'resumeDisplay': "Y",
            "staffType":"M",
        };
        httpPost(requestUrl, requestParams, this.callBackFunction);
    }

    renderAssessRecordStateRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    let selExaminetateCode = item.stateCode;
                    this.setState({
                        "selExaminetateCode":selExaminetateCode
                    })
                    console.log(this.state.selExaminetateCode);
                    let url= "/biz/cr/member/management/listByTime";
                    let data={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "staffType":"M",
                        "sign":true,
                        "staffState": selExaminetateCode === 'all' ? null : selExaminetateCode,

                    };
                    httpPost(url, data, this._loadFreshDataCallBack);
                }}>
                    <View key={item.stateCode} style={[CommonStyle.tabItemViewStyle]}>
                        <Text style={[item.stateCode === this.state.selExaminetateCode ?
                            [CommonStyle.selectedtabItemTextStyle]
                            :
                            [CommonStyle.tabItemTextStyle]
                        ]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    // 所属行业
    renderPortraitNameList = (value) => {
        return (
            <View key={value} style={[{
                backgroundColor: '#ECEEF2', marginRight: 8, marginTop: 5, paddingLeft: 6, paddingTop: 3, paddingRight: 6,
                paddingBottom: 3, borderRadius: 2, justifyContent: 'center', alignContent: 'center'
            }
            ]}>
                <Text style={[{ fontSize: 14, color: 'rgba(0, 10, 32, 0.65)', lineHeight: 20, textAlign: 'center' }]}>
                    {value}
                </Text>
            </View>
        )
    }

    renderRow=(memberItem)=>{
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("MemberManagementExamineDetail", {
                    // 传递参数
                    staffId: memberItem.staffId,
                    staffState: this.state.selExaminetateCode === 'all' ? null : this.state.selExaminetateCode,
                    // 传递回调函数
                    refresh1: this.props.route.params.refresh,
                    refresh2: this.callBackFunction
                })
            }}>
                <View key={memberItem.staffId} style={styles.innerViewStyle}>
                
                    <View style={{ width: screenWidth - 31, flexDirection: 'row', marginLeft: 16, marginTop: 16, marginRight: 15 }}>
                        {
                            memberItem.electronicPhotos ?
                                <Image source={{ uri: constants.image_addr + '/' + memberItem.electronicPhotos }} style={{ height: 80, width: 80, borderRadius: 100 }} />
                                :
                                <Image style={{ height: 80, width: 80, borderRadius: 100 }} source={require('../../assets/icon/iconfont/head.png')}></Image>
                        }

                        <View style={{ marginLeft: 16, flexDirection: 'column' }}>
                            <View style={{ flexDirection: 'row', alignItems: 'flex-start', width: screenWidth - 185, flexWrap: 'wrap' }}>
                                <View style={{ flexDirection: 'row', marginRight: 16, flexWrap: 'wrap' }}>
                                    <Text style={{ fontSize: 18, fontWeight: 'bold', color: 'rgba(0, 10, 32, 0.85)', lineHeight: 24 }}>{memberItem.staffName}</Text>
                                    {
                                        memberItem.staffState ==="0AA" ? 
                                        <View style={{ width: 52, height: 20, borderRadius: 2, marginLeft: 7,flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: "#3ab240" }}>
                                            <Text style={{fontSize: 13, color: '#FFFFFF' }}>已通过</Text>
                                        </View>
                                        :
                                        <View>
                                            {
                                                memberItem.staffState === "0AB" ?
                                                <View style={{ width: 52, height: 20, borderRadius: 2, marginLeft: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: '#FF8C28' }}>
                                                    <Text style={{fontSize: 13, color: '#FFFFFF' }}>待审核</Text>
                                                </View>
                                                :
                                                <View style={{ width: 52, height: 20, borderRadius: 2, marginLeft: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: 'rgba(255,0,0,0.4)' }}>
                                                    <Text style={{fontSize: 13, color: '#FFFFFF' }}>未通过</Text>
                                                </View>
                                            }
                                            
                                        </View>
                                    }
                                </View>
                                {/* <View style={{ flexDirection: 'row', marginTop: 2, flexWrap: 'wrap' }}>
                                    <Text style={{ fontSize: 14, color: 'rgba(0, 10, 32, 0.65)', lineHeight: 24 }}>{memberItem.staffPosition}</Text>
                                </View> */}
                            </View>

                            <View style={{ flexDirection: 'row', width: screenWidth - 140, marginTop: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0, 10, 32, 0.85)', lineHeight: 20 }}>{memberItem.graduateInstitutions ? memberItem.graduateInstitutions : "暂无信息"}</Text>
                            </View>
                            {
                                memberItem.staffPosition != null ?
                                <View style={{ flexDirection: 'row', marginTop: 5, flexWrap: 'wrap' }}>
                                    <Text style={{ fontSize: 14, color: 'rgba(0, 10, 32, 0.65)', lineHeight: 24 }}>{memberItem.staffPosition}</Text>
                                </View>
                                :
                                <View></View>
                            }
                            
                            <View style={{ flexDirection: 'row', width: screenWidth - 128, flexWrap: 'wrap'}}>
                                {
                                    (memberItem.portraitNameList && memberItem.portraitNameList.length > 0)
                                        ?
                                        memberItem.portraitNameList.map((value, index) => {
                                            return this.renderPortraitNameList(value)
                                        })
                                        :
                                        <Text style={{ fontSize: 14, color: 'rgba(0, 10, 32, 0.65)', lineHeight: 20 }}>{memberItem.portraitName}</Text>
                                }
                            </View>


                        </View>

                    </View>

                    <View style={styles.bodyViewStyle}>
                        <Text numberOfLines={3} style={{ fontSize: 14, fontWeight: "400", color: 'rgba(0, 10, 32, 0.65)', lineHeight: 24 }}>{memberItem.collegeEvaluation ? memberItem.collegeEvaluation : "暂无信息"}</Text>
                    </View>
                    {/* <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    {
                        memberItem.staffState === "0AA" ?
                        <View></View>
                        :
                        <TouchableOpacity onPress={()=>{this.props.navigation.navigate("MemberManagementExamineDetail", 
                            {
                                // 传递回调函数
                                staffId: memberItem.staffId,
                                refresh1: this.props.route.params.refresh,
                                refresh2: this.callBackFunction
                            })}}>
                            <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 65,backgroundColor: "#1E6EFA", flexDirection: "row", marginRight: 10 }]}>
                                <Image style={{ width: 16, height: 18, marginRight: 3 }} source={memberItem.staffState ==='0AB' ? require('../../assets/icon/iconfont/audit2.png') :  require('../../assets/icon/iconfont/restart.png')}></Image>
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>审核</Text>
                            </View>
                        </TouchableOpacity>   
                    }
                    </View> */}
                </View>
            </TouchableOpacity>
        )
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View>
                {/* <TouchableOpacity onPress={() => {
                    this.props.navigation.navigate("MemberManagementList",
                        {
                            // 传递回调函数
                            refresh: this.callBackFunction
                        })
                }}>
                     <Text style={CommonStyle.headRightText}>会员管理</Text> 
                
                </TouchableOpacity> */}

            </View>
            
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='入库审核'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                
                <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                    {
                        (this.state.examineStateDataSource && this.state.examineStateDataSource.length > 0) 
                        ? 
                        this.state.examineStateDataSource.map((item, index)=>{
                            return this.renderAssessRecordStateRow(item)
                        })
                        : <View/>
                    }  
                    </View> 
                </View> 

                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList 
                        data={this.state.dataSource}
                        ItemSeparatorComponent={this.space}
                        ListEmptyComponent={this.emptyComponent}
                        renderItem={({item}) => this.renderRow(item)}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 1.2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle:{
        // marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10
    },
    titleTextStyle:{
        fontSize:23
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyRowLeftView:{
        width:screenWidth/2-40, 
        flexDirection:'row'
    },
    bodyRowRightView:{
        flexDirection:'row', 
        alignItems:'flex-start',
        paddingLeft:10,
        marginRight:5, 
        justifyContent:'flex-start',
        alignContent:'flex-start'
    }
})
module.exports = MemberManagementExamine;
import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,Image,FlatList,TouchableOpacity,Dimensions,Modal} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class TenantAdd extends Component {
    constructor(){
        super()
        this.state = {
            operateTenantId:"",

            managerTenantId:"",
            managerTenantName:"",

            tenantName:"",
            tenantAbbreviation:"",
            operate:"",
            userId:"",
            userCode:"",
            userPwd:"",
            editDeleteTimeLimit:168,
            tenantLogo:"",
            tenantLoginBackground:"",
            tenantStyleTitle:"",
            mobileStyleHeadBackgroundColor:"#CB4139",
            mobileStyleBottomNavigateBackgroundColor:"#353B45",
            mobileStyleBodyBackgroundColor:"#FFFFFF",
            passwordEffectDuration:null,
            continueStorageOutFlag:null,
            adjustFactor:"",
            menuTypeSource:[],
            menuTypeList:[],
            orgDataSource:[],
            selectOrganization:[],
            orgName:"",
            orgId:"",
            salePersonName:"",
            salePersonTel:"",

            searchKeyWord:"",
            tenantList:[],
            modal:false,
            tenantSort:0
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadOrgList();
        let menuTypeSource = [
            {
                typeId:0,
                typeName:"耐材行业",
                typeCode:"M"
            },
            {
                typeId:1,
                typeName:"数字化管理",
                typeCode:"D"
            },
            {
                typeId:2,
                typeName:"就业实习平台",
                typeCode:"R"
            },
            {
                typeId:3,
                typeName:"后勤运营",
                typeCode:"H"
            },
            {
                typeId:4,
                typeName:"会员系统",
                typeCode:"V"
            }
        ]
        this.setState({
            menuTypeSource:menuTypeSource
        })
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { operateTenantId } = route.params;
            if (operateTenantId) {
                console.log("========Edit==operateTenantId:", operateTenantId);
                this.setState({
                    operateTenantId:operateTenantId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/tenant/get";
                loadRequest={'operateTenantId':operateTenantId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditTenantDataCallBack);
            }
            else {
                this.setState({
                    operate:"添加",
                    userPwd:"1234567",
                })
            }
        }
        this.loadTenantList();
    }

    loadTenantList=()=>{
        let url= "/biz/tenant/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this.loadTenantListCallBack);
    }

    loadTenantListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataAll = response.data.dataList;
            this.setState({
                tenantList:dataAll,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }


    loadEditTenantDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                tenantId:response.data.tenantId,
                managerTenantId:response.data.managerTenantId,
                managerTenantName:response.data.managerTenantName,
                tenantName:response.data.tenantName,
                tenantAbbreviation:response.data.tenantAbbreviation,
                tenantContact:response.data.tenantContact,
                tenantTel:response.data.tenantTel,
                userId:response.data.userId,
                userCode:response.data.userCode,
                userPwd:response.data.userPwd,
                editDeleteTimeLimit:response.data.editDeleteTimeLimit,
                tenantLogo:response.data.tenantLogo,
                tenantLoginBackground:response.data.tenantLoginBackground,
                tenantStyleTitle:response.data.tenantStyleTitle,
                mobileStyleHeadBackgroundColor:response.data.mobileStyleHeadBackgroundColor ? response.data.mobileStyleHeadBackgroundColor : "#CB4139",
                mobileStyleBottomNavigateBackgroundColor:response.data.mobileStyleBottomNavigateBackgroundColor ? response.data.mobileStyleBottomNavigateBackgroundColor : "#353B45",
                mobileStyleBodyBackgroundColor:response.data.mobileStyleBodyBackgroundColor ? response.data.mobileStyleBodyBackgroundColor : "#FFFFFF",
                passwordEffectDuration:response.data.passwordEffectDuration ? response.data.passwordEffectDuration : null,
                continueStorageOutFlag:response.data.continueStorageOutFlag ? response.data.continueStorageOutFlag : null,
                adjustFactor:response.data.adjustFactor,
                menuTypes:response.data.menuTypes,
                selectOrganization: [response.data.orgName],
                orgName:response.data.orgName,
                orgId:response.data.orgId,
                salePersonName:response.data.salePersonName,
                salePersonTel:response.data.salePersonTel,
                tenantSort:response.data.tenantSort
            })
            var list = [];
            if(response.data.menuTypes){
                var menuTypeList = response.data.menuTypes.split(",")
                console.log("====menuTypeList===="+menuTypeList)
                for(var i=0; i< menuTypeList.length; i++){
                  list =  list.concat(menuTypeList[i])
                }
                console.log(list)
                this.setState({
                    menuTypeList:list
                })
            }
        }
    }

    loadOrgList=()=>{
        let loadTypeUrl= "/biz/org/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000
        };

        httpPost(loadTypeUrl, loadRequest, this.loadOrgListCallBack);

    }

    loadOrgListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                orgDataSource:response.data.dataList
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("TenantList")
            }}>
                <Text style={CommonStyle.headRightText}>租户管理</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveTenant =()=> {
        console.log("=======saveTenant");
        let toastOpts;
        if (!this.state.tenantName) {
            toastOpts = getFailToastOpts("请输入租户名称");
            WToast.show(toastOpts)
            return;
        }
        if(this.state.tenantAbbreviation.length > 6){
            toastOpts = getFailToastOpts("租户简称不可超过6位");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.tenantContact) {
            toastOpts = getFailToastOpts("请输入联系人");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.tenantTel) {
            toastOpts = getFailToastOpts("请输入联系电话");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.userCode) {
            toastOpts = getFailToastOpts("请输入用户名");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.userPwd) {
            toastOpts = getFailToastOpts("请输入密码");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.editDeleteTimeLimit) {
            toastOpts = getFailToastOpts("请输入按钮时效");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.orgId) {
        //     toastOpts = getFailToastOpts("请选择所属组织");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.menuTypeList || this.state.menuTypeList.length == 0) {
            toastOpts = getFailToastOpts("请选择平台菜单");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/tenant/add";
        if (this.state.operateTenantId) {
            console.log("=========Edit===operateTenantId", this.state.operateTenantId)
            url= "/biz/tenant/modify";
        }
        let requestParams={
            "operateTenantId":this.state.operateTenantId,
            "managerTenantId":this.state.managerTenantId,
            "tenantName":this.state.tenantName,
            "tenantAbbreviation":this.state.tenantAbbreviation,
            "tenantContact":this.state.tenantContact,
            "tenantTel":this.state.tenantTel,
            "userId":this.state.userId,
            "userCode":this.state.userCode,
            "userPwd":this.state.userPwd,
            "editDeleteTimeLimit":this.state.editDeleteTimeLimit,
            "tenantLogo":this.state.tenantLogo,
            "tenantLoginBackground":this.state.tenantLoginBackground,
            "tenantStyleTitle":this.state.tenantStyleTitle,
            "mobileStyleHeadBackgroundColor":this.state.mobileStyleHeadBackgroundColor,
            "mobileStyleBottomNavigateBackgroundColor":this.state.mobileStyleBottomNavigateBackgroundColor,
            "mobileStyleBodyBackgroundColor":this.state.mobileStyleBodyBackgroundColor,
            "passwordEffectDuration":this.state.passwordEffectDuration,
            "continueStorageOutFlag":this.state.continueStorageOutFlag,
            "adjustFactor":this.state.adjustFactor,
            "orgId":this.state.orgId,
            "menuTypes":this.state.menuTypeList.toString(),
            "salePersonName":this.state.salePersonName,
            "salePersonTel":this.state.salePersonTel,
            "tenantSort":this.state.tenantSort
        };
        httpPost(url, requestParams, this.saveTenantCallBack);
    }
    renderTenantItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                if(item.tenantId==this.state.managerTenantId)
                {
                    this.setState({
                        managerTenantId:null,
                        managerTenantName:null,
                    })
                }
                else{
                    this.setState({
                        managerTenantId:item.tenantId,
                        managerTenantName:item.tenantName,
                    })
                }
            }}>
                <View key={item.tenantId} style={item.tenantId===this.state.managerTenantId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.tenantId===this.state.managerTenantId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.tenantName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 保存回调函数
    saveTenantCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    compare=(typeCode)=>{
        for(var i=0;i<this.state.menuTypeList.length;i++){
            if(this.state.menuTypeList[i] == typeCode){
                return true;
            }
        }
        return false;
    }

    // 员工单项渲染
    renderMenuTypeRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                var menuTypeList = this.state.menuTypeList;
                if (this.compare(item.typeCode)) {
                    arrayRemoveItem(menuTypeList, item.typeCode);
                }
                else {
                    menuTypeList = menuTypeList.concat(item.typeCode)
                }
                this.setState({
                    menuTypeList:menuTypeList,
                })
                WToast.show({data:'点击了' + item.typeName});
                console.log("======menuTypeList:", menuTypeList)
            }}>
                <View key={item.typeCode} style={this.compare(item.typeCode) ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={this.compare(item.typeCode) ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.typeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    openOrganizationSelect(){
        if (!this.state.orgDataSource || this.state.orgDataSource.length < 1) {
            WToast.show({data:"请先添加组织"});
            return
        }
        this.refs.SelectOrganization.showOrg(this.state.selectOrganization, this.state.orgDataSource)
    }
    callBackOrgValue(value){
        console.log("==========组织选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectOrganization:value
        })
        var orgName = value.toString();
        let loadUrl= "/biz/org/getOrgByName";
        let loadRequest={
            "orgName":orgName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadProfessionalDetailData);
    }
    callBackLoadProfessionalDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                orgName:response.data.orgName,
                orgId:response.data.orgId,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + "租户"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>租户名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入租户名称'}
                            onChangeText={(text) => this.setState({tenantName:text})}
                        >
                            {this.state.tenantName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>租户简称</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入租户简称'}
                            onChangeText={(text) => this.setState({tenantAbbreviation:text})}
                        >
                            {this.state.tenantAbbreviation}
                        </TextInput>
                    </View>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>所属组织</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={()=>this.openOrganizationSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 5)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.orgName ? "请选择所属组织" : this.state.orgName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入联系人'}
                            onChangeText={(text) => this.setState({tenantContact:text})}
                        >
                            {this.state.tenantContact}
                        </TextInput>
                    </View>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入联系电话'}
                            onChangeText={(text) => this.setState({
                                tenantTel:text,
                                userCode:text,
                            })}
                        >
                            {this.state.tenantTel}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>用户名</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入用户名'}
                            onChangeText={(text) => this.setState({userCode:text})}
                        >
                            {this.state.userCode}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>密码</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入密码'}
                            onChangeText={(text) => this.setState({userPwd:text})}
                        >
                            {this.state.userPwd}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>租户LOGO</Text>
                            <Text style={styles.leftLabRedTextStyle}></Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入租户LOGO'}
                            onChangeText={(text) => this.setState({
                                tenantLogo:text,
                            })}
                        >
                            {this.state.tenantLogo}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>租户登录背景颜色RGB值</Text>
                            <Text style={styles.leftLabRedTextStyle}></Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入租户登录背景颜色RGB值'}
                            onChangeText={(text) => this.setState({
                                tenantLoginBackground:text,
                            })}
                        >
                            {this.state.tenantLoginBackground}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>头部背景色RGB值</Text>
                            <Text style={styles.leftLabRedTextStyle}></Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入头部背景色RGB值'}
                            onChangeText={(text) => this.setState({
                                mobileStyleHeadBackgroundColor:text,
                            })}
                        >
                            {this.state.mobileStyleHeadBackgroundColor}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>底部导航背景色RGB值</Text>
                            <Text style={styles.leftLabRedTextStyle}></Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入底部导航背景色RGB值'}
                            onChangeText={(text) => this.setState({
                                mobileStyleBottomNavigateBackgroundColor:text,
                            })}
                        >
                            {this.state.mobileStyleBottomNavigateBackgroundColor}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>内容背景色RGB值</Text>
                            <Text style={styles.leftLabRedTextStyle}></Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入内容背景色RGB值'}
                            onChangeText={(text) => this.setState({
                                mobileStyleBodyBackgroundColor:text,
                            })}
                        >
                            {this.state.mobileStyleBodyBackgroundColor}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>按钮时效(h)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入按钮时效（单位小时）'}
                            onChangeText={(text) => this.setState({
                                editDeleteTimeLimit:text,
                            })}
                        >
                            {this.state.editDeleteTimeLimit}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>调节系数(%)</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入调节系数'}
                            onChangeText={(text) => this.setState({adjustFactor:text})}
                        >
                            {this.state.adjustFactor}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>密码有效时长（天）</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'不填代表不限制'}
                            onChangeText={(text) => this.setState({passwordEffectDuration:text})}
                        >
                            {this.state.passwordEffectDuration}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>是否支持继续出库（Y/N）</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'Y - 代码支持'}
                            onChangeText={(text) => this.setState({continueStorageOutFlag:text})}
                        >
                            {this.state.continueStorageOutFlag}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>租户排序</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入租户排序'}
                            onChangeText={(text) => this.setState({tenantSort:text})}
                        >
                            {this.state.tenantSort}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.rowLabView}>
                        <Text style={CommonStyle.rowLabTextStyle}>业务主管</Text>
                    </View>
                    <View style={[{flexWrap:'wrap'}, this.state.managerTenantId? CommonStyle.disableViewStyle : null]}>
                        <TouchableOpacity onPress={()=>{
                            // if(this.state.managerTenantId) {
                            //     return;
                            // }
                            this.setState({ 
                                modal:true,
                            })
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}]}>
                                <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                                    {this.state.managerTenantId && this.state.managerTenantName ? (this.state.managerTenantName) : "无业务主管"}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput 
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({searchKeyWord:text})}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={()=>{
                                        this.loadTenantList();
                                        }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{flexDirection:'row', flexWrap:'wrap', overflow:'scroll'}}>
                                    {
                                        (this.state.tenantList && this.state.tenantList.length > 0) 
                                        ? 
                                        this.state.tenantList.map((item, index)=>{
                                            if (index < 1000) {
                                                return this.renderTenantItem(item)
                                            }
                                        })
                                        : <EmptyRowViewComponent/> 
                                    }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                    <TouchableOpacity onPress={() => { 
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>平台菜单</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.menuTypeSource && this.state.menuTypeSource.length > 0) 
                            ? 
                            this.state.menuTypeSource.map((item, index)=>{
                                return this.renderMenuTypeRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>接口人信息</Text>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>接口人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入接口人'}
                            onChangeText={(text) => this.setState({salePersonName:text})}
                        >
                            {this.state.salePersonName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入联系电话'}
                            onChangeText={(text) => this.setState({salePersonTel:text})}
                        >
                            {this.state.salePersonTel}
                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveTenant.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect 
                            ref={'SelectOrganization'} 
                            callBackOrgValue={this.callBackOrgValue.bind(this)}
                        />
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})
import React, { Component } from 'react';
import { View, ScrollView, Text, Image, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class DepartmentAdd extends Component {
    constructor() {
        super()
        this.state = {
            departmentId: "",
            departmentName: "",
            departmentSort: 0,
            operate: "",
            parentDepartmentId: null,
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { departmentId, parentDepartmentId } = route.params;
            if (parentDepartmentId) {
                this.setState({
                    parentDepartmentId: parentDepartmentId,
                })
            }
            if (departmentId) {
                console.log("========Edit==departmentId:", departmentId);
                this.setState({
                    departmentId: departmentId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/department/get";
                loadRequest = { 'departmentId': departmentId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditMachineDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
        }
    }
    loadEditMachineDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                departmentId: response.data.departmentId,
                departmentName: response.data.departmentName,
                departmentSort: response.data.departmentSort,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        // return (
        //     <TouchableOpacity onPress={() => {
        //         this.props.navigation.navigate("DepartmentList")
        //     }}>
        //         <Text style={CommonStyle.headRightText}>部门管理</Text>
        //     </TouchableOpacity>
        // )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    saveDepartment = () => {
        console.log("=======saveBrickClassify");
        let toastOpts;
        if (!this.state.departmentName) {
            toastOpts = getFailToastOpts("请输入部门名称");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/department/add";
        if (this.state.departmentId) {
            console.log("=========Edit===departmentId", this.state.departmentId)
            url = "/biz/department/modify";
        }
        let requestParams = {
            "departmentId": this.state.departmentId,
            "departmentName": this.state.departmentName,
            "departmentSort": this.state.departmentSort,
            "parentDepartmentId": this.state.parentDepartmentId,
        };
        httpPost(url, requestParams, this.saveDepartmentCallBack);
    }

    // 保存回调函数
    saveDepartmentCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }
    render() {
        return (
            <View style={{backgroundColor: 'rgba(242, 245, 252, 1)'}}>
                <CommonHeadScreen title={this.state.operate + '部门'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={{backgroundColor: '#FFFFFF',borderBottomWidth:1,borderBottomColor:'#E8E9EC'}}>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>部门名称</Text>
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入部门'}
                            onChangeText={(text) => this.setState({ departmentName: text })}
                        >
                            {this.state.departmentName}
                        </TextInput>
                    </View>    
                    <View style={styles.lineViewStyle}></View>           
                    {/* <Image style={{ width:500  }} source={require('../../assets/icon/iconfont/slices.png')}></Image>             */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({ departmentSort: text })}
                        >
                            {this.state.departmentSort}
                        </TextInput>
                    </View>
                    {/* <Image style={{ width: 500,}} source={require('../../assets/icon/iconfont/slices.png')}></Image> */}
                </ScrollView>
                <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0, marginTop: screenHeight-305,backgroundColor:'rgba(255, 255, 255, 1)',height:66,}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginLeft: 20,marginTop:8, width: (screenWidth - 56)/2}]} >
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveDepartment.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, {marginRight: 20, marginTop:8, width: (screenWidth - 56)/2}]}>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        // borderRadius: 5,
        borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        // color: '#A0A0A0',
        color:'rgba(43, 51, 63, 1)',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        marginBottom: 6,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
})
import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,TextInput,
    FlatList,RefreshControl,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import LinearGradient from 'react-native-linear-gradient';
var CommonStyle = require('../../assets/css/CommonStyle');
const { ifIphoneXBodyViewHeight} = require('../../utils/ScreenUtil');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class MemberManagementExamineDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            personalHonor:"",
            collegeEvaluation:"",
            selExtList:[],
            selStaffId:null,
            staffId:"",
            staffName: "",
            staffState: "",
            staffTel: "",
            nationality: "",
            className: "",
            nativePlace: "",
            address: "",
            email: "",
            electronicPhotos:"",
            professionalName:  "",
            graduateInstitutions:"",
            userPhotoUrl:"",
            userId:"",
            mainProducts:"",
            enterpriseDemand:"",
            selectedClass: [],
            classDataSource:[],
            roleId:"",
            portraitName:"",
            portraitNameList:[],
            staffPosition:"",
            staffSort:"",
            classId: "",
            filterStaffState: null,
            staffIdList: [],
            currentIndex: 0
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 获取积分情况

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { staffId, staffState } = route.params;
            if(staffState) {
                this.setState({
                    filterStaffState : staffState
                })
            }
            if(staffId) {
                this.setState({
                    staffId : staffId
                })
                this.loadMemberInterViewList(staffId);
            }
            this.loadMemberManagementList(staffState);
        }
    }

    loadMemberInterViewList=(staffId) =>{
        let url = "/biz/cr/member/get";
        let loadRequest = {
           "staffId" : staffId,
           "staffType": "M",
           "sign":true,
        };
        httpPost(url, loadRequest, this._loadMemberInterViewListcallback);

    }

    _loadMemberInterViewListcallback=(response) =>{
        if (response.code == 200 && response.data) {
            this.setState({
                staffId: response.data.staffId,
                staffName: response.data.staffName,
                staffState: response.data.staffState,
                staffTel: response.data.staffTel,
                staffSort:response.data.staffSort,
                nationality: response.data.nationality,
                className: response.data.className,
                nativePlace: response.data.nativePlace,
                address: response.data.address,
                userPhotoUrl:constants.image_addr + '/' +  response.data.electronicPhotos,
                electronicPhotos:response.data.electronicPhotos,
                professionalName:  response.data.professionalName,
                personalHonor: response.data.personalHonor,
                collegeEvaluation: response.data.collegeEvaluation,
                selExtList:response.data.crStaffExtDTOList,
                graduateInstitutions:response.data.graduateInstitutions,
                mainProducts:response.data.mainProducts,
                enterpriseDemand:response.data.enterpriseDemand,
                userId:response.data.userId,
                classId:response.data.classId,
                className: response.data.className,
                selectedClass: [response.data.className],
                roleId:response.data.roleId,
                portraitName:response.data.portraitName,
                portraitNameList:response.data.portraitNameList,
                staffPosition:response.data.staffPosition,
            })

            console.log("现在的照片",constants.image_addr + '/' +  response.data.electronicPhotos)
        }
    }

    loadMemberManagementList=(staffState)=>{
        let url= "/biz/cr/member/management/listByTime";
        let data={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "staffType":"M",
            "sign":true,
            "staffState": staffState ? staffState : this.state.filterStaffState
        };
        httpPost(url, data, this.callBackLoadMemberManagementList);
    }

    callBackLoadMemberManagementList = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld, ...dataNew];
            // 获取会员标识列表
            var staffList = []
            dataNew.forEach(element => {
                staffList.push(element.staffId);
            });
            var currentIndex = staffList.indexOf(parseInt(this.state.staffId));
            console.log("currentIndex==========", currentIndex)
            console.log("staffList==========", staffList)
            console.log("staffList.length==========", staffList.length)
            this.setState({
                currentIndex: currentIndex,
                staffIdList: staffList,
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 展示上一个会员的信息
    previousMember = () => {
        var currentIndex = this.state.staffIdList.indexOf(parseInt(this.state.staffId));
        if (currentIndex === 0) {
            console.log("已经到第一个了")
            WToast.show({ data: '已经到第一个了' });
            return;
        }
        this.loadMemberInterViewList(this.state.staffIdList[currentIndex - 1]);
        this.setState({
            currentIndex: currentIndex - 1,
            staffId : this.state.staffIdList[currentIndex - 1]
        })
    }

    // 展示下一个会员的信息
    nextMember = () => {
        var currentIndex = this.state.staffIdList.indexOf(parseInt(this.state.staffId));
        if (currentIndex === (this.state.staffIdList.length - 1)) {
            console.log("已经到最后一个了")
            WToast.show({ data: '已经到最后一个了' });
            return;
        }
        this.loadMemberInterViewList(this.state.staffIdList[currentIndex + 1]);
        this.setState({
            currentIndex: currentIndex + 1,
            staffId : this.state.staffIdList[currentIndex + 1]
        })
    }

    // 所属行业
    renderPortraitNameList = (value) => {
        return (
            <View key={value} style={[{backgroundColor: '#ECEEF2', height: 25, marginRight: 10, paddingLeft: 7, paddingTop: 2, paddingRight: 10,
                paddingBottom: 3, borderRadius: 2, justifyContent: 'center',alignContent: 'center'}
            ]}>
                <Text style={[{fontSize: 14, fontWeight: "400", color: 'rgba(0, 10, 32, 0.65)', lineHeight: 20, textAlign : 'center'}]}>
                    {value}
                </Text>
            </View>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View>
                {/* <TouchableOpacity onPress={() => {
                    this.props.navigation.navigate("MemberManagementExamine",
                        {
                            // 传递回调函数
                            refresh: this.callBackFunction
                        })
                }}>
                    <Text style={CommonStyle.headRightText}>会员管理</Text> 
                
                </TouchableOpacity> */}

            </View>
        )
    }

    staffDisplaySetting = (item, index) => {
        console.log("=======staffDisplaySetting");
        let toastOpts;
        let requestUrl = "/biz/cr/member/update_member_display";
        let requestParams = {
            'staffId': this.state.staffId,
            'resumeDisplay': "Y",
            "staffType":"M",
            "staffState":"0AA",
            "roleId":this.state.roleId,
            "staffTel":this.state.staffTel,
            "staffName":this.state.staffName,
            "electronicPhotos":this.state.electronicPhotos,
            "staffSort":this.state.staffSort ? this.state.staffSort : 0,
            "tenantName":constants.loginUser.tenantName,

        };
        httpPost(requestUrl, requestParams, this.saveMemberCallBack);
    }

    // 保存回调函数
    saveMemberCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                
                if (this.props.route.params.refresh1) {
                    this.props.route.params.refresh1();
                }
                if (this.props.route.params.refresh2) {
                    this.props.route.params.refresh2();
                }
                if (response.data.roleId === this.state.roleId) {
                    toastOpts = getSuccessToastOpts('入库成功');
                }
                else {
                    toastOpts = getSuccessToastOpts('该联系电话已注册，所属角色无法更改！');
                }
                
                WToast.show(toastOpts);
                // this.props.navigation.goBack()
                this.loadMemberInterViewList(this.state.staffIdList[this.state.currentIndex]);
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    ignore = (item, index) => {
        console.log("=======staffDisplaySetting=staffId", this.state.staffId);
        let requestUrl = "/biz/cr/staff/refuse";
        let requestParams = {
            "staffId": this.state.staffId,
            "staffState":"0BB",
            "staffTel":this.state.staffTel,
            "tenantName":constants.loginUser.tenantName,
            "tenantId":constants.loginUser.tenantId,
            "staffName":this.state.staffName,
        };
        httpPost(requestUrl, requestParams, this.ignoreCallBack);
    }

    // 保存回调函数
    ignoreCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh1) {
                    this.props.route.params.refresh1();
                }
                if (this.props.route.params.refresh2) {
                    this.props.route.params.refresh2();
                }
                toastOpts = getSuccessToastOpts('拒绝成功');
                WToast.show(toastOpts);
                // this.props.navigation.goBack()
                this.loadMemberInterViewList(this.state.staffIdList[this.state.currentIndex]);
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    render(){
        return(
            <View style={{backgroundColor:'#FFFFFF',height:screenHeight}}>
                <CommonHeadScreen title='入库审核'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{height:ifIphoneXBodyViewHeight(),backgroundColor:'#FFFFFF' }}>
                <ScrollView style={[{height:ifIphoneXBodyViewHeight() - 64 }]}>
                    <View style={[styles.topBox]}> 
                        {/* <View style={{position:'absolute',right:3, top:3}}>
                        </View> */}
                        <View style={{width: screenWidth - 64, flexDirection: 'row'}}>
                            {
                                this.state.electronicPhotos ?
                                    <Image source={{ uri: constants.image_addr + '/' +  this.state.electronicPhotos }} style={{ height: 100, width: 80}} />
                                    :
                                    <Image style={{ height: 100, width: 80}} source={require('../../assets/icon/iconfont/head.png')}></Image>
                            }
                            <View style={{marginLeft:16, flexDirection: 'column', marginTop: 6}}>
                                <View style={{flexDirection: 'row', alignItems: 'flex-start', width: screenWidth - 150, flexWrap: 'wrap'}}>
                                    <View style={{ flexDirection: 'row', marginRight: 16, flexWrap: 'wrap' }}>
                                        <Text style={{ fontSize: 20, fontWeight: 'bold', color: 'rgba(0, 0, 0, 0.86)', lineHeight: 24 }}>{this.state.staffName}</Text>
                                        {
                                            this.state.staffState ==="0AA" ? 
                                            <View style={{ width: 52, height: 20, marginLeft: 7,borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: "#3ab240" }}>
                                                <Text style={{fontSize: 13, color: '#FFFFFF' }}>已通过</Text>
                                            </View>
                                            :
                                            <View>
                                                {
                                                    this.state.staffState === "0AB" ?
                                                    <View style={{ width: 52, height: 20, marginLeft: 7,borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: '#FF8C28' }}>
                                                        <Text style={{fontSize: 13, color: '#FFFFFF' }}>待审核</Text>
                                                    </View>
                                                    :
                                                    <View style={{ width: 52, height: 20, marginLeft: 7,borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: 'rgba(255,0,0,0.4)' }}>
                                                        <Text style={{fontSize: 13, color: '#FFFFFF' }}>未通过</Text>
                                                    </View>
                                                }
                                            </View>
                                        }
                                    </View>
                                    {/* <View style={{flexDirection: 'row', marginTop: 2, flexWrap: 'wrap'}}>
                                        <Text style={{ fontSize: 16, color: 'rgba(0,10,32,0.65)', lineHeight: 20 }}>{this.state.staffPosition}</Text>
                                    </View> */}
                                </View>
                                <View style={{flexDirection: 'row', width: screenWidth - 150, marginTop: 2, alignItems: 'center', flexWrap: "wrap",marginTop: 6}}>
                                    <Text style={{ fontSize: 16, color: 'rgba(0, 10, 32, 0.85)', lineHeight: 20 }}>{this.state.graduateInstitutions ? this.state.graduateInstitutions : "暂无信息"}</Text>
                                </View>
                                {
                                    this.state.staffPosition != null ?
                                    <View style={{flexDirection: 'row', marginTop: 5, flexWrap: 'wrap'}}>
                                        <Text style={{ fontSize: 16, color: 'rgba(0,10,32,0.65)', lineHeight: 20 }}>{this.state.staffPosition}</Text>
                                    </View>
                                    :
                                    <View></View>
                                }
                                
                                <View style={{flexDirection: 'row', width: screenWidth - 160, alignItems: 'center',marginTop: 5}}>
                                    <View style={{flexDirection: 'row', marginTop: 2, width: (screenWidth - 160)/2, flexWrap: "wrap"}}>
                                        <Text style={{ fontSize: 16, color: 'rgba(0, 10, 32, 0.85)', lineHeight: 24 }}>{this.state.address ? this.state.address : "暂无信息"}</Text>
                                    </View>

                                    <View style={{marginLeft:6,flexDirection: 'row', marginTop: 2,  width: (screenWidth - 160)/2}}>
                                        <Text style={{ fontSize: 16, color: 'rgba(0, 10, 32, 0.85)', lineHeight: 24 }}>{this.state.staffTel}</Text>
                                    </View>
                                </View>
                                    
                                <View style={{flexDirection: 'row', width: screenWidth - 145, flexWrap: "wrap",marginTop: 5}}>
                                    {this.state.portraitName ?
                                        this.state.portraitName.split('  ').slice(0, -1).map((word, index) => (
                                            <View key={index} style={[{backgroundColor: '#ECEEF2', marginRight: 8, marginTop: 5, paddingLeft: 6, paddingTop: 3, paddingRight: 6,
                                                paddingBottom: 3, borderRadius: 2, justifyContent: 'center',alignContent: 'center'}
                                            ]}>
                                                <Text style={{ fontSize: 14, color: 'rgba(0, 10, 32, 0.85)', lineHeight: 20}}>{word}</Text>
                                            </View>
                                        ))
                                        :null
                                    }
                                </View>
                            </View>
                        </View>
                    </View>

                    <View style={[CommonStyle.addItemSplitRowViewDetail, {marginLeft: 18}]}>
                        <Image source={require('../../assets/image/business.png')} style={styles.titleImageStyle} />
                        <Text style={[CommonStyle.addItemSplitRowTextDetail]}>业务简介</Text>
                    </View>

                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>{this.state.collegeEvaluation ? this.state.collegeEvaluation :  "暂无信息"}</Text>
                    </View>

                    <View style={styles.lineViewStyle}/>

                    <View style={[CommonStyle.addItemSplitRowViewDetail, {marginLeft: 18}]}>
                        <Image source={require('../../assets/image/resource.png')} style={styles.titleImageStyle} />
                        <Text style={[CommonStyle.addItemSplitRowTextDetail]}>期望对接的资源</Text>
                    </View>

                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>{this.state.enterpriseDemand ? this.state.enterpriseDemand :  "暂无信息"}</Text>
                    </View>

                    <View style={styles.lineViewStyle}/>

                    <View style={[CommonStyle.addItemSplitRowViewDetail, {marginLeft: 18}]}>
                        <Image source={require('../../assets/image/resourcesProviding.png')} style={styles.titleImageStyle} />
                        <Text style={[CommonStyle.addItemSplitRowTextDetail]}>我能提供的资源</Text>
                    </View>

                    <View style={[styles.titleViewStyle, {marginBottom: 30}]}>
                        <Text style={styles.titleTextStyle}>{this.state.personalHonor ? this.state.personalHonor :  "暂无信息"}</Text>
                    </View>
                    {
                        (this.state.selExtList && this.state.selExtList.length > 0)
                        ?
                        <View>
                            {
                                this.state.selExtList.map((item, index)=>{
                                    return(
                                    <View key={item.extId} style={[styles.innerViewStyle]}>
                                        <View style={CommonStyle.addItemSplitRowView}>
                                            <Text style={[CommonStyle.addItemSplitRowText,{fontWeight:'bold'}]}>{item.extTitle ? item.extTitle : "无"}</Text>
                                        </View>
                                        <View style={styles.titleViewStyle}>
                                            <Text style={styles.titleTextStyle}>{item.extContent ? item.extContent : "无" }</Text>
                                        </View>
                                    </View>
                                    )                           
                                })
                            }
                        </View> : <View/>
                    }

                </ScrollView>

                {
                    this.state.staffState === "0AB" ?
                        <View style={[styles.buttonContainer]}>
                            <TouchableOpacity onPress={() => {
                                this.previousMember()
                            }}>
                                {
                                    this.state.currentIndex === 0 ?
                                        <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                                            <View style={[styles.imgBox]}>
                                                <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/previousDisabled.png')} />
                                            </View>
                                            <Text style={[{fontSize: 16, color: '#999999'}]}>上一个</Text>
                                        </View>
                                        :
                                        <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                                            <View style={[styles.imgBox]}>
                                                <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/previous.png')} />
                                            </View>
                                            <Text style={[{fontSize: 16, color: '#1D80FF'}]}>上一个</Text>
                                        </View>
                                }
                            </TouchableOpacity>
                            <TouchableOpacity onPress={()=>{
                                Alert.alert('确认','您确定要拒绝该申请吗？',[
                                    {
                                        text:"取消", onPress:()=>{
                                            WToast.show({data:'点击了取消'});
                                        }
                                    },
                                    {
                                        text:"确定", onPress:()=>{
                                            WToast.show({data:'点击了确定'});
                                            this.ignore();
                                        }
                                    }
                                ]);
                            }}>
                                <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                                    <View style={[styles.imgBox]}>
                                        <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/memberRefuse.png')} />
                                    </View>
                                    <Text style={[{fontSize: 16, color: '#CB4139'}]}>拒绝</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={this.staffDisplaySetting.bind(this)}>
                                <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                                    <View style={[styles.imgBox]}>
                                        <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/memberPass.png')} />
                                    </View>
                                    <Text style={[{fontSize: 16, color: '#1BBC82'}]}>通过</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => {
                                this.nextMember()
                            }}>
                                {
                                    this.state.currentIndex === (this.state.staffIdList.length -1) ?
                                        <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                                            <View style={[styles.imgBox]}>
                                                <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/nextDisabled.png')} />
                                            </View>
                                            <Text style={[{fontSize: 16, color: '#999999'}]}>下一个</Text>
                                        </View>
                                        :
                                        <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                                            <View style={[styles.imgBox]}>
                                                <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/next.png')} />
                                            </View>
                                            <Text style={[{fontSize: 16, color: '#1D80FF'}]}>下一个</Text>
                                        </View>
                                }
                            </TouchableOpacity>
                            
                        </View>
                        :
                        <View style={[styles.buttonContainer]}>
                            <TouchableOpacity onPress={() => {
                                this.previousMember()
                            }}>
                                {
                                    this.state.currentIndex === 0 ?
                                        <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 2 }}>
                                            <View style={[styles.imgBox]}>
                                                <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/previousDisabled.png')} />
                                            </View>
                                            <Text style={[{fontSize: 16, color: '#999999'}]}>上一个</Text>
                                        </View>
                                        :
                                        <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 2 }}>
                                            <View style={[styles.imgBox]}>
                                                <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/previous.png')} />
                                            </View>
                                            <Text style={[{fontSize: 16, color: '#1D80FF'}]}>上一个</Text>
                                        </View>
                                }
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => {
                                this.nextMember()
                            }}>
                                {
                                    this.state.currentIndex === (this.state.staffIdList.length -1) ?
                                        <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 2 }}>
                                            <View style={[styles.imgBox]}>
                                                <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/nextDisabled.png')} />
                                            </View>
                                            <Text style={[{fontSize: 16, color: '#999999'}]}>下一个</Text>
                                        </View>
                                        :
                                        <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 2 }}>
                                            <View style={[styles.imgBox]}>
                                                <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/next.png')} />
                                            </View>
                                            <Text style={[{fontSize: 16, color: '#1D80FF'}]}>下一个</Text>
                                        </View>
                                }
                            </TouchableOpacity>
                        </View>
                }
                 </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    titleImageStyle:{
        marginLeft:5, 
        width: 20, 
        height: 21,
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },

    innerViewStyleList:{
        // marginTop: 10,
        borderColor: "rgba(255, 255, 255, 1)",
        // borderColor: "#FFFFFF",
        // borderWidth: 2
    },
    topBox: {
        flexDirection: 'row',
        margin: 16,
        padding: 16,
        borderColor: "#F4F4F4",
        elevation: 3,
    },
    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', width:100, alignItems:'center', alignContent:'flex-end', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize: 14, 
        color: '#404956', 
        lineHeight: 24,
    },
    titleViewStyle:{
        flexDirection: 'row',
        marginLeft: 18,
        marginRight: 14,
        marginTop: 5
    },
    innerViewStyle:{
        marginTop:0,
    },
    photos:{
        width:150,
        height:200,
        // borderRadius:50,
        borderWidth:0,
        // marginTop:80,
        // marginBottom:30
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },
    buttonContainer: {
        // position: 'absolute',
        height: 64,
        width: screenWidth,
        backgroundColor: 'rgba(255, 255, 255, 1)',
        // bottom: 0,
        paddingBottom: 8,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        //borderTopLeftRadius: 32,
       // borderTopRightRadius: 32,
    },
    imgBox: {
        width: 32,
        height: 32,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
    }
});
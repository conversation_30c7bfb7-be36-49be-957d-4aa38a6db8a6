import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity
    , Dimensions, KeyboardAvoidingView, Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import DateTimePicker from '@react-native-community/datetimepicker';
import moment from 'moment';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
var currentTime=(new Date()).getTime() + 8*3600*1000
export default class CourseSchedulingAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            schedulingId:"",
            // 课程
            selCourseId:"",
            selectCourseName:[],
            selCourseName:"",
            courseDataSource:[],
            courseSketch:"",

            // 部门
            selDepartmentId:"",
            selectDepartmentName:[],
            selDepartmentName:"",
            departmentDataSource:[],

            teachingStart:"",
            teachingEnd:"",
            teachingClassroom:"",
            teachingTeacher:"",
            teachingDate:"",
            selectedTeachingDate:[],
            teachingTime:"",
            open1:false,
            open2:false,
            currentTime:currentTime,
            startTime:"",
            endTime:"",

            selectedTeachingStartTime:[],
            selectedTeachingEndTime:[],
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 加载课程列表
        this.loadCourseList();

        // 加载部门列表
        this.loadDepartmentList();

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { schedulingId } = route.params;
            if (schedulingId) {
                console.log("=============schedulingId" + schedulingId + "");
                this.setState({
                    schedulingId:schedulingId,
                    operate:"编辑"
                })
                let loadTypeUrl= "/biz/course/scheduling/get";
                let loadRequest={'schedulingId':schedulingId};
                httpPost(loadTypeUrl, loadRequest, this.loadSchedulingDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增",
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectedTeachingDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    teachingDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
    }

    loadCourseList=()=>{
        let url= "/biz/course/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            "scene":"T",
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    courseDataSource: response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    loadDepartmentList=()=>{
        let url= "/biz/department/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    departmentDataSource: response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    loadSchedulingDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            var selectedTeachingDate = response.data.teachingDate.split("-");
            this.setState({
                schedulingId:response.data.schedulingId,
                selCourseId:response.data.courseId,
                selCourseName:response.data.courseName,
                selectCourseName:[response.data.courseName],
                courseSketch:response.data.courseSketch,
                selDepartmentId:response.data.departmentId,
                selDepartmentName:response.data.departmentName,
                selectDepartmentName:[response.data.departmentName],
                teachingTeacher:response.data.teachingTeacher,
                teachingClassroom:response.data.teachingClassroom,
                teachingDate:response.data.teachingDate,
                teachingTime:response.data.teachingTime,
                selectedTeachingDate:selectedTeachingDate
            })
            var teachingTimeData = response.data.teachingTime.split(" - ");
            this.setState({
                teachingStart: teachingTimeData[0],
                teachingEnd: teachingTimeData[1],
            })
            // console.log("==teachingTimeData=======",teachingTimeData[0]);
            var selectedTeachingStartTime = teachingTimeData[0].split(":");
            // console.log("==teachingStartTime=======",teachingStartTime);
            // console.log("==teachingStartTime[0]=======",teachingStartTime[0]);
            // console.log("==teachingStartTime[1]=======",teachingStartTime[1]);
            this.setState({
                selectedTeachingStartTime: selectedTeachingStartTime
            })
            var selectedTeachingEndTime = teachingTimeData[1].split(":");
            this.setState({
                selectedTeachingEndTime: selectedTeachingEndTime
            })
        }
    }

    // setOpen1 = (open) => {
    //     console.log("teachingStart===",this.state.teachingStart)
    //     this.setState({
    //         open1: open,
    //         open2: false,
    //     })
    // }

    // setOpen2 = (open) => {
    //     console.log("teachingEnd===",this.state.teachingEnd)
    //     this.setState({
    //         open1: false,
    //         open2: open,
    //     })
    // }

    // dateFormatter=(value)=> {
    //     // https://www.cnblogs.com/catgatp/p/13178934.html
    //     // YYYY-MM-DD HH:mm
        
    //     var date = moment.parseZone(value).local().format('HH:mm');
    //     return date;
    // }

    // timeOnChange1 = (event, selecteTime) => {
    //     if(event.type === "set"){
    //         if(selecteTime){
    //             const selTime = this.dateFormatter(selecteTime);
    //             console.log("selecteTime========",selecteTime)
    //             console.log("======onChange", selTime);
    //             var saveTeachingStart = selTime.toString();
    //             var saveTeachingTime = saveTeachingStart + '-' + this.state.teachingEnd;
    //             console.log(saveTeachingTime);
    //             this.setState({
    //                 teachingStart: saveTeachingStart,
    //                 // startTime:saveTeachingStart,
    //                 open1:false,
    //                 open2:false,
    //                 // currentTime:selecteTime.getTime(),
    //                 // teachingSTime: saveTeachingTime,
    //             })
    //             var startTimeData = saveTeachingStart.split(":");
    //             var startTime = new Date();
    //             startTime.setHours(startTimeData[0]);
    //             startTime.setMinutes(startTimeData[1]);
    //             this.setState({
    //                 startTime:startTime
    //             })
    //             console.log("======teachingStart", this.state.teachingStart);
    //         }
    //         else{
    //             return;
    //         }
    //     }
    //     else{
    //         this.setState({
    //             open1:false
    //         })
    //     }
        
    // }

    // timeOnChange2 = (event, selecteTime) => {
    //     if(event.type === "set"){
    //         if(selecteTime){
    //             const selTime = this.dateFormatter(selecteTime);
    //             console.log("selecteTime========",selecteTime)
    //             console.log("======onChange", selTime);
    //             var saveTeachingEnd=selTime.toString();
    //             var saveTeachingTime = this.state.teachingStart + '-' + saveTeachingEnd;
    //             console.log(saveTeachingTime);
    //             this.setState({
    //                 teachingEnd: saveTeachingEnd,
    //                 open1:false,
    //                 open2:false,
    //                 // currentTime:selecteTime.getTime(),
    //                 // teachingSTime: saveTeachingTime,
    //             })
    //             var endTimeData = saveTeachingEnd.split(":");
    //             var endTime = new Date();
    //             endTime.setHours(endTimeData[0]);
    //             endTime.setMinutes(endTimeData[1]);
    //             this.setState({
    //                 endTime:endTime
    //             })
    //             console.log("======teachingEnd", this.state.teachingEnd);
    //         }
    //         else{
    //             return;
    //         }
    //     }
    //     else{
    //         this.setState({
    //             open2:false
    //         })
    //     }
    // }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("CourseScheduling")
            }}>
                <Text style={CommonStyle.headRightText}>排课设置</Text>
            </TouchableOpacity>
        )
    }

    saveScheduling =()=> {
        console.log("=======saveScheduling");
        let toastOpts;
        var saveTime=this.state.teachingStart+" - "+this.state.teachingEnd;
        this.setState({
            teachingTime: saveTime
        })
        console.log("=======teachingTime",saveTime);
        if (!this.state.selCourseId) {
            toastOpts = getFailToastOpts("请选择课程名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selDepartmentId) {
            toastOpts = getFailToastOpts("请选择所属部门");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.teachingTeacher) {
        //     toastOpts = getFailToastOpts("请输入授课老师");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.teachingDate) {
            toastOpts = getFailToastOpts("请选择授课日期");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.teachingStart&&!this.state.teachingEnd) {
            toastOpts = getFailToastOpts("请选择授课时间");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.teachingClassroom) {
        //     toastOpts = getFailToastOpts("请输入上课教室");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url= "/biz/course/scheduling/add";
        if (this.state.schedulingId) {
            console.log("=========Edit===schedulingId", this.state.schedulingId)
            url= "/biz/course/scheduling/modify";
        }
        let requestParams={
            schedulingId:this.state.schedulingId,
            courseId: this.state.selCourseId,
            courseSketch:this.state.courseSketch,
            departmentId:this.state.selDepartmentId,
            teachingTeacher: this.state.teachingTeacher,
            teachingDate: this.state.teachingDate,
            teachingTime: saveTime,
            teachingClassroom: this.state.teachingClassroom,
        };
        console.log("requestParams=========",requestParams)
        httpPost(url, requestParams, this.saveSchedulingCallBack);
    }
    
    // 保存回调函数
    saveSchedulingCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 渲染客户底部滚动数据
    openDepartmentName() {
        if (!this.state.departmentDataSource || this.state.departmentDataSource.length < 1) {
            WToast.show({ data: "请先添加部门" });
            return
        }
        this.refs.SelectDepartmentName.showDepartmentName(this.state.selectDepartmentName, this.state.departmentDataSource)
    }

    callBackSelectDepartmentNameValue(value) {
        console.log("==========部门选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectDepartmentName: value,
            selDepartmentName:value.toString()
        })
        var departmentName = value.toString();
        let loadUrl = "/biz/department/getDepartmentByName";
        let loadRequest = {
            "departmentName": departmentName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadDepartmentDetailData);
    }

    callBackLoadDepartmentDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                selDepartmentName: response.data.departmentName,
                selDepartmentId:response.data.departmentId,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }  

    // 渲染客户底部滚动数据
    openCourseName() {
        if (!this.state.courseDataSource || this.state.courseDataSource.length < 1) {
            WToast.show({ data: "请先添加课程" });
            return
        }
        this.refs.SelectCourseName.showCourseName(this.state.selectCourseName, this.state.courseDataSource)
    }

    callBackSelectCourseNameValue(value) {
        console.log("==========课程选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectCourseName: value,
            selCourseName:value.toString()
        })
        var courseName = value.toString();
        let loadUrl = "/biz/course/getCourseByName";
        let loadRequest = {
            "courseName": courseName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadCourseDetailData);
    }

    callBackLoadCourseDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                selCourseName: response.data.courseName,
                selCourseId:response.data.courseId,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }    

    openTeachingDate(){
        this.refs.SelectTeachingDate.showDate(this.state.selectedTeachingDate)
    }
    
    callBackSelectTeachingDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedTeachingDate:value
        })
        if (value && value.length) {
            var teachingDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    teachingDate += vartime;
                }
                else{
                    teachingDate += "-" + vartime;
                }
            }
            this.setState({
                teachingDate:teachingDate
            })
        }
    }

    openTeachingStartTimeDate(){
        this.refs.SelectTeachingStartTimeDate.showTime(this.state.selectedTeachingStartTime)
    }
    
    callBackTeachingStarTimeValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedTeachingStartTime:value
        })
        if (value && value.length) {
            var teachingStart = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    teachingStart += vartime;
                }
                else{
                    teachingStart += ":" + vartime;
                }
            }
            this.setState({
                teachingStart:teachingStart
            })
        }
    }

    openTeachingEndTimeDate(){
        this.refs.SelectTeachingEndTimeDate.showTime(this.state.selectedTeachingEndTime)
    }
    
    callBackTeachingEndTimeValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedTeachingEndTime:value
        })
        if (value && value.length) {
            var teachingEnd = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    teachingEnd += vartime;
                }
                else{
                    teachingEnd += ":" + vartime;
                }
            }
            this.setState({
                teachingEnd:teachingEnd
            })
        }
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '排课'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>课程名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openCourseName()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.selCourseName ? "请选择课程名称" : this.state.selCourseName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>上课班级</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openDepartmentName()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.selDepartmentName ? "请选择上课班级" : this.state.selDepartmentName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>课程简述</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                            placeholder={'请输入课程简述'}
                            onChangeText={(text) => this.setState({courseSketch:text})}
                        >
                            {this.state.courseSketch}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>授课老师</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入授课老师'}
                            onChangeText={(text) => this.setState({ teachingTeacher: text })}
                        >
                            {this.state.teachingTeacher}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>授课日期</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openTeachingDate()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.teachingDate ? "请选择授课日期" : this.state.teachingDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>课程开始时间</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.setOpen1(true)}>
                            <View style={[CommonStyle.inputTextStyleTextStyle]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {this.state.teachingStart?this.state.teachingStart:"开始时间"}
                                </Text>
                            </View>
                        </TouchableOpacity>
                        {
                            this.state.open1?
                            <DateTimePicker
                                value={new Date(this.state.currentTime)}
                                mode="time"
                                is24Hour={true}
                                display='default'
                                onChange={this.timeOnChange1}
                            />
                            :
                            <View/>
                        }
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>授课时间</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openTeachingStartTimeDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyleNoWidth,{width:85,alignItems:'center'}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {this.state.teachingStart?this.state.teachingStart:"开始时间"}
                                </Text>
                            </View>
                        </TouchableOpacity>
                        <View style={[{height:45,justifyContent:'center',marginLeft:10,marginRight:10}]}>
                            <Text style={styles.leftLabNameTextStyle}>-</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openTeachingEndTimeDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyleNoWidth,{width:85,alignItems:'center'}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {this.state.teachingEnd?this.state.teachingEnd:"结束时间"}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>上课教室</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入上课教室'}
                            onChangeText={(text) => this.setState({ teachingClassroom: text })}
                        >
                            {this.state.teachingClassroom}
                        </TextInput>
                    </View>
                        
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveScheduling.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                            <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                <BottomScrollSelect 
                    ref={'SelectTeachingDate'} 
                    callBackDateValue={this.callBackSelectTeachingDateValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectCourseName'} 
                    callBackCourseValue={this.callBackSelectCourseNameValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectDepartmentName'} 
                    callBackDepartmentNameValue={this.callBackSelectDepartmentNameValue.bind(this)}
                />
                <BottomScrollSelect 
                        ref={'SelectTeachingStartTimeDate'} 
                        callBackTimeValue={this.callBackTeachingStarTimeValue.bind(this)}
                    />
                <BottomScrollSelect 
                        ref={'SelectTeachingEndTimeDate'} 
                        callBackTimeValue={this.callBackTeachingEndTimeValue.bind(this)}
                    />
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    // inputRowText:{
    //     width:screenWidth - 10,
    //     borderRadius:5,
    //     borderColor:'#F1F1F1',
    //     borderWidth:1,
    //     marginRight:5,
    //     marginLeft:5,
    //     color:'#A0A0A0',
    //     fontSize:15,
    //     paddingLeft:10,
    //     paddingRight:10
    // }
});
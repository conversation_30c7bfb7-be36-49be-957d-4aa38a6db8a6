import React, { Component } from 'react';
import {Text, AppRegistry, StyleSheet, Image, View, Platform} from 'react-native';
// import TabNavigator from 'react-native-tab-navigator';
// import { Navigator } from 'react-native-deprecated-custom-components'
import RouterTest from './RouterTest/RouterTest';
import Home from './home/<USER>';
import SemiFinished from './semi_finished/SemiFinishedList';
import EncastageList from './encastage/EncastageList';
import Temperature from './warm/List';
import CheckOut from './check_out/CheckOutList'
import CheckOutAdd from './check_out/CheckOutAdd';

class MainPage extends Component {

    constructor(props) {
        super(props);
        this.state = {
            selectedTab: 'Home'
        };
    }

    // componentDidMount(){
    //     this.props.navigation = 
    // }

    // static navigationOptions = {
    //     title:'首页',
    //     headerStyle:{
    //       backgroundColor:'green',
          
    //     },
    //     headerTintColor:'#FFFFFF',
    //     headerTitleStyle:{
    //       fontWeight:'normal',
    //       fontSize:20
    //     }
    // }

    switchTab = (selectedTabStr) => {
        console.log("=======selectedTabStr:", selectedTabStr);
        this.setState(
            {
                selectedTab: selectedTabStr
            }
        );
    };
    renderTabBarItem=(title, iconName, selectedIconName, selectedTab, componentName, component)=>{
        return (
        <TabNavigator.Item
            title={title}
            renderIcon={() => <Image source={iconName} style={styles.iconStyle} />}
            renderSelectedIcon={() => <Image source={selectedIconName} style={styles.iconStyle} />}
            onPress={this.switchTab.bind(this, selectedTab)}
            selected={this.state.selectedTab === selectedTab}
        >
            <Navigator
                initialRoute={{name:componentName,component:component}}
                configureScene={(route, routeStack) =>{
                    return Navigator.SceneConfigs.PushFromRight;
                }}
                renderScene={(route, navigator) => {
                    let Component = route.component;
                    return <Component {...route.params} navigator={navigator} />
                  }}
            >
            </Navigator>
        </TabNavigator.Item>
        )
    }
    render() {
        return (
            <TabNavigator tabBarStyle={styles.tabBar} sceneStyle={{ paddingBottom: 10 }}>
                {this.renderTabBarItem('首页', require('../assets/icon/foot_home_icon.png'), require('../assets/icon/foot_home_icon_lighted.png'), 'Home', '首页', Home)}
                {this.renderTabBarItem('半成品管理', require('../assets/icon/foot_semi_finished_icon.png'), require('../assets/icon/foot_semi_finished_icon_lighted.png'), 'SemiFinished', '半成品管理', SemiFinished)}
                {this.renderTabBarItem('装窑管理', require('../assets/icon/foot_encastage_icon.png'), require('../assets/icon/foot_encastage_icon_lighted.png'), 'Encastage', '装窑管理', EncastageList)}
                {this.renderTabBarItem('温度管理', require('../assets/icon/foot_temperature_icon.png'), require('../assets/icon/foot_temperature_icon_lighted.png'), 'Temperature', '温度管理', Temperature)}
                {this.renderTabBarItem('出库管理', require('../assets/icon/check_out.png'), require('../assets/icon/check_out_lighted.png'), 'CheckOut', '出库管理', CheckOut)}
                {/* {this.renderTabBarItem('测试', require('../assets/icon/testIcon.png'), require('../assets/icon/testIcon_lighted.png'), 'RouterTest', '测试', CheckOutAdd)} */}
                {/* <TabNavigator.Item
                    title="Home"
                    renderIcon={() => <Image source={require('../assets/icon/home.png')} style={styles.iconStyle} />}
                    renderSelectedIcon={() => <Image source={require('../assets/icon/home_lighted.png')} style={styles.iconStyle} />}
                    onPress={this.switchTab.bind(this, 'Home')}
                    selected={this.state.selectedTab === 'Home'}
                >
                    <Navigator
                        initialRoute={{name:'首页',component:Home}}
                        configureScene={(route, routeStack) =>{
                            return Navigator.SceneConfigs.PushFromRight;
                        }}
                        renderScene={(route, navigator) => {
                            let Component = route.component;
                            return <Component {...route.params} navigator={navigator} />
                          }}
                    >
                    </Navigator>
                </TabNavigator.Item> */}
                {/* <TabNavigator.Item
                    title="Search"
                    renderIcon={() => <Image source={require('../assets/icon/search.png')} style={styles.iconStyle} />}
                    renderSelectedIcon={() => <Image source={require('../assets/icon/search_lighted.png')} style={styles.iconStyle} />}
                    onPress={() => { this.setState({ selectedTab: 'Shop' }) }}
                    selected={this.state.selectedTab === 'Shop'}
                >
                    <Navigator
                        initialRoute={{name:'Search',component:Shop}}
                        configureScene={(route, routeStack) =>{
                            return Navigator.SceneConfigs.PushFromRight;
                        }}
                        renderScene={(route, navigator) => {
                            let Component = route.component;
                            return <Component {...route.params} navigator={navigator} />
                          }}
                    >
                    </Navigator>
                </TabNavigator.Item> */}
                {/* <TabNavigator.Item
                    title="News"
                    renderIcon={() => <Image source={require('../assets/icon/news.png')} style={styles.iconStyle} />}
                    renderSelectedIcon={() => <Image source={require('../assets/icon/news_lighted.png')} style={styles.iconStyle} />}
                    onPress={() => { this.setState({ selectedTab: 'News' }) }}
                    selected={this.state.selectedTab === 'News'}
                >
                    <Navigator
                        initialRoute={{name:'News',component:News}}
                        configureScene={(route, routeStack) =>{
                            return Navigator.SceneConfigs.PushFromRight;
                        }}
                        renderScene={(route, navigator) => {
                            let Component = route.component;
                            return <Component {...route.params} navigator={navigator} />
                          }}
                    >
                    </Navigator>
                </TabNavigator.Item> */}
                {/* <TabNavigator.Item
                    title="My"
                    renderIcon={() => <Image source={require('../assets/icon/my.png')} style={styles.iconStyle} />}
                    renderSelectedIcon={() => <Image source={require('../assets/icon/my_lighted.png')} style={styles.iconStyle} />}
                    onPress={() => { this.setState({ selectedTab: 'My' }) }}
                    selected={this.state.selectedTab === 'My'}
                >
                    <Navigator
                        initialRoute={{name:'My',component:My}}
                        configureScene={(route, routeStack) =>{
                            return Navigator.SceneConfigs.PushFromRight;
                        }}
                        renderScene={(route, navigator) => {
                            let Component = route.component;
                            return <Component {...route.params} navigator={navigator} />
                          }}
                    >
                    </Navigator>
                </TabNavigator.Item> */}
            </TabNavigator>
        );
    }
}

const styles = StyleSheet.create({
    tabBar: {
        height: 60,
        backgroundColor:"#353B45",
        overflow: 'hidden'
    },
    iconStyle: {
        width: Platform.OS === 'ios' ? 30 : 25,
        height: Platform.OS === 'ios' ? 30 : 25,
    },
    selectedIconStyle:{
        color:'green'
    }

});
// 输出类
module.exports = MainPage;

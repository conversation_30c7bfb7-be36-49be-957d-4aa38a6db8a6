import React, { Component } from 'react';
import { View, ScrollView, Text, Image, TextInput, StyleSheet, Dimensions,Alert } from 'react-native';
import { WToast } from 'react-native-smart-tip';
import { TouchableOpacity } from 'react-native-gesture-handler';
import CommonHeadScreen from '../../component/CommonHeadScreen';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';

import { uploadImageLibrary } from '../../utils/UploadImageUtils';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
class Profile extends Component {

    constructor(props) {
        super(props);
        this.state = {
            userPhotoUrl: constants.image_addr + constants.loginUser.userPhoto,
            userPhoto:""
        }
    }

    UNSAFE_componentWillMount() {
        httpPost("/biz/portal/user/get", { "userId": constants.loginUser.userId }, (response) => {
            if (response.code === 200) {
                let userPhoto = response.data.userPhoto;
                this.setState({
                    userPhotoUrl: constants.image_addr + '/' + userPhoto,
                    userPhoto:userPhoto
                })
            }
        });
    }

    deleteUser=()=>{
        Alert.alert('确认','您确定要注销吗？注销过后账号将被彻底清除，不可找回，请谨慎操作',[
            {
                text:"取消", onPress:()=>{
                WToast.show({data:'点击了取消'});
                // this在这里可用，传到方法里还有问题
                // this.props.navigation.goBack();
                }
            },
            {
                text:"确定", onPress:()=>{
                    WToast.show({data:'点击了确定注销'});
                    httpDelete("/biz/role/role_user_delete", { "userId": constants.loginUser.userId }, (response) => {
                    switch (response.code) {
                        case 200:
                            toastOpts = getSuccessToastOpts('账号已注销，退出系统');
                            WToast.show(toastOpts)
                            this.logout();
                            break;
                        default:
                            toastOpts = getFailToastOpts(response.message);
                            WToast.show({data:response.message})
                        }
                    });
                }
            }
        ]);
    }

    logout=()=>{
        console.log("===logout");
        let url = "/biz/user/logout?a=123&b=234"
        httpGet(url, this.logout_call_back);
    }

    logout_call_back=(response)=>{
        console.log("=====logout_call_back:", response);
        this.props.navigation.navigate('LoginView');
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }
    render() {
        return (
            <ScrollView style={CommonStyle.contentViewStyle}>
                <CommonHeadScreen title='个人信息'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.inputRowStyle, { height: 100 }]}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>头像</Text>
                    </View>
                    <View style={[{ width: screenWidth - (leftLabWidth + 25) }]}>
                        <TouchableOpacity onPress={() => {
                            uploadImageLibrary(this.state.userPhotoUrl, "user_header", (imageUploadResponse) => {
                                console.log("========imageUploadResponse", imageUploadResponse)
                                if (imageUploadResponse.code === 200) {
                                    WToast.show({ data: "成功上传" });
                                    let { compressFile } = imageUploadResponse.data
                                    this.setState({
                                        userPhotoUrl: constants.image_addr + '/' + compressFile
                                    })
                                    httpPost("/biz/portal/user/modify_user_photo", {
                                        "userId":constants.loginUser.userId, 
                                        "userPhoto":compressFile
                                    }, (updateResponse)=>{
                                        if (updateResponse.code === 200) {
                                            console.log("======用户头像已经更新")
                                        }
                                    })
                                }
                                else {
                                    WToast.show({ data: imageUploadResponse.message });
                                }
                            });

                        }}>
                            {
                                this.state.userPhoto?
                                <Image source={{ uri: this.state.userPhotoUrl }} style={{ height: 80, width:80, borderRadius:50 }} />
                                :
                                <Image style={{width:80, height:80}} source={require('../../assets/icon/iconfont/addHEAD.png')}></Image>
                            }
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>员工姓名</Text>
                    </View>
                    <View style={[styles.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 16 }}>
                            {constants.loginUser.userName}
                        </Text>
                    </View>
                </View>
                <View style={[styles.inputRowStyle]}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                    </View>
                    <View style={[styles.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 16 }}>
                            {constants.loginUser.userNbr}
                        </Text>
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>管理权限</Text>
                    </View>
                    <View style={[styles.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 16 }}>
                            {constants.roleInfo.roleName}
                        </Text>
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>登录账号</Text>
                    </View>
                    <View style={[styles.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 16 }}>
                            {constants.loginUser.userCode}
                        </Text>
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>登录时间</Text>
                    </View>
                    <View style={[styles.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 16 }}>
                            {constants.loginUser.lastLoginTime}
                        </Text>
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>今日登录(次)</Text>
                    </View>
                    <View style={[styles.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 16 }}>
                            {constants.loginUser.todayLoginTimes}
                        </Text>
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>登录总数(次)</Text>
                    </View>
                    <View style={[styles.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 16 }}>
                            {constants.loginUser.loginTimes}
                        </Text>
                    </View>
                </View>
                {
                    constants.loginUser.portalDepartmentDTOList ?
                        <View style={[{ flexDirection: 'row', marginTop: 10 }]}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>任职部门</Text>
                            </View>
                            <View style={{ flexDirection: 'column' }}>
                                {
                                    constants.loginUser.portalDepartmentDTOList.map((item, key) => {
                                        return (
                                            <View style={[styles.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }, { flexWrap: 'wrap', borderWidth: 0, borderColor: '#F1F1F1', borderBottomWidth: 1, width: screenWidth - (leftLabWidth + 15) }]}>
                                                <Text style={{ color: '#A0A0A0', fontSize: 16 }}>
                                                    {item.departmentName}
                                                </Text>
                                            </View>
                                        )
                                    })
                                }
                            </View>
                        </View>
                        :
                        <View></View>
                }
                {
                    constants.loginUser.portalJobDTOList ?
                        <View style={[{ flexDirection: 'row', marginTop: 10 }]}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>职务</Text>
                            </View>
                            <View style={{ flexDirection: 'column' }}>
                                {
                                    constants.loginUser.portalJobDTOList.map((item, key) => {
                                        return (
                                            <View style={[styles.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }, { flexWrap: 'wrap', borderWidth: 0, borderColor: '#F1F1F1', borderBottomWidth: 1, width: screenWidth - (leftLabWidth + 15) }]}>
                                                <Text style={{ color: '#A0A0A0', fontSize: 16 }}>
                                                    {item.jobName}
                                                </Text>
                                            </View>
                                        )
                                    })
                                }
                            </View>
                        </View>
                        :
                        <View></View>
                }

                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>所属公司</Text>
                    </View>
                    <View style={[styles.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 16 }}>
                            {constants.loginUser.tenantName}
                        </Text>
                    </View>
                </View>
                <View style={{   alignItems:'center',}}>
                <TouchableOpacity onPress={this.deleteUser.bind(this)}>
                    <View style={[ {backgroundColor:'red', justifyContent:'center',alignItems:'center', height:40, borderRadius:2, marginBottom:15, marginTop:10, width:(screenWidth - 50)}]}>
                        <Text style={CommonStyle.btnRowRightSaveBtnText}>注销</Text>
                    </View>
                </TouchableOpacity>
                </View>
            </ScrollView>
        )
    }
}


const styles = StyleSheet.create({
    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputTextStyleTextStyle: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    },
    inputTextStyleTextStyleAutoHeight: {
        width: screenWidth - (leftLabWidth + 15),
        borderRadius: 0,
        borderColor: 'red',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        justifyContent: 'center'
    }
})
export default Profile
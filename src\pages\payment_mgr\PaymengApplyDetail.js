import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class PaymengApplyDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            applyAuditId:"",
            auditState:"",
            ccUserDataSource:[],
            auditConfigId:"",
            auditTypeCode:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId,auditItemId,auditTypeCode} = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }

            if (auditItemId && auditTypeCode) {
                console.log("=============auditItemId" + auditItemId + "");
                let url= "/biz/audit/node/record/auditListByItemId";
                let loadRequest={
                    "auditItemId": auditItemId,
                    "excludeAuditResult":"I",
                    "auditTypeCode":auditTypeCode
                };
                httpPost(url, loadRequest, this.loadAuditListByItemIdCallBack);

                this.setState({
                    applyAuditId:auditItemId,
                    auditTypeCode:auditTypeCode
                })
                url= "/biz/payment/apply/audit/get";
                loadRequest={'applyAuditId':auditItemId};
                httpPost(url, loadRequest, this.loadEditPurchaseDataCallBack);
            }
        }
    }

    loadAuditListByItemIdCallBack=(response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                dataSource:response.data.reverse(),
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditPurchaseDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                auditState:response.data.auditState,
                auditConfigId:response.data.auditConfigId,
            })
            if(null != response.data.auditConfigId) {
                this.loadAuditCcConfigList(response.data.auditConfigId)
            }
        }
    }

    loadAuditCcConfigList=(auditConfigId)=>{
        let url = "/biz/audit/cc/config/list";
        console.log("auditConfigId ++++===",auditConfigId);
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
            "auditConfigId":auditConfigId ? auditConfigId : this.state.auditConfigId
        };
        httpPost(url, loadRequest, this.loadAuditCcConfigListCallBack);
    }

    loadAuditCcConfigListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                ccUserDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }

    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity>
                {/* <Text style={CommonStyle.headRightText}>新增</Text> */}
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='申请详情'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                {
                    this.state.dataSource && this.state.dataSource.length > 0 ?
                    <View>
                        {this.state.dataSource.map((item, index)=>{
                            return(
                                <View key={item.recordId} style={styles._innerViewStyle}>
                                    <View style={[styles._titleViewStyle]}>
                                        <Text style={[styles._titleTextStyle,{fontWeight:'bold',fontSize:18}]}>{item.nodeName}</Text>
                                    </View>
                                    <View style={styles._titleViewStyle}>
                                        <Text style={styles._titleTextStyle}>审核人：{item.userName}</Text>
                                    </View>
                                    <View style={styles._titleViewStyle}>
                                        <Text style={styles._titleTextStyle}>审核批复：{item.auditResult === 'Y' ? "同意" : "驳回"}</Text>
                                    </View>
                                    <View style={styles._titleViewStyle}>
                                        <Text style={styles._titleTextStyle}>审核意见：{item.auditOpinion?item.auditOpinion:"无"}</Text>
                                    </View>
                                    <View style={styles._titleViewStyle}>
                                        <Text style={styles._titleTextStyle}>审核时间：{item.gmtModified ? item.gmtModified : item.gmtCreated}</Text>
                                    </View>
                                </View>
                            )
                                
                            })
                        }
                        {
                            this.state.auditState == '3' || this.state.auditState == '4' ?
                            <View style={styles._innerViewStyle}>
                                <View style={[styles._titleViewStyle,{justifyContent:"flex-start"}]}>
                                    <Text style={[styles._titleTextStyle,{fontWeight:'bold',fontSize:18}]}>抄送人：</Text>
                                    {
                                    this.state.ccUserDataSource.map((item, index)=>{
                                        return(
                                            <View key={item.recordId}>
                                                <Text style={styles._titleTextStyle}>{item.userName + ((index == this.state.ccUserDataSource.length - 1)?"":"、")}</Text>
                                            </View>
                                        )
                                    })
                                }
                                </View>
                                
                            </View>
                            :
                            <View/>
                        }
                            
                    </View> 
                    :
                    <EmptyListComponent/>
                    
                }

                
                
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    _innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:0,
    },
    _titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    _titleTextStyle:{
        fontSize:16
    },

    innerViewStyle:{
        marginTop:10,
        backgroundColor:"#FFFFFF"
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10
    },

    titleTextStyle:{
        fontSize:18
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyRowView:{
        flexDirection:'row', 
        // backgroundColor:'yellow'
    },
    bodyRowLeftView:{
        width:screenWidth/2-40, 
        flexDirection:'row', 
        // backgroundColor:'yellow'
    },
    bodyRowRightView:{
        // backgroundColor:'green', 
        flexDirection:'row', 
        alignItems:'flex-start',
        paddingLeft:10,
        marginRight:5, 
        // alignItems:'flex-start', 
        justifyContent:'flex-start',
        alignContent:'flex-start'
    },
});
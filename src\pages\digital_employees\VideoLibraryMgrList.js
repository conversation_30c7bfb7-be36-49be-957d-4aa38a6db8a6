import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Image, ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class VideoLibraryMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 6,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            selCourseLevelCode: 'all',
            courseLevelDataSource: [],
            selectCourse: [],
            courseDataSource: [],
            courseName: "",
            courseId: null,
            topBlockLayoutHeight: 0,
            searchKeyWord: null,
            showCourseTypeSearchItemBlock: false,
            courseTypeDataSource: [],
            selcourseTypeId: null,
            selcourseTypeName: null,
            documentTypeList:["TV","PV"]
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        // 所属职级
        // loadTypeUrl = "/biz/course/course_level";
        loadTypeUrl = "/biz/course/join/task/find_course_level";
        loadRequest = { "qryAll": "Y" };
        httpPost(loadTypeUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    courseLevelDataSource: response.data,
                })
            }
        });

        this.loadDocumentList();

        // 加载砖型
        // var loadTypeUrl = "/biz/course/course_level_course_tree";
        loadTypeUrl = "/biz/course/join/task/course_level_course_tree";
        loadRequest = { 'currentPage': 1, 'pageSize': 10000 };
        httpPost(loadTypeUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data && response.data) {
                this.setState({
                    courseDataSource: response.data
                })
            }
            else if (response.code == 401) {
                WToast.show({ data: response.message });
                this.props.navigation.navigate("LoginView");
            }
        });

        let loadCourseTypeUrl = "/biz/course/type/list";
        let loadCourseTypeRequest = { "qryAll": "Y", "currentPage": 1, "pageSize": 1000 };
        httpPost(loadCourseTypeUrl, loadCourseTypeRequest, (response) => {
            if (response.code == 200 && response.data.dataList) {
                var courseTypeData = response.data.dataList;
                courseTypeData.unshift({ "courseTypeId": 0, "courseTypeName": "全部" })
                this.setState({
                    courseTypeDataSource: courseTypeData,
                })
                // courseTypeData
                // console.log("==========课程类型数据源：", this.state.courseTypeDataSource);
            }
        });
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/document/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "courseId": this.state.courseId,
            "courseLevelId": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
            "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
            "searchKeyWord": this.state.searchKeyWord,
            "documentTypeList":this.state.documentTypeList
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {

        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.courseId == null && this.state.searchKeyWord == null) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            brickTypeName: "",
            brickTypeId: null,
            searchKeyWord: null,
        })
        this.setState({
            currentPage: 1
        })
        let url = "/biz/document/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "courseId": null,
            "courseLevelId": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
            "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
            "searchKeyWord": this.state.searchKeyWord,
            "documentTypeList":this.state.documentTypeList
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            // var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            // var dataAll = [...dataNew];
            console.log("数据" + JSON.stringify(response.data, null, 6))
            this.setState({
                dataSource: response.data.dataList,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadDocumentList();
    }

    loadDocumentList = () => {
        let url = "/biz/document/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "courseId": null,
            "courseLevelId": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
            "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
            "searchKeyWord": this.state.searchKeyWord,
            "documentTypeList":this.state.documentTypeList
        };
        httpPost(url, loadRequest, this.loadDocumentListCallBack);
    }

    loadDocumentListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld, ...dataNew];
            // var dataAll = dataOld.concat(dataNew.filter(v => !dataOld.includes(v)))
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow = (item, index) => {
        return (

            <View key={item.videoId} style={CommonStyle.innerViewStyle}>
                <View style={[styles.titleViewStyle, { marginTop: 10 }]}>
                    <Text style={styles.titleTextStyle}>视频名称：</Text>
                    <Text style={styles.itemContentStyle}>{item.documentName}</Text>
                </View>
                {/* <TouchableOpacity onPress={() => {
                    this.props.navigation.navigate("VideoLibraryView",
                        {
                            videoId: item.videoId,
                            videoTitle: item.videoTitle,
                            videoUrl: item.videoUrl,
                            videoImage: item.videoImage,
                            videoDesc: item.videoDesc,
                            courseName: item.courseName,
                            courseLevelName: item.courseLevelName,
                            // 传递回调函数
                            refresh: this.callBackFunction
                        })
                }}>
                    <Image style={{ height: 160, }} source={{ uri: item.videoImage }} />
                </TouchableOpacity> */}
                <View style={[styles.titleViewStyle, { marginTop: 5 }]}>
                    <Text style={styles.titleTextStyle}>课程名称：</Text>
                    <Text style={styles.itemContentStyle}>{item.courseName}</Text>
                </View>
                <View style={[styles.titleViewStyle, { marginTop: 5 }]}>
                    <Text style={styles.titleTextStyle}>所属职级：</Text>
                    <Text style={styles.itemContentStyle}>{item.courseLevelName}</Text>
                </View>
                <View style={[styles.titleViewStyle, { marginTop: 5, marginBottom: 10 }]}>
                    <Text style={styles.titleTextStyle}>提交时间：</Text>
                    <Text style={styles.itemContentStyle}>{item.gmtCreated}</Text>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle, { borderColor: "#F2F5FC", flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                    <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                        <TouchableOpacity onPress={() => {
                            this.props.navigation.navigate("VideoLibraryView",
                            {
                                documentId: item.documentId,
                                documentName: item.documentName,
                                courseName: item.courseName,
                                courseLevelName: item.courseLevelName,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                        }}>
                            <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64 }]}>
                                <Image style={{ width: 20, height: 20, marginRight: 3 }} source={require('../../assets/icon/iconfont/view.png')}></Image>
                                <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }}>查看</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }
    // 渲染砖型底部滚动数据
    open2ColumnSelect() {
        if (!this.state.courseDataSource || this.state.courseDataSource.length < 1) {
            WToast.show({ data: "请先添加课程" });
            return
        }
        this.refs.SelectCourse.showBrickType(this.state.selectCourse, this.state.courseDataSource)
    }

    callBackSelect2ColumnValue(value) {
        console.log("==========选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            "selectCourse": value,
            "selCourseLevelCode": "all",
            "selcourseTypeName": null,
            "selcourseTypeId": 0,
        })
        // 取选定的砖型ID
        if (value.length == 2) {
            // 加载砖型
            let loadTypeUrl = "/biz/course/get_course_by_level_course_name";
            let loadRequest = {
                "courseLevelName": value[0],
                "courseName": value[1],
            };
            httpPost(loadTypeUrl, loadRequest, this._callBackLoadCourseData);
        }
        else {
            console.log("======选择课程返回数据不合法", value)
        }
    }

    _callBackLoadCourseData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                courseName: response.data.courseName,
                courseId: response.data.courseId,
                searchKeyWord: null,
            })
            let loadUrl = "/biz/course/video/list";
            let loadRequest = {
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "courseId": response.data.courseId,
                "courseLevel": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
                "searchKeyWord": null,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
            this.setState({
                courseName: '',
                courseId: '',
                searchKeyWord: null,
            })
        }
    }

    // 课程类型
    renderCourseTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selcourseTypeId: item.courseTypeId,
                    selcourseTypeName: item.courseTypeName,
                })
            }}>
                <View key={"department_" + item.courseTypeId} style={[item.courseTypeId === this.state.selcourseTypeId ? CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                CommonStyle.choseToSearchItemsViewSize]}>
                    <Text style={[item.courseTypeId === this.state.selcourseTypeId ? CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.courseTypeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    searchByKeyWord = () => {
        let toastOpts;
        if (!this.state.searchKeyWord) {
            toastOpts = getFailToastOpts("请输入文档名称或创建人");
            WToast.show(toastOpts)
            return;
        }
        this.setState({
            courseName: "",
            courseId: null,
        })

        let loadUrl = "/biz/course/video/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "courseId": null,
            "courseLevel": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    renderCourseLevelRow = (item, index) => {
        return (
            <View key={item.courseLevelCode} >
                <TouchableOpacity onPress={() => {
                    let selCourseLevelCode = item.courseLevelCode;
                    this.setState({
                        courseId: null,
                        courseName: "",
                        selCourseLevelCode: selCourseLevelCode
                    })

                    let loadUrl = "/biz/course/video/list";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "courseLevelId": selCourseLevelCode === "all" ? null : selCourseLevelCode,
                        "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
                        "searchKeyWord": this.state.searchKeyWord,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.courseLevelCode} style={[CommonStyle.tabItemViewStyle, { width: 40, }]}>
                        <Text style={[item.courseLevelCode === this.state.selCourseLevelCode ? [CommonStyle.selectedtabItemTextStyle] : [CommonStyle.tabItemTextStyle]]}>
                            {item.courseLevelName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }
    // 显示搜索项目
    showCourseTypeSearchItemSelect() {
        if (!this.state.courseTypeDataSource || this.state.courseTypeDataSource.length < 1) {
            WToast.show({ data: "请先添加课程类型" });
            return
        }
        this.setState({
            showCourseTypeSearchItemBlock: true,
        })
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='视频库'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.rightTop50FloatingBlockView,
                {
                    height: 32,
                    width: 110,
                    opacity: 0.6,
                    borderRadius: 8,
                    backgroundColor: "rgba(242, 245, 252, 1)",
                    marginTop: 40
                }]}>
                    <TouchableOpacity onPress={() => this.open2ColumnSelect()}>
                        <Text style={{ color: 'rgba(0,10,32,0.85)', fontSize: 14 }}>
                            {!this.state.courseName ? "课程" : this.state.courseName}
                        </Text>
                    </TouchableOpacity>

                </View>

                <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.courseLevelDataSource && this.state.courseLevelDataSource.length > 0)
                                ?
                                this.state.courseLevelDataSource.map((item, index) => {
                                    return this.renderCourseLevelRow(item)
                                })
                                : <View />
                        }
                    </View>
                    <View style={{ flexDirection: 'row', justifyContent: "flex-start", flexWrap: 'wrap', flexDirection: 'row' }}>
                        <View style={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                            <TouchableOpacity onPress={() => this.showCourseTypeSearchItemSelect()}>

                                {
                                    this.state.showCourseTypeSearchItemBlock ?
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                                {this.state.selcourseTypeId && this.state.selcourseTypeName ? (this.state.selcourseTypeName) : "课程类型"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchClosedIconSize]} source={require('../../assets/icon/iconfont/arrow-up.png')}></Image>
                                        </View>
                                        :
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                                {this.state.selcourseTypeId && this.state.selcourseTypeName ? (this.state.selcourseTypeName) : "课程类型"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchOpenedIconSize]} source={require('../../assets/icon/iconfont/arrow_down.png')}></Image>
                                        </View>
                                }
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>

                <View>


                    {
                        this.state.showCourseTypeSearchItemBlock ?
                            <View style={[CommonStyle.choseToSearchBigBoxViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                                <View style={CommonStyle.heightLimited}>
                                    <ScrollView>
                                        <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                            <View style={[{ backgroundColor: 'rgba(255,255,255,1)' }, CommonStyle.choseToSearchItemsViewSize]}>
                                                <Text style={{ fontSize: 16, fontWeight: 'bold' }}>课程类型：</Text>
                                            </View>
                                            {
                                                (this.state.courseTypeDataSource && this.state.courseTypeDataSource.length > 0)
                                                    ?
                                                    this.state.courseTypeDataSource.map((item, index) => {
                                                        return this.renderCourseTypeRow(item)
                                                    })
                                                    : null
                                            }
                                        </View>
                                    </ScrollView>
                                </View>
                                <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showCourseTypeSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        let loadUrl = "/biz/course/video/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "courseLevelId": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
                                            "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
                                        };
                                        console.log("选择的课程类型=====" + this.state.selcourseTypeId)
                                        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                        this.setState({
                                            showCourseTypeSearchItemBlock: false,
                                            courseId: null,
                                            courseName: "",
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            null
                    }

                    <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                        {/* <View style={CommonStyle.contentViewStyle}> */}

                        <FlatList
                            data={this.state.dataSource}
                            renderItem={({ item, index }) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                    tintColor="#FF0000"
                                    title="loading"
                                    colors={['#FF0000', '#00FF00', '#0000FF']}
                                    progressBackgroundColor="#FFFF00"
                                    refreshing={this.state.refreshing}
                                    onRefresh={() => {
                                        this._loadFreshData()
                                    }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={() => this.flatListFooterComponent()}
                            onEndReached={() => this._loadNextData()}
                        />
                    </View>
                </View>
                <BottomScrollSelect
                    ref={'SelectCourse'}
                    callBackBrickTypeValue={this.callBackSelect2ColumnValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 14,
        height: 40,
        flexDirection: 'row',
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2.8,
        borderColor: '#000000',
        borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 18,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0
    },

    innerViewStyle: {
        marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 14,
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    // itemContentViewStyle:{
    //     flexDirection:'row',
    //     justifyContent:'space-between',
    //     marginLeft:25
    // },
    itemContentChildViewStyle: {
        justifyContent: 'space-between',
        flexDirection: 'row',
    },
});

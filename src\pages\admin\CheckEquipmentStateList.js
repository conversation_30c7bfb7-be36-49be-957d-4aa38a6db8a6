import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,
    FlatList,RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class CheckEquipmentStateList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            equipmentId:null,
            equipmentName:'',
            equipmentStateName:'',
            selEquipmentState:"all",
            equipmentStateDataSource:[
                {"code":"all", "name":"全部"},
                {"code":"0AA", "name":"正常"},
                {"code":"0AB", "name":"维修"},
                {"code":"0AC", "name":"保养"},
            ]
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { equipmentId, equipmentName, equipmentStateName } = route.params;
            if (equipmentId) {
                this.setState({
                    equipmentId:equipmentId,
                })
                this.loadJobList(equipmentId);
            }
            if (equipmentName) {
                this.setState({
                    equipmentName:equipmentName,
                })
            }
            if (equipmentStateName) {
                this.setState({
                    equipmentStateName:equipmentStateName,
                })
            }
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let loadUrl;
        let loadRequest;

        if (this.state.equipmentId) {
            loadUrl = "/biz/equipment/get";
            loadRequest={
                "equipmentId": this.state.equipmentId
            };
            httpPost(loadUrl, loadRequest, (response)=>{
                if (response.code == 200 && response.data) {
                    this.setState({
                        equipmentId:response.data.equipmentId,
                        equipmentName:response.data.equipmentName,
                        equipmentStateName:response.data.equipmentStateName
                    })
                }
            });
        }

        loadUrl = "/biz/equipment/record/list";
        loadRequest = { 
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "equipmentId": this.state.equipmentId,
            "equipmentState": this.state.selEquipmentState === 'all' ? null : this.state.selEquipmentState,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        
        
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/equipment/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "equipmentId": this.state.equipmentId,
            "equipmentState": this.state.selEquipmentState === 'all' ? null : this.state.selEquipmentState,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadJobList();
    }

    loadJobList=(equipmentId)=>{
        let loadUrl= "/biz/equipment/record/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "equipmentId": equipmentId ? equipmentId : this.state.equipmentId,
            "equipmentState": this.state.selEquipmentState === 'all' ? null : this.state.selEquipmentState,
        };
        console.log("===========loadRequestssss:", loadRequest);
        httpPost(loadUrl, loadRequest, this.loadJobListCallBack);
    }

    loadJobListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteJob =(jobId)=> {
        console.log("=======delete=jobId", jobId);
        let url= "/biz/equipment/record/delete";
        let requestParams={'jobId':jobId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.recordId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>设备状态：{item.equipmentStateName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>状态说明：{item.remark}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>最近更新时间：{item.gmtModified ? item.gmtModified: item.gmtCreated }</Text>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { 
                if (this.props.route.params && this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                this.props.navigation.goBack() 
            }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    renderEquipmentStateRow=(item, index)=>{
        return (
            <View key={item.code} >
                <TouchableOpacity onPress={()=>{
                    let selEquipmentState = item.code;
                    this.setState({
                        "selEquipmentState":selEquipmentState
                    })

                    let loadUrl= "/biz/equipment/record/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "equipmentId": this.state.equipmentId,
                        "equipmentState": selEquipmentState === 'all' ? null : selEquipmentState,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.code} style={[item.code===this.state.selEquipmentState? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle, {padding:10, margin:5, }] }>
                        <Text style={[item.code===this.state.selEquipmentState? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                            {item.name}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='设备状况-明细'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0, index:1000}]} onLayout={this.topBlockLayout.bind(this)}>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, fontWeight:'bold', marginRight:100}]}>设备名称：{this.state.equipmentName}</Text>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, fontWeight:'bold', marginRight:100}]}>
                         {/* 设备状态：{this.state.dataSource && this.state.dataSource.length > 0 ? this.state.dataSource[0].equipmentStateName : this.state.equipmentStateName} */}
                         设备状态：{this.state.equipmentStateName}
                    </Text>
                    <FlatList 
                        numColumns = {4}
                        data={this.state.equipmentStateDataSource}
                        renderItem={({item}) => this.renderEquipmentStateRow(item)}
                    />
                </View>
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});
import React,{ Component } from 'react';
import {View, Text, StyleSheet,Dimensions,Image} from 'react-native';
var CommonStyle = require('../assets/css/CommonStyle');
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
class ProductEmptyRowViewComponent extends Component {

    constructor(props) {
        super(props);
        this.state = {  
            height:props.height
        };
    }

    render(){
        return(
            <View style={[{height:(this.state.height ? this.state.height : 150), width:screenWidth,alignItems: 'center', justifyContent: 'center'}]}>
                <Image  style={{width:75, height:75}} source={require('../assets/image/emptyImage.png')}></Image>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:60,
    //     width:screenWidth,
    //     alignItems: 'center',
    //     justifyContent: 'center',
    //     justifyContent: 'center',
    // },
    contentTextStyle:{
        color:'#A0A0A0',
        fontSize:25
    }
})
module.exports = ProductEmptyRowViewComponent;
import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,Modal 
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../utils/CameraRollUtils';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class PointExchange extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            exchangeGoodsImage:"",
            // goodsImageUrl:constants.image_addr + constants.loginUser.exchangeGoodsImage,
            isShowImage: false,
            pictureIndex:0
        }
    }
     //下拉视图开始刷新时调用
     _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }
    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { exchangeConfigId} = route.params;
            if (exchangeConfigId) {
                console.log("=============exchangeConfigId" + exchangeConfigId + "");
            }
            this.loadPointExchangeConfigList(exchangeConfigId);
        }
    }
     // 回调函数
     callBackFunction=()=>{
        let url= "/biz/point/exchange/config/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/point/exchange/config/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N" ,pictureDisplay: "N",personPictureDisplay:false}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
     // 上拉触底加载下一页
     _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadPointExchangeConfigList();
    }

    loadPointExchangeConfigList=()=>{
        let url= "/biz/point/exchange/config/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this.loadPointExchangeConfigListCallBack);
    }


    loadPointExchangeConfigListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N" ,pictureDisplay: "N",personPictureDisplay:false}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deletePointExchangeConfig =(exchangeConfigId)=> {
        console.log("=======delete=exchangeConfigId", exchangeConfigId);
        let url= "/biz/point/exchange/config/delete";
        let requestParams={'exchangeConfigId':exchangeConfigId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }
    renderRow=(item, index)=>{
        return (
            <View key={item.pointExchangeId} style={[CommonStyle.innerViewStyle]}>
                <View style={{flexDirection: 'row',justifyContent: 'space-between',marginBottom:5,marginTop:5,marginLeft:10,marginRight:90}}>
                    <Text style={styles.titleTextStyle}>商品名称：{item.exchangeGoodsName}</Text> 
                </View>
                <View style={{position:'absolute',right:10,top:10}}>
                    {
                        item.exchangeGoodsImage?
                        <View style={{height: 80, width:80}}>
                            <View>   
                            <TouchableOpacity onPress={() => {
                                let list = this.state.dataSource;
                                list.map((elem, index) => {
                                    if(elem.pointExchangeId == item.pointExchangeId){
                                        item.personPictureDisplay = true;
                                    }
                                })
                                console.log("personPictureDisplay",item.personPictureDisplay)
                                this.setState({
                                    dataSource:list,
                                })
                            }}>
                                <Image source={{ uri: (constants.image_addr + '/' + item.exchangeGoodsImage) }} style={{width:80,height:80,justifyContent:'center',alignItems:'center'}} />                                                    
                            </TouchableOpacity>
                            <Modal visible={item.personPictureDisplay} transparent={true}>
                                <ImageViewer enableSwipeDown={false} menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                    saveToLocalByLongPress={true}
                                    onClick={() => { // 图片单击事件
                                        let list = this.state.dataSource;
                                        list.map((elem, index) => {
                                            if(elem.pointExchangeId == item.pointExchangeId){
                                                item.personPictureDisplay = false;
                                            }
                                        })
                                        console.log("personPictureDisplay",item.personPictureDisplay)
                                        this.setState({
                                            dataSource:list,
                                        })
                                    }}
                                    imageUrls={[{url:(constants.image_addr + '/' + item.exchangeGoodsImage)}]} 
                                    onSave ={() => {
                                        var imageUrl = constants.image_addr + '/' + item.exchangeGoodsImage;
                                        saveImage( imageUrl); 
                                    } }
                                />
                            </Modal>
                            </View>
                            {/* <Text>{constants.image_addr+ '/' + item.exchangeGoodsImage}</Text> */}
                        </View>
                        :
                        <View style={{height: 80, width:80,borderColor:'#AAAAAA',borderWidth:0.3,justifyContent:'center',alignItems:'center'}}>
                            <Text style={[styles.titleTextStyle,{color:"#aaaaaa"}]}>无</Text>
                        </View>
                    }
                </View>
                    {/* {
                        item.goodsImage ?
                        <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    isShowImage:true,
                                    pictureIndex:index
                                })                                                    
                            }}>
                                <Image source={{ uri: this.state.goodsImageUrl }} style={{width:120,height:150,justifyContent:'center',alignItems:'center'}} />
                            </TouchableOpacity>
                            <Modal visible={this.state.isShowImage} transparent={true}>
                                <ImageViewer index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} 
                                onSave={() => alert("点击了保存图片")} onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} />
                            </Modal>
                        </View>
                        :
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>暂无</Text>
                        </View>
                    } */}
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>所需积分：{item.exchangePointValue}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>排序：{item.exchangeGoodsSort}</Text>
                </View>
                
                
                <View style={[CommonStyle.itemBottomBtnStyle,{ flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','确定要删除该商品吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deletePointExchangeConfig(item.exchangeConfigId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle, { width: 64 }]}>
                            <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("PointExchangeAdd", 
                            {
                                // 传递参数
                                exchangeConfigId:item.exchangeConfigId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64 }]}>
                            <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PointExchangeAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='积分兑换'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle: {
        // marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 8,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});
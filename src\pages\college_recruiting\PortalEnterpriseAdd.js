import React,{Component} from 'react';
import {
    View,Text,StyleSheet,TextInput,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class PortalEnterpriseAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            currentPage:1,
            operate:"",
            enterpriseId:"",
            enterpriseName:"",
            enterpriseAbbreviation:"",
            enterpriseIntroduction:"",
            enterpriseSort:0,
            roleDataSource:[],
            selectRole:[],
            roleId:"",
            roleName:"",
            goodsImageUrl:"",
            enterpriseLogo:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadRoleList();
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { enterpriseId } = route.params;
            if (enterpriseId) {
                console.log("=============enterpriseId" + enterpriseId + "");
                this.setState({
                    enterpriseId:enterpriseId,
                    operate:"编辑"
                })
                let loadTypeUrl= "/biz/enterprise/get";
                let loadRequest={'enterpriseId':enterpriseId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditEnterpriseDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增",
                    goodsImageUrl:constants.image_addr
                })
            }
        }
    }
    
    loadEditEnterpriseDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                enterpriseName:response.data.enterpriseName,
                enterpriseAbbreviation:response.data.enterpriseAbbreviation,
                enterpriseIntroduction:response.data.enterpriseIntroduction,
                enterpriseSort:response.data.enterpriseSort,
                roleName:response.data.roleName,
                selectRole:[response.data.roleName],
                enterpriseLogo:response.data.enterpriseLogo,
                goodsImageUrl:constants.image_addr+response.data.enterpriseLogo
            })
        }
    }

    loadRoleList=()=>{
        let url= "/biz/role/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": 1000,
            "excludeRoleIdList":[constants.loginUser.roleId]
        };
        httpPost(url, loadRequest, this.loadRoleListCallBack);
    }

    loadRoleListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.roleDataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                roleDataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PortalEnterpriseList", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>企业管理</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveEnterprise =()=> {
        console.log("=======saveEnterprise");
        let toastOpts;
        if (!this.state.enterpriseName) {
            toastOpts = getFailToastOpts("请填写企业名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.roleName) {
            toastOpts = getFailToastOpts("请选择企业角色");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/enterprise/add";
        if (this.state.enterpriseId) {
            console.log("=========Edit===enterpriseId", this.state.enterpriseId)
            url= "/biz/enterprise/modify";
        }
        let requestParams={
            enterpriseId:this.state.enterpriseId,
            enterpriseName: this.state.enterpriseName,
            enterpriseAbbreviation: this.state.enterpriseAbbreviation,
            enterpriseIntroduction: this.state.enterpriseIntroduction,
            enterpriseSort:this.state.enterpriseSort,
            roleId:this.state.roleId,
            // 企业图片
            enterpriseLogo:this.state.enterpriseLogo

        };
        httpPost(url, requestParams, this.saveEnterpriseCallBack);
    }

    // 保存回调函数
    saveEnterpriseCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    openRoleSelect=()=>{
        if (!this.state.roleDataSource || this.state.roleDataSource.length < 1) {
            WToast.show({data:"没有创建角色，请添加企业对应的角色"});
            return
        }
        console.log("==========角色数据源：", this.state.roleDataSource);
        this.refs.SelectRole.showRole(this.state.selectRole, this.state.roleDataSource)

    }

    callBackRoleValue(value){
        console.log("==========角色选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectRole:value
        })
        var roleName = value.toString();
        let loadUrl= "/biz/role/getRoleByName";
        let loadRequest={
            "roleName":roleName
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    roleName:response.data.roleName,
                    roleId:response.data.roleId,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({data:response.message});
            }
        });
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '企业'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>企业名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入企业名称'}
                            onChangeText={(text) => this.setState({enterpriseName:text})}
                        >
                            {this.state.enterpriseName}
                        </TextInput>
                    </View>

                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>企业logo</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[{ width: 120,height:150,marginBottom:10,display:'flex',justifyContent:'center',
                            alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                            <TouchableOpacity 
                                onPress={() => {
                                    console.log(this.state.imageUrl)
                                    uploadImageLibrary(this.state.imageUrl, "enterpriseLogo", (imageUploadResponse) => {
                                        console.log("========imageUploadResponse", imageUploadResponse)
                                        if (imageUploadResponse.code === 200) {
                                            WToast.show({ data: "成功上传" });
                                            let { compressFile } = imageUploadResponse.data
                                            console.log(compressFile)
                                            this.setState({
                                                //goodsImageUrl:服务器地址+图片存储地址
                                                imageUrl: constants.image_addr + '/' + compressFile,
                                                enterpriseLogo:compressFile,
                                            })
                                        }
                                        else {
                                            WToast.show({ data: imageUploadResponse.message });
                                        }
                                    });
                            }}
                            >
                                    {
                                        this.state.enterpriseLogo ?
                                        <Image source={{ uri: constants.image_addr + '/' +  this.state.enterpriseLogo }} style={{width:120,height:150,justifyContent:'center',alignItems:'center'}} />
                                        :
                                        <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                    }
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>企业角色</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openRoleSelect()}>
                            <View style={styles.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.roleName ? "请选择企业角色" : this.state.roleName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                   
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>企业简介</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:150}]}
                            placeholder={'请输入企业简介'}
                            onChangeText={(text) => this.setState({enterpriseIntroduction:text})}
                        >
                            {this.state.enterpriseIntroduction}
                        </TextInput>
                    </View>

                    

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'0'}
                            onChangeText={(text) => this.setState({enterpriseSort:text})}
                        >
                            {this.state.enterpriseSort}
                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row'}]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveEnterprise.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect 
                        ref={'SelectRole'} 
                        callBackRoleValue={this.callBackRoleValue.bind(this)}
                    />
                </View>

            </View>
        )
    }
}
const styles = StyleSheet.create({

    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputTextStyleTextStyle:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        height:45,
        justifyContent:'center'
    }

});
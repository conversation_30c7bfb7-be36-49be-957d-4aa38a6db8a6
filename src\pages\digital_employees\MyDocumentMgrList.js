import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Clipboard, Linking, TextInput, Image, ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class MyDocumentMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            courseLevelDataSource: [],
            topBlockLayoutHeight: 0,
            selCourseLevelCode: 'all',

            showCourseTypeSearchItemBlock: false,
            courseTypeDataSource: [],
            selcourseTypeId: null,
            selcourseLevelId: null,
            selcourseTypeName: null,
            selectCourse: [],
            courseDataSource: [],
            courseName: "",
            courseId: null,
            documentTypeList: ["PV", "PD"],
            typeList: [
                { id: 0, name: "文档", value: "PD" },
                { id: 1, name: "视频", value: "PV" }
            ],
            selType: "PD"
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;

        // 所属职级
        // loadTypeUrl = "/biz/course/course_level";
        loadTypeUrl = "/biz/course/join/task/find_course_level";
        loadRequest = { "qryAll": "Y" };
        httpPost(loadTypeUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    courseLevelDataSource: response.data,
                })
            }
        });

        // loadTypeUrl = "/biz/course/course_level_course_tree";
        loadTypeUrl = "/biz/course/join/task/course_level_course_tree";
        // addLine会在对应职级的课程列表前添加“-”，用于选择职级
        loadRequest = { 'currentPage': 1, 'pageSize': 10000, "addLine": "Y" };
        httpPost(loadTypeUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data && response.data) {
                this.setState({
                    courseDataSource: response.data
                })
            }
            else if (response.code == 401) {
                WToast.show({ data: response.message });
                this.props.navigation.navigate("LoginView");
            }
        });

        // 课程类型
        let loadCourseTypeUrl = "/biz/course/type/list";
        let loadCourseTypeRequest = { "qryAll": "Y", "currentPage": 1, "pageSize": 1000 };
        httpPost(loadCourseTypeUrl, loadCourseTypeRequest, (response) => {
            if (response.code == 200 && response.data.dataList) {
                var courseTypeData = response.data.dataList;
                courseTypeData.unshift({ "courseTypeId": 0, "courseTypeName": "全部" })
                console.log("courseTypeData" + JSON.stringify(courseTypeData, null, 6))
                this.setState({
                    courseTypeDataSource: courseTypeData,
                })
            }
        });
        this.loadDocumentList();
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/document/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "courseLevelId": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
            "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
            "documentTypeList": [this.state.selType]
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/document/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "courseLevelId": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
            "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
            "documentTypeList": [this.state.selType]
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadCourseList();
    }

    loadDocumentList = () => {
        let url = "/biz/document/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "courseLevelId": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
            "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
            "documentTypeList": [this.state.selType]
        };
        httpPost(url, loadRequest, this.loadCourseListCallBack);
    }

    loadCourseListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            console.log("dataAll" + JSON.stringify(dataAll, null, 6))
            // console.log("courseLevelDataSource" + JSON.stringify(this.state.courseLevelDataSource, null, 6))
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteDocument = (documentId) => {
        console.log("=======delete=documentId", documentId);
        let url = "/biz/document/delete";
        let requestParams = { 'documentId': documentId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    renderRow = (item, index) => {
        return (
            <View key={item.documentId} style={CommonStyle.innerViewStyle, { borderTopWidth: 0, borderTopWidth: 0 }}>
                <TouchableOpacity onPress={() => {
                    // if (item.documentType === 'PD') {
                    //     let netWork = item.documentAccessPath;
                    if (item.documentType === "TD" || item.documentType === 'PD') {
                        this.props.navigation.navigate("DocumentLibraryView", {
                            dataItem:item,
                            // 传递回调函数
                            refresh: this.callBackFunction
                        })
                    }
                    else {
                        this.props.navigation.navigate("VideoLibraryView",
                            {
                                dataItem:item,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }
                }}>
                    <View style={{ display: 'flex', flexDirection: 'row', marginLeft: 10, marginRight: 10, }}>
                        {
                            item.coursePhoto?
                            <Image source={{ uri:constants.image_addr + '/' + item.coursePhoto }} style={{ width: 80, height: 57, borderRadius:10,marginTop: 10}}></Image>
                            :
                            <View >
                                <View style={{position:'absolute',zIndex:10,width: 80, height: 57,marginTop: 10,alignItems:"center",justifyContent:"center"}}>
                                    <Text style={{fontSize:14,color:"white"}}>{item.courseName}</Text>
                                </View>
                                <Image style={{ width: 80, height: 57, borderRadius:10,marginTop: 10}} source={require('../../assets/image/defaultCover.png')}></Image>
                            </View>
                        }
                        <View style={{ flexDirection: 'column', paddingTop: 1, marginLeft: 14, marginRight: 6 }}>
                            <View style={[styles.titleViewStyle, { marginTop: 10, }]}>
                                <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16, fontWeight: '400', width: screenWidth - 150 }} numberOfLines={1}>{item.documentName}</Text>
                            </View>
                            <View style={[styles.titleViewStyle, { marginTop: 5, marginBottom: 10 }]}>
                                <View style={{ flexDirection: 'row', marginTop: 5, paddingLeft: 9, paddingTop: 3, paddingBottom: 3, paddingRight: 12, backgroundColor: 'rgba(236, 238, 242, 1)', borderRadius: 12 }}>
                                    <Text style={styles.itemContentStyle, { fontSize: 12, color: 'rgba(64, 73, 86, 1)' }} >{item.courseName}</Text>
                                </View>
                                <View style={{ flexDirection: 'row', marginLeft: 10, marginTop: 5, paddingLeft: 9, paddingTop: 3, paddingBottom: 3, paddingRight: 12, backgroundColor: 'rgba(30, 110, 250, 0.20)', borderRadius: 12 }}>
                                    <Text style={styles.itemContentStyle, { fontSize: 12, color: 'rgba(37, 91, 218, 1)' }}>{item.courseTypeName}</Text>
                                </View>
                            </View>
                        </View>
                    </View >
                </TouchableOpacity >
                <View style={styles.lineViewStyle}></View>
            </View >
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View />
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }


    // 渲染砖型底部滚动数据
    open2ColumnSelect() {
        if (!this.state.courseDataSource || this.state.courseDataSource.length < 1) {
            WToast.show({ data: "请先添加课程" });
            return
        }
        this.refs.SelectCourse.showBrickType(this.state.selectCourse, this.state.courseDataSource)
    }

    callBackSelect2ColumnValue(value) {
        console.log("==========选择结果：", value)
        if (!value) {
            return;
        }
        console.log("==========选择结果：", value[0], value[1])
        this.setState({
            "selectCourse": value,
            "selCourseLevelCode": "all",
            "selcourseTypeName": null,
            "selcourseTypeId": 0,
            "courseName": value[1] === "—" ? value[0] : value[1],
        })
        if (value.length == 2) {
            if (value[1] === "—") {
                let courseLevelItem = this.state.courseLevelDataSource.filter(item => item.courseLevelName === value[0])[0];
                console.log("==========courseLevel", courseLevelItem);
                let loadUrl = "/biz/document/list";
                let loadRequest = {
                    "currentPage": 1,
                    "pageSize": this.state.pageSize,
                    "courseLevelId": courseLevelItem.courseLevelCode,
                    "userId": constants.loginUser.userId,
                    "documentTypeList": [this.state.selType]
                };
                httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
            }
            else {
                let loadTypeUrl = "/biz/course/join/task/get_course_by_level_course_name";
                let loadRequest = {
                    "courseLevelName": value[0],
                    "courseName": value[1],
                };
                httpPost(loadTypeUrl, loadRequest, this._callBackLoadCourseData);
            }
        }
        else {
            console.log("======选择课程返回数据不合法", value)
        }
    }

    _callBackLoadCourseData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                courseName: response.data.courseName,
                courseId: response.data.courseId,
                searchKeyWord: null,
            })
            let loadUrl = "/biz/document/list";
            let loadRequest = {
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "courseId": response.data.courseId,
                "userId": constants.loginUser.userId,
                "documentTypeList": [this.state.selType]
                // "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
            this.setState({
                courseName: '',
                courseId: '',
                searchKeyWord: null,
            })
        }
    }

    renderTypeListRow = (item, index) => {
        return (
            <View key={item.id} >
                <TouchableOpacity onPress={() => {
                    let selType = item.value;
                    this.setState({
                        courseName: null,
                        courseId: null,
                        selType: selType
                    })
                    console.log('selType', selType)
                    let loadUrl = "/biz/document/list";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "searchKeyWord": this.state.searchKeyWord,
                        "userId": constants.loginUser.userId,
                        "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
                        "courseLevelId": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
                        "documentTypeList": [selType]
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.id} style={[CommonStyle.tabItemViewStyle, { width: 50 }]}>
                        <Text style={[item.value === this.state.selType ? [CommonStyle.selectedtabItemTextStyle, { fontSize: 18, alignItems: 'center' }] : [CommonStyle.tabItemTextStyle, { fontSize: 16 }]]}>
                            {item.name}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    renderCourseLevelRow = (item, index) => {
        return (
            <View key={item.courseLevelCode} >
                <TouchableOpacity onPress={() => {
                    let selCourseLevelCode = item.courseLevelCode;
                    this.setState({
                        "selCourseLevelCode": selCourseLevelCode,
                        "courseName": null
                    })

                    let loadUrl = "/biz/document/list";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "userId": constants.loginUser.userId,
                        "courseLevelId": selCourseLevelCode === "all" ? null : selCourseLevelCode,
                        "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
                        "documentTypeList": [this.state.selType]
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    console.log("选择的课程职级=====" + selCourseLevelCode)
                }}>
                    <View key={item.courseLevelCode} style={[CommonStyle.tabItemViewStyle, { width: 40, }]}>
                        <Text style={[item.courseLevelCode === this.state.selCourseLevelCode ? [CommonStyle.selectedtabItemTextStyle] : [CommonStyle.tabItemTextStyle]]}>
                            {item.courseLevelName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }
    // 课程类型
    renderCourseTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                console.log("object" + item.courseTypeId)
                this.setState({
                    selcourseTypeId: item.courseTypeId,
                    selcourseTypeName: item.courseTypeName
                })
            }}>
                <View key={"department_" + item.courseTypeId} style={[item.courseTypeId === this.state.selcourseTypeId ?
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                CommonStyle.choseToSearchItemsViewSize
                ]}>
                    <Text style={[item.courseTypeId === this.state.selcourseTypeId ?
                        CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.courseTypeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 显示搜索项目
    showCourseTypeSearchItemSelect() {
        if (!this.state.courseTypeDataSource || this.state.courseTypeDataSource.length < 1) {
            WToast.show({ data: "请先添加课程类型" });
            return
        }
        this.setState({
            showCourseTypeSearchItemBlock: true,
        })
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='我的资源'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <TouchableOpacity onPress={() => this.open2ColumnSelect()} style={[CommonStyle.rightTop50FloatingBlockView,
                {
                    height: 32,
                    width: 110,
                    opacity: 0.6,
                    borderRadius: 8,
                    backgroundColor: "rgba(242, 245, 252, 1)",
                    marginTop: 40
                }]}>
                    <Text style={{ color: 'rgba(0,10,32,0.85)', fontSize: 14 }}>
                        {!this.state.courseName ? "课程" : this.state.courseName}
                    </Text>
                </TouchableOpacity>

                <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.typeList && this.state.typeList.length > 0)
                                ?
                                this.state.typeList.map((item, index) => {
                                    return this.renderTypeListRow(item)
                                })
                                : <View />
                        }
                        <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                            <TouchableOpacity onPress={() => this.showCourseTypeSearchItemSelect()}>
                                {
                                    this.state.showCourseTypeSearchItemBlock ?
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                                {this.state.selcourseTypeId && this.state.selcourseTypeName ? (this.state.selcourseTypeName) : "课程类型"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchClosedIconSize]} source={require('../../assets/icon/iconfont/arrow-up.png')}></Image>
                                        </View>
                                        :
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                                {this.state.selcourseTypeId && this.state.selcourseTypeName ? (this.state.selcourseTypeName) : "课程类型"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchOpenedIconSize]} source={require('../../assets/icon/iconfont/arrow_down.png')}></Image>
                                        </View>
                                }
                            </TouchableOpacity>
                        </View>

                    </View>
                </View>
                <View>


                    {
                        this.state.showCourseTypeSearchItemBlock ?
                            <View style={[CommonStyle.choseToSearchBigBoxViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                                <View style={CommonStyle.heightLimited}>
                                    <ScrollView>
                                        <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                            <View style={[{ backgroundColor: 'rgba(255,255,255,1)' }, CommonStyle.choseToSearchItemsViewSize]}>
                                                <Text style={{ fontSize: 16, fontWeight: 'bold' }}>课程类型：</Text>
                                            </View>
                                            {
                                                (this.state.courseTypeDataSource && this.state.courseTypeDataSource.length > 0)
                                                    ?
                                                    this.state.courseTypeDataSource.map((item, index) => {
                                                        return this.renderCourseTypeRow(item)
                                                    })
                                                    : null
                                            }
                                        </View>
                                    </ScrollView>
                                </View>
                                <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showCourseTypeSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        let loadUrl = "/biz/document/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "userId": constants.loginUser.userId,
                                            "courseLevelId": this.state.selCourseLevelCode === 'all' ? null : this.state.selCourseLevelCode,
                                            "courseTypeId": this.state.selcourseTypeId === 0 ? null : this.state.selcourseTypeId,
                                            "documentTypeList": [this.state.selType]
                                        };
                                        console.log("选择的课程类型=====" + this.state.selcourseTypeId)
                                        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                        this.setState({
                                            showCourseTypeSearchItemBlock: false,
                                            courseName: null
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            null
                    }


                    <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                        <FlatList
                            data={this.state.dataSource}
                            renderItem={({ item, index }) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                    tintColor="#FF0000"
                                    title="loading"
                                    colors={['#FF0000', '#00FF00', '#0000FF']}
                                    progressBackgroundColor="#FFFF00"
                                    refreshing={this.state.refreshing}
                                    onRefresh={() => {
                                        this._loadFreshData()
                                    }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={() => this.flatListFooterComponent()}
                            onEndReached={() => this._loadNextData()}
                        />
                    </View>
                </View>
                <BottomScrollSelect
                    ref={'SelectCourse'}
                    callBackBrickTypeValue={this.callBackSelect2ColumnValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    lineViewStyle: {
        height: 1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 12,
        marginBottom: 11,
        borderBottomWidth: 0.5,
        borderColor: '#E8E9EC'
    },
});
import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;

export default class CollegPositionQuery extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            searchKeyWord: "",
            topBlockLayoutHeight: 0,
            positionTypeChooseDataSource:[],
            selPositionType:"all"
        }
        console.log("屏幕宽度===========",screenWidth)
    }


    UNSAFE_componentWillMount(){
        // console.log("屏幕宽度===========",screenWidth)
        console.log('componentWillMount');
        let positionTypeChooseDataSource = [
            {
                positionType:'all',
                positionTypeName:'全部',
            },
            {
                positionType:"F",
                positionTypeName:"全职",
            },
            {
                positionType:"P",
                positionTypeName:"兼职",
            },
            {
                positionType:"I",
                positionTypeName:"实习"
            }

        ]
        this.setState({
            positionTypeChooseDataSource:positionTypeChooseDataSource,
        })
        const { route, navigation } = this.props;
        this.loadPositionList();
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/hiring/position/tenantAllPositionList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "staffId":constants.loginUser.staffId,
            "searchKeyWordHiringPosition":this.state.searchKeyWord,
            "positionState":"0AA",
            "positionType":this.state.selPositionType === "all" ? null : this.state.selPositionType
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
        
    }

    searchByKeyWord = () => {
        let url = "/biz/hiring/position/tenantAllPositionList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "staffId":constants.loginUser.staffId,
            "searchKeyWordHiringPosition":this.state.searchKeyWord,
            "positionState":"0AA",
            "positionType":this.state.selPositionType === "all" ? null : this.state.selPositionType
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);        
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/hiring/position/tenantAllPositionList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "staffId":constants.loginUser.staffId,
            "searchKeyWordHiringPosition":this.state.searchKeyWord,
            "positionState":"0AA",
            "positionType":this.state.selPositionType === "all" ? null : this.state.selPositionType
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

     // 上拉触底加载下一页
     _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            console.log("=====")
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadPositionList();
    }

    loadPositionList=()=>{
        let url = "/biz/hiring/position/tenantAllPositionList";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "staffId":constants.loginUser.staffId,
            "searchKeyWordHiringPosition":this.state.searchKeyWord,
            "positionState":"0AA",
            "positionType":this.state.selPositionType === "all" ? null : this.state.selPositionType
        };
        httpPost(url, loadRequest, this.loadPositionListCallBack);
    }

    loadPositionListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];


            dataAll.forEach(item=>{
                console.log(item)
            })


            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    collectionResume=(item)=>{
        console.log("=========item========",item);
        let loadUrl = "/biz/collection/position/add";
        let loadRequest = {
            "positionId":item.positionId,
            "staffId":constants.loginUser.staffId,
            "collectionPosition":item.collectionPosition == 'Y'? "N" : "Y",
        };
        httpPost(loadUrl, loadRequest, this.collectionResumeCallBack);
    }

    collectionResumeCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.callBackFunction();
            WToast.show({ data: response.data.collectionPosition == 'Y'?"加入收藏成功":"取消收藏成功" });

        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }

    }
    applyInterview=(item)=>{
        let url = "/biz/collection/position/add";
        let loadRequest = {
            "staffId":constants.loginUser.staffId,
            "positionId":item.positionId,
            "applyPosition":item.applyState == 'Y'?"N":"Y"
        };
        httpPost(url, loadRequest, this.applyInterviewCallBack);
    }

    applyInterviewCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({ data: response.data.applyPosition == 'Y'?"申请面试成功":"已取消申请" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow=(item)=>{
        return (
            <View key={item.dailyId} style={{ borderColor: '#F2F5FC', borderBottomWidth: 7, borderTopWidth: 8 }}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>企业名称：{item.enterpriseName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>招聘岗位：{item.positionName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>岗位类型：{item.positionTypeName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>工作地点：{item.workingPlace ? item.workingPlace : "暂无信息"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,{fontWeight:'bold'}]}>薪资待遇</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle]}>{item.positionTreatment ? item.positionTreatment : "暂无信息"}</Text>
                </View>
                {
                item.applyState == 'N' ?
                null
                :
                    <View style={styles.titleViewStyle}>
                        <Text style={{fontSize:14,color:'#FF8C28'}}>已申请面试</Text>
                    </View>
                }
                <View style={{width: 40, height: 40, 
                    backgroundColor: 'rgba(255,0,0,0.0)', 
                    position:'absolute', 
                    alignItems:'center',
                    justifyContent:'center',
                    right: 10,
                    top:5,
                    }}>
                    <TouchableOpacity onPress={()=>{
                        this.collectionResume(item)
                    }}>
                    {
                        item.collectionPosition == 'Y' ? 
                        <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/start_full.png')}></Image>
                        :
                        <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/start.png')}></Image>
                    }
                    </TouchableOpacity>
                </View>

                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                {
                    constants.loginUser.staffId != null ?
                    <View style={CommonStyle.itemBottomBtnStyle}>
                        <TouchableOpacity onPress={()=>{this.applyInterview(item)}}>
                            {
                                item.applyState == 'Y' ?
                                <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle,{borderColor:'rgba(145, 147, 152, 0.5)',flexDirection:"row"}]}>
                                    {/* <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/closeGrey.png')}></Image> */}
                                    <Text style={[{ color: 'rgba(145, 147, 152, 0.5)', fontSize: 14, lineHeight: 20 }]}>取消申请</Text>
                                </View>
                                :
                                <View style={[{
                                    width: 80,
                                    height: 28,
                                    flexDirection: "row",
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    margin: 10,
                                    marginRight: 0,
                                    borderColor: '#FF8C28',
                                    borderWidth: 0.85,
                                    borderRadius: 6
                                }]}>
                                    {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                    <Text style={[{ color: '#FF8C28', fontSize: 14, lineHeight: 20 }]}>申请面试</Text>
                                </View>    
                            }
                        </TouchableOpacity>
                    </View>
                    :
                    <View/>
                }
                    <TouchableOpacity onPress={()=>{this.props.navigation.navigate("EnterprisecrHiringPositionDetail", 
                        {
                            // 传递回调函数
                            positionId: item.positionId,
                            enterpriseId: item.enterpriseId,
                            refresh: this.callBackFunction,
                            
                        })}}>
                        <View style={[{
                            width: 80,
                            height: 28,
                            flexDirection: "row",
                            justifyContent: 'center',
                            alignItems: 'center',
                            margin: 10,
                            marginRight: 10,
                            borderColor: 'rgba(27, 188, 130, 1)',
                            borderWidth: 0.85,
                            borderRadius: 6
                        }]}>
                            <Image style={{ width: 24, height: 24, marginRight: 0.5, marginLeft: 3 }} source={require('../../assets/icon/iconfont/detailGreen.png')}></Image>
                            <Text style={[{ color: 'rgba(27, 188, 130, 1)', fontSize: 14, lineHeight: 20 }]}>详情</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                
                
            </View>
        )
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    positionTypeChooseStateRow=(item, index)=>{
        return (
            <View key={item.positionType} >
                <TouchableOpacity onPress={()=>{
                    var selPositionType = item.positionType;
                    this.setState({
                        selPositionType:selPositionType
                    })

                    let url= "/biz/hiring/position/tenantAllPositionList";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "staffId":constants.loginUser.staffId,
                        "searchKeyWordHiringPosition":this.state.searchKeyWord,  
                        "positionState":"0AA",          
                        "positionType":selPositionType === "all" ? null : selPositionType
                    };
                    // console.log("selYearsChooseName+1:"+ this.addOneYear(selYearsChooseName))
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    {/* 宽度调整 width:80=>width:70*/}
                    <View key={item.positionType} style={[item.positionType===this.state.selPositionType ? 
                        [styles.selectedBlockItemViewStyle]
                        :
                        [styles.blockItemViewStyle] ]}>
                        <Text style={[item.positionType===this.state.selPositionType ? 
                            [{ color: "rgba(0, 10, 2, 0.8)", fontSize: 16, textAlign: 'center' }]
                            :
                            [{ color: "rgba(0, 10, 2, 0.45)", fontSize: 14, textAlign: 'center' }],
                            { fontWeight: 'bold' }
                        ]}>
                            {item.positionTypeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='职位搜索'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle, { marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }]} 
                    onLayout={this.topBlockLayout.bind(this)}>
                     <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.positionTypeChooseDataSource && this.state.positionTypeChooseDataSource.length > 0)
                                ?
                                this.state.positionTypeChooseDataSource.map((item, index) => {
                                    return this.positionTypeChooseStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                    <View style={{}}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Image  style={{width:18, height:18}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                    returnKeyLabel="搜索"
                                    onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                    }}
                                placeholder={'搜索企业/岗位'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        // justifyContent: "space-between",
        alignItems: 'center',
        width: screenWidth / 1.05,
        paddingLeft: 5,
        height: 34,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#F2F5FC",
        backgroundColor: '#F2F5FC',
        borderRadius: 15,
        margin: 0,
        marginTop: 0,
        marginLeft: 0,
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 14,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle: {
        // marginTop: 10,
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    // 分段器样式
    blockItemViewStyle: {
        margin: 5,
        width: 45, 
        borderRadius: 0,
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
        // marginTop: 0, 
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        width: 45, borderRadius: 0, 
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: "#FFFFFF", 
        // marginTop: 0, 
    },    
    
});
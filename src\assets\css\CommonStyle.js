'use strict';

var React = require('react-native');
const { fullScreenIfIphoneXContentViewHeight, ifIphoneXContentViewHeight, ifIphoneXBodyViewHeight, isIphoneX, ifIphoneXHeaderHeight } = require('../../utils/ScreenUtil');
var {
    StyleSheet, Dimensions, Platform
} = React;
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 70;

module.exports = StyleSheet.create({

    // 带滚动页面的调试
    fullScreenContentViewStyle: {
        // height:Platform.OS === 'ios' ? screenHeight - 110 : screenHeight - 140,
        backgroundColor: '#F5F5F5',
        height: fullScreenIfIphoneXContentViewHeight(),
    },
    // 带滚动页面的调试
    contentViewStyle: {
        // height:Platform.OS === 'ios' ? screenHeight - 110 : screenHeight - 140,
        backgroundColor: '#FFFFFF',
        height: ifIphoneXContentViewHeight(),
    },
    // 表单页面整体内容样式
    formContentViewStyle: {
        // height:Platform.OS === 'ios' ? screenHeight - 110 : screenHeight - 140,
        backgroundColor: '#FFFFFF',
        height: ifIphoneXContentViewHeight() + ifIphoneXHeaderHeight(),
        marginBottom: 10
    },
    // 带滚动页面的调试
    onlyContainHeaderContentViewStyle: {
        // height:Platform.OS === 'ios' ? screenHeight - 110 : screenHeight - 140,
        backgroundColor: '#FFFFFF',
        height: ifIphoneXBodyViewHeight(),
    },

    // modal全屏遮挡
    fullScreenKeepOut: {
        height: screenHeight,
        width: screenWidth,
        backgroundColor: 'rgba(169,169,169,0.95)',
        alignItems: 'center',
        justifyContent: 'center',
    },
    // modal全屏遮挡 -- 内容样式
    modalContentViewStyle: {
        height: ifIphoneXContentViewHeight(),
        width: screenWidth - 30,
        backgroundColor: '#FFFFFF',
        padding: 10,
        borderRadius: 5
    },
    modalSearchInputText: {
        width: screenWidth - 200,
        borderColor: '#000000',
        borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 18,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0
    },
    modalSearchBtnViewStyle: {
        fontSize: 16,
        width: 100,
        height: 30,
        borderWidth: 1,
        borderColor: '#A0A0A0',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        borderRadius: 4
    },
    modalSearchBtnTextStyle: {
        color: '#000000',
        fontSize: 16
    },


    // 主体文字大小
    bodyViewStyle: {
        padding: 5,
    },
    // 主体文字大小
    bodyTextStyle: {
        fontSize: 16,
        flexWrap: 'wrap',
        alignItems: 'center',
    },
    fontSize18: {
        fontSize: 18
    },
    boldTextStyle: {
        fontWeight: 'bold'
    },
    alignCenterStyle: {
        alignSelf: 'center',
        alignItems: 'center',
        alignContent: 'center',
    },

    // 隐藏-无用
    hiddenViewStyle: {
        width: 0,
        height: 0,
        display: 'none'
    },
    // 置灰，不可用
    disableViewStyle: {
        opacity: 0.3,
    },

    rightTop50FloatingBlockView: {
        width: 50,
        height: 50,
        zIndex: 100,
        backgroundColor: 'green',
        position: 'absolute',
        right: 15,
        top: isIphoneX() ? 75 : 50,
        borderRadius: 50,
        opacity: 0.6,
        alignItems: 'center',
        justifyContent: 'center'
    },
    rightTop50FloatingBlockText: {
        color: '#FFF',
    },
    rightTop50SecondFloatingBlockView: {
        width: 50,
        height: 50,
        zIndex: 100,
        backgroundColor: 'green',
        position: 'absolute',
        right: 15,
        top: isIphoneX() ? 127 : 102,
        borderRadius: 50,
        opacity: 0.6,
        alignItems: 'center',
        justifyContent: 'center'
    },
    rightTop50ThirdFloatingBlockView: {
        width: 50,
        height: 50,
        zIndex: 100,
        backgroundColor: 'green',
        position: 'absolute',
        right: 15,
        top: isIphoneX() ? 180 : 155,
        borderRadius: 50,
        opacity: 0.6,
        alignItems: 'center',
        justifyContent: 'center'
    },

    // 列表项按钮样式
    itemBottomBtnStyle: {
        flexDirection: 'row',
        justifyContent: 'flex-end'
    },
    // 置闲
    itemBottomFreeBtnViewStyle: {
        fontSize: 16,
        width: 100,
        height: 30,
        borderWidth: 0,
        borderColor: '#A0A0A0',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        backgroundColor: "green",
        borderRadius: 4
    },
    itemBottomFreeBtnTextStyle: {
        color: '#FFFFFF',
        fontSize: 16
    },

    // 故障
    itemBottomBreakdownBtnViewStyle: {
        fontSize: 16,
        width: 100,
        height: 30,
        borderWidth: 1,
        borderColor: '#A0A0A0',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        backgroundColor: "yellow",
        borderRadius: 4
    },
    itemBottomBreakdownBtnTextStyle: {
        color: '#000000',
        fontSize: 16
    },
    // 查看详情
    itemBottomDetailBtnViewStyle: {
        fontSize: 16,
        width: 100,
        height: 30,
        borderWidth: 0,
        borderColor: '#A0A0A0',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        backgroundColor: "green",
        borderRadius: 6
    },
    itemBottomDetailBtnTextStyle: {
        color: '#FFFFFF',
        fontSize: 16
    },
    itemBottomDeleteBtnViewStyle: {
        fontSize: 16,
        width: 100,
        height: 30,
        borderWidth: 1,
        borderColor: '#A0A0A0',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        borderRadius: 6
    },
    itemBottomDeleteGreyBtnViewStyle: {
        fontSize: 16,
        width: 80,
        height: 28,
        flexDirection: "row",
        justifyContent: 'center',
        borderWidth: 0.85,
        alignItems: 'center',
        margin: 10,
        marginRight: 0,
        borderRadius: 6,
        borderColor: 'rgba(145, 147, 152, 1)',
        // flexWrap: 'wrap'
    },
    itemBottomDeleteBtnTextStyle: {
        color: '#000000',
        fontSize: 16
    },
    itemBottomEditBtnViewStyle: {
        fontSize: 222,
        width: 100,
        height: 30,
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        backgroundColor: "#CB4139",
        borderRadius: 4
    },
    itemBottomEditBlueBtnViewStyle: {
        width: 80,
        height: 28,
        flexDirection: "row",
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        marginRight: 0,
        backgroundColor: "#1E6EFA",
        borderRadius: 6
    },
    itemAgreeBtnViewStyle: {
        fontSize: 222,
        width: 100,
        height: 30,
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        backgroundColor: "#ffffff",
        borderRadius: 4,
        borderWidth: 1,
        borderColor: "#3ab240"
    },
    itemRejectBtnViewStyle: {
        fontSize: 222,
        width: 100,
        height: 30,
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        backgroundColor: "#ffffff",
        borderRadius: 4,
        borderWidth: 1,
        borderColor: "#CB4139"
    },

    itemBottomEditBtnTextStyle: {
        color: '#F0F0F0',
        fontSize: 16
    },


    naicaiIndexHeadLeftText: {
        color: '#000000',
        fontSize: 16,
    },
    // 导航左侧字体样式
    headLeftText: {
        color: '#FFFFFF',
        fontSize: 16,
    },
    // 导航右侧字体样式
    // headRightText:{
    //     color:'#FFFFFF',
    //     fontSize:16,
    // },
    headRightText: {
        // color:'rgb(180,180,180)',
        color: '#33333375',
        fontSize: 15,
    },

    // 一行放两个按钮【取消、保存】
    btnRowStyle: {
        flexDirection: 'row',
        margin: 10,
        justifyContent: 'space-between',
    },
    btnRowLeftCancelBtnView: {
        backgroundColor: '#FFFFFF',
        // justifyContent:'flex-end',
        // alignContent:'flex-end',
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#A0A0A0',
        borderRadius: 50,
        flexDirection: 'row',
        width: 170, height: 45,
        marginLeft: 5,
        marginTop: 15
    },
    btnRowLeftCancelBtnText: {
        fontSize: 18,
        color: '#000A20'
    },
    btnRowRightSaveBtnView: {
        backgroundColor: '#1E6EFA',
        alignItems: 'center',
        // alignContent:'center',
        justifyContent: 'center',
        borderRadius: 50,
        flexDirection: 'row',
        width: 170, height: 45,
        marginRight: 5,
        marginTop: 15
    },
    image: {
        width: 25,
        height: 25,
        marginRight: 15,
    },
    btnRowRightSaveBtnText: {
        fontSize: 18,
        color: '#FFFFFF'
    },

    // 分隔样式，垂直居中靠左对齐，占整行
    addItemSplitRowView: {
        width: screenWidth,
        height: 50,
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
        backgroundColor: '#F6F9FA',
        marginTop: 10
    },
    addItemSplitRowViewDetail: {
        width: screenWidth,
        height: 30,
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
        // backgroundColor: '#F6F9FA',
        marginTop: 10
    },
    addItemSplitRowTextDetail: {
        fontWeight: 'bold',
        color: 'rgba(0, 10, 32, 0.85)',
        fontSize: 16,
        marginLeft: 8,
        lineHeight: 20
    },
    addItemSplitRowText: {
        color: '#999999',
        fontSize: 20,
        paddingLeft: 10
    },

    blockSplitViewStyle: {
        backgroundColor: '#F6F9FA',
        height: 20
    },

    rowSplitViewStyle: {
        borderBottomWidth: 1, borderBottomColor: '#D9D9D9', height: 0, marginTop: 5
    },

    // 分段器样式
    blockItemViewStyle: {
        margin: 5,
        paddingTop: 5, paddingBottom: 5,
        paddingLeft: 10, paddingRight: 10,
        // height:35,
        justifyContent: 'center',
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        paddingTop: 5, paddingBottom: 5,
        paddingLeft: 10, paddingRight: 10,
        // height:35,
        justifyContent: 'center',
        borderRadius: 2,
        backgroundColor: "#1E6EFA"
    },
    blockItemTextStyle: {
        color: '#000000',
        fontSize: 18
    },
    selectedBlockItemTextStyle: {
        color: '#FFFFFF',
        fontSize: 18
    },
    blockItemTextStyle16: {
        color: '#000000',
        fontSize: 15
    },
    selectedBlockItemTextStyle16: {
        color: '#FFFFFF',
        fontSize: 15
    },

    // 添加页面-占整行
    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    rowLabTextStyle: {
        fontSize: 18,
    },
    rowLabLeftView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    rowLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    rowRightTextInput: {
        width: screenWidth - (leftLabWidth + 15),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        height: 45,
        paddingLeft: 10,
        paddingRight: 10
    },
    // 假装是输入框的Text样式
    inputTextStyleTextStyle: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    },
    // 假装是输入框的Text样式--不指定宽度
    inputTextStyleTextStyleNoWidth: {
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    },
    // 输入框占整行
    inputRowText: {
        width: screenWidth - 10,
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        marginLeft: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },

    // 搜索框
    // 假装是输入框的Text样式
    inputTextStyleViewStyle: {
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        paddingLeft: 10,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    },
    // 重置
    resetBtnViewStyle: {
        fontSize: 16,
        height: 30,
        borderWidth: 1,
        borderColor: '#A4A4A4',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        paddingLeft: 10,
        paddingRight: 10,
        backgroundColor: "#FFFFFF",
        borderRadius: 4
    },
    resetBtntextStyle: {
        color: '#A0A0A0',
        fontSize: 16
    },
    searchInputText: {
        width: screenWidth / 1.5,
        borderColor: '#000000',
        borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 18,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0
    },

    selectViewItem: {
        width: 100, justifyContent: 'center', alignItems: 'center'
    },
    selectTextItem: {
        fontSize: 18,
        fontWeight: 'bold'
    },

    //renderRow最外层样式
    innerViewStyle: {
        borderColor: "rgba(242, 245, 252, 1)",
        borderBottomWidth: 8,
        borderTopWidth: 8
    },
    //列表页面头部操作区最外层样式
    headViewStyle: {
        borderColor: "#ffffff",
        borderWidth: 8,
        backgroundColor: "#ffffff"
    },

    // Tab分类样式
    tabItemViewStyle: {
        margin: 5,
        width: 60,
        borderRadius: 0,
        paddingTop: 2, paddingBottom: 0,
        paddingLeft: 2, paddingRight: 2,
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
    },
    selectedtabItemTextStyle: {
        color: "rgba(0, 10, 2, 0.8)",
        fontSize: 16,
        textAlign: 'center',
        fontWeight: 'bold'
    },
    tabItemTextStyle: {
        color: "rgba(0, 10, 2, 0.45)",
        fontSize: 14,
        textAlign: 'center',
        fontWeight: 'bold'
    },
    //搜索框和导出按钮同一行时两者的样式布局
    searchBoxAndExport: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: "space-between",
        paddingLeft: 12,
        height: 34,
        paddingRight: 16
    },
    //同一行有导出按钮的搜索时间框的样式
    searchTimeBoxWithExport: {
        justifyContent: "space-between",
        flexDirection: 'row',
        alignItems: 'center',
        margin: 0,
        marginTop: 0,
        marginLeft: 0,
        width: screenWidth / 1.4,
        backgroundColor: '#F2F5FC',
        // backgroundColor: '#4169E1',
        paddingLeft: 5,
        borderWidth: 0,
        borderColor: '#F2F5FC',
        height: 34,
        borderRadius: 15
    },
    //选项搜索样式
    heightLimited: {
        maxHeight: screenWidth - 68
    },
    choseToSearchViewStyle: {
        margin: 5,
        paddingTop: 5, paddingBottom: 5,
        paddingLeft: 10, paddingRight: 10,
        // height:35,
        justifyContent: 'center',
        backgroundColor: '#ffffff',
        alignItems: 'center',
        // padding: 10, 
        // margin: 5, 
        flexDirection: 'row'
    },
    choseToSearchClosedTextStyle: {
        fontWeight: 'bold', color: "rgba(0, 10, 2, 0.45)", paddingRight: 10, fontSize: 16
    },
    choseToSearchOpenedTextStyle: {
        fontWeight: 'bold', color: "rgba(30,110,250,1)", paddingRight: 10, fontSize: 16
    },
    choseToSearchClosedIconSize: {
        width: 22, height: 22
    },
    choseToSearchOpenedIconSize: {
        width: 20, height: 20
    },
    choseToSearchBigBoxViewStyle: {
        position: 'absolute',
        backgroundColor: 'rgba(169,169,169,0.5)',
        width: screenWidth,
        zIndex: 101,
        right: 0,
        left: 0,
        // height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)
    },
    choseToSearchSmallBoxViewStyle: {
        //内边距
        paddingTop: 13, paddingBottom: 7,
        paddingLeft: 10, paddingRight: 10,
        width: screenWidth,
        flexWrap: 'wrap',
        flexDirection: 'row',
        backgroundColor: 'rgba(255,255,255,1)',
        borderBottomWidth: 1,
        borderBottomColor: "rgba(0,10,32,0.15)",
        borderTopWidth: 1,
        borderTopColor: "rgba(0,10,32,0.15)"
    },
    choseToSearchItemsViewSize: {
        //外边距
        marginLeft: 6,
        marginRight: 6,
        marginTop: 8,
        marginBottom: 0,
        //内边距
        paddingTop: 5, paddingBottom: 5,
        paddingLeft: 15, paddingRight: 15,
        borderRadius: 4,
        justifyContent: 'center',
        height: 30,
        borderRadius: 4
    },
    choseToSearchItemsSelectedViewColor: {
        backgroundColor: 'rgba(255, 255, 255, 0.10)',
        borderWidth: 1,
        borderColor: 'rgba(30, 110, 250, 1)'
    },
    choseToSearchItemsViewColor: {
        backgroundColor: "rgba(246,246,246,1)"
    },
    choseToSearchItemsSelectedTextStyle: {
        color: 'rgba(30,110,250,1)',
        fontSize: 16
    },
    choseToSearchItemsTextStyle: {
        color: 'rgba(0,10,32,0.45)',
        fontSize: 16
    },
    choseToSearchBtnRowStyle: {
        justifyContent: 'center',
        alignItems: 'center',
        // borderWidth: 9,
        borderColor: "#ffffff",
        backgroundColor: "#ffffff",
        height: 68,
        flexDirection: 'row'
    },
    choseToSearchBtnCanleViewStyle: {
        justifyContent: 'center',
        alignItems: 'center',
        width: screenWidth / 2 - 60,
        height: 40,
        marginRight: 20,
        backgroundColor: 'rgba(0,10,32,0.10)',
        borderRadius: 20
    },
    choseToSearchBtnOKViewStyle: {
        justifyContent: 'center',
        alignItems: 'center',
        width: screenWidth / 2 - 60,
        height: 40,
        // marginRight: 20, 
        backgroundColor: 'rgba(30,110,250,1)',
        borderRadius: 20
    },

    singleSearchBox: {
        width: '100%',
        // borderColor: '#F2F5FC',
        backgroundColor: '#FFFFFF',
        // backgroundColor: '#1E90FF',
        height: 34,
        flexDirection: 'row',
        paddingLeft: 12,
        paddingRight: 16
    },
    searchBoxWithoutOthers: {
        flexDirection: 'row',
        alignItems: 'center',
        margin: 0,
        marginTop: 0,
        marginLeft: 0,
        width: '100%',
        backgroundColor: '#F2F5FC',
        // backgroundColor: '#4169E1',
        paddingLeft: 5,
        borderWidth: 0,
        borderColor: '#F2F5FC',
        height: 34,
        borderRadius: 15
    },
    itemBottomStudyGreyBtnViewStyle: {
        width: 92,
        backgroundColor: '#1BBC82',
        fontSize: 16,
        height: 28,
        flexDirection: "row",
        justifyContent: 'center',
        borderWidth: 0.85,
        borderColor: '#1BBC82',
        alignItems: 'center',
        margin: 10,
        marginRight: 0,
        borderRadius: 6,
    },
    itemBottomProgressGreyBtnViewStyle: {
        width: 64,
        backgroundColor: '#1BBC82',
        fontSize: 16,
        height: 28,
        flexDirection: "row",
        justifyContent: 'center',
        borderWidth: 0.85,
        borderColor: '#1BBC82',
        alignItems: 'center',
        margin: 10,
        marginRight: 0,
        borderRadius: 6,
    }
});
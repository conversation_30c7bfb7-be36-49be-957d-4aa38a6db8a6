import React,{ Component } from 'react';
import {Text, StyleSheet, TouchableHighlight} from 'react-native';

export default class OButton extends Component {
    render() {
        return (
            <TouchableHighlight
                style={styles.button}
                underlayColor="#a5a5a5"
                onPress={this.props.onPress}>
                <Text style={styles.buttonText}>{this.props.text}</Text>
            </TouchableHighlight>
        );
    }
}

const styles = StyleSheet.create({
    button: {
        margin:10,
        padding:10,
        borderRadius:5,
        backgroundColor:'red'
    },
    buttonText: {
        fontSize: 15,
        color:'#FFFFFF'
    },
});
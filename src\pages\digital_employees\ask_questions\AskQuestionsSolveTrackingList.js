import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../../utils/CameraRollUtils';
var CommonStyle = require('../../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class AskQuestionsSolveTrackingList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            askQuestionsId:null,
            listTitleName:null,
            operate:null,
            compressFileList:[],
            urls:[],
            isShowImage: false,
            pictureIndex:0
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { askQuestionsId, listTitleName, operate } = route.params;
            if (askQuestionsId) {
                this.setState({
                    askQuestionsId:askQuestionsId
                })
            }
            if (listTitleName) {
                this.setState({
                    listTitleName:listTitleName
                })
            }

            if (operate) {
                this.setState({
                    operate:operate
                })
            }
            
            this.loadAskQuestionsSolveTrackingList(askQuestionsId);
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/ask/questions/solve/tracking/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "askQuestionsId":this.state.askQuestionsId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/ask/questions/solve/tracking/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "askQuestionsId":this.state.askQuestionsId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadTractList();
    }

    loadAskQuestionsSolveTrackingList=(askQuestionsId)=>{
        let url= "/biz/ask/questions/solve/tracking/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "askQuestionsId":askQuestionsId ? askQuestionsId : this.state.askQuestionsId,
        };
        httpPost(url, loadRequest, this.loadAskQuestionsSolveTrackingListCallBack);
    }

    loadAskQuestionsSolveTrackingListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteAskQuestionsSolveTracking =(solveTrackingId)=> {
        console.log("=======delete=solveTrackingId", solveTrackingId);
        let url= "/biz/ask/questions/solve/tracking/delete";
        let requestParams={'solveTrackingId':solveTrackingId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.solveTrackingId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text selectable={true} style={styles.titleTextStyle}>{item.solveTrackingContent}</Text>
                </View>

                {
                    item.compressFileList && item.compressFileList.length > 0 ?
                    (
                        <View>
                            {
                                item.pictureDisplay === "N"?
                                    <View style={[styles.titleViewStyle, { justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                                        <Text style={styles.titleTextStyle}>附件：</Text>
                                        <TouchableOpacity onPress={() => {
                                            var urls = [];
                                            if(item.compressFileList && item.compressFileList.length > 0){
                                                for(var i=0;i<item.compressFileList.length;i++){
                                                    var url = {
                                                        url:constants.image_addr + '/' +  item.compressFileList[i].compressFile
                                                    }
                                                    urls=urls.concat(url)
                                                    console.log(url)
                                                }
                                            }
                                            this.setState({
                                                urls:urls
                                            })
                                            let list = this.state.dataSource;
                                            list.map((elem, index) => {
                                                if(elem.solveTrackingId == item.solveTrackingId){
                                                    elem.pictureDisplay = "Y"
                                                }
                                            })
                                            this.setState({
                                                dataSource:list
                                            })
                                            // console.log("==============",list)
                                        }}>
                                                <Text style={[styles.titleTextStyle,{color:"#CB4139"}]}>点击展开</Text>
                                        </TouchableOpacity>
                                    </View>
                                :
                                <View>
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>附件：</Text>
                                    </View>
                                    <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                        {
                                            item.compressFileList.map((item,index) =>{
                                            return(
                                                <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex'}]}>

                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        isShowImage:true,
                                                        pictureIndex:index
                                                    })
                                                    // uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                                    //     console.log("========imageUploadResponse", imageUploadResponse)
                                                    //     if (imageUploadResponse.code === 200) {
                                                    //         WToast.show({ data: "上传成功" });
                                                    //         let compressFileList = imageUploadResponse.data
                                                    //         this.setState({
                                                    //             compressFileList: compressFileList
                                                    //         })
                                                    //     }
                                                    //     else {
                                                    //         WToast.show({ data: imageUploadResponse.message });
                                                    //     }
                                                    // });

                                                }}>
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />
                                                </TouchableOpacity>
                                                <Modal visible={this.state.isShowImage} transparent={true}>
                                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} index={this.state.pictureIndex}
                                                    enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                    onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls}
                                                    onSave={()=>{
                                                        saveImage( this.state.urls[this.state.pictureIndex].url)
                                                    }}/>
                                                </Modal>
                                            </View>
                                            )
                                            })
                                        }
                                    </View>
                                    <View style={[styles.titleViewStyle,{justifyContent:'center'}]}>
                                        {
                                            item.pictureDisplay === "Y"?
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    urls:[]
                                                })
                                                let list = this.state.dataSource;
                                                list.map((elem, index) => {
                                                    if(elem.solveTrackingId == item.solveTrackingId){
                                                        elem.pictureDisplay = "N"
                                                    }
                                                })
                                                this.setState({
                                                    dataSource:list
                                                })
                                                // console.log("==============",list)
                                            }}>
                                                    <Text style={[styles.titleTextStyle,{color:"#CB4139",textAlign:'center'}]}>点击收起</Text>
                                            </TouchableOpacity>
                                            :
                                            <View/>
                                        }
                                    </View>
                                </View>

                            }

                        </View>
                    ):
                    null
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>提交人：{item.userName}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>提交时间：{item.gmtCreated}</Text>
                </View>

                {
                    this.state.operate === 'query' ?
                    null
                :
                <View>
                    {
                        item.userId ==constants.loginUser.userId ?
                        <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                            <TouchableOpacity onPress={()=>{
                                Alert.alert('确认','您确定要删除该条记录吗？',[
                                    {
                                        text:"取消", onPress:()=>{
                                        WToast.show({data:'点击了取消'});
                                        // this在这里可用，传到方法里还有问题
                                        // this.props.navigation.goBack();
                                        }
                                    },
                                    {
                                        text:"确定", onPress:()=>{
                                            WToast.show({data:'点击了确定'});
                                            this.deleteAskQuestionsSolveTracking(item.solveTrackingId)
                                        }
                                    }
                                ]);
                            }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,,{width:80,flexDirection:"row"}
                                ]}>
                                    <Image  style={{width:20, height:20,marginRight:5}} source={require('../../../assets/icon/iconfont/delete.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => {
                                this.props.navigation.navigate("AskQuestionsSolveTrackingAdd",
                                    {
                                        // 传递参数
                                        solveTrackingId: item.solveTrackingId,
                                        // 传递回调函数
                                        refresh: this.callBackFunction
                                    })
                            }}>
                                <View style={[CommonStyle.itemBottomEditBtnViewStyle, { width: 80, flexDirection: "row" }
                                ]}>
                                    <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../../assets/icon/iconfont/edit.png')}></Image>
                                    <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        :
                        null
                    }
                </View>
                }
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image style={{ width: 22, height: 22}} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        if (this.state.operate === 'query') {
            return (
                <View/>
            )
        }
        else {
            return (
                <TouchableOpacity onPress={() => {
                    if (!this.state.askQuestionsId) {
                        WToast.show({dataAll:"数据错误，缺少参数-askQuestionsId"})
                        return;
                    }
                    this.props.navigation.navigate("AskQuestionsSolveTrackingAdd", 
                    {
                        askQuestionsId:this.state.askQuestionsId,
                        listTitleName:this.state.listTitleName,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })
                }}>
                <Image style={{ width:27, height:27 }} source={require('../../../assets/icon/iconfont/add.png')}></Image>
                    {/* <Text style={CommonStyle.headRightText}>新增进展</Text> */}
                </TouchableOpacity>
            )
        }
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.listTitleName}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.contentViewStyle,{ height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});
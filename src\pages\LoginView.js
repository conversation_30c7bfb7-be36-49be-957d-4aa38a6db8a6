import React from 'react';
import {
    Text,
    Image,
    View,
    TouchableWithoutFeedback,
    TextInput,
    StyleSheet,
    Alert,
    Dimensions, StatusBar,
    ScrollView, TouchableOpacity, KeyboardAvoidingView
} from 'react-native';
import AsyncStorage from '@react-native-community/async-storage';
import CheckBox from 'react-native-check-box'
import LinearGradinet from 'react-native-linear-gradient';
import { WToast } from 'react-native-smart-tip';
// import DeviceInfo from 'react-native-device-info';

var CommonStyle = require('../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class LoginView extends React.Component {
    static navigationOptions = ({ navigation }) => ({
        header: null,
        title: 'Home',
    })

    constructor(props) {
        super(props);
        this.state = {
            // 登录按钮是否可用
            loginBtnDisabled: true,
            imageState: false,
            userAgreeIsChecked:false,
            // userCode:"test",
            // userPwd:"1234567",
            userCode:"",
            userPwd:"",
            testDouble:null,
            appFirstStartPopup:null,
            tenantAbbreviation:"登录界面",
            tenantLogo:"http://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/jzxs_logo.png",
            tenantLoginBackground:"#303F58",
        }
        // 上次点击登录按钮时间
        this.lastClickTime = 0

        // 本地储存--- 删除
        AsyncStorage.removeItem('userAgreeIsChecked', (error) => {
            if (error) {
                console.log("===AsyncStorage===删除本地储存失败==userAgreeIsChecked", error);
            }
        });
    }

    static navigationOptions = {
        title: 'DemoTitle',
        headerStyle: {
            backgroundColor: 'green',

        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
            fontWeight: 'normal',
            fontSize: 20
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        // 读本地储存, 租户简称
        // AsyncStorage.getItem("tenantAbbreviation", (err, result) => {
        //     if (err) {
        //         console.log('===AsyncStorage==getItem==tenantAbbreviation error' + err);
        //         return;
        //     }
        //     if (result != null) {
        //         // console.log('===AsyncStorage==getItem==tenantAbbreviation' + result);
        //         this.setState({
        //             tenantAbbreviation: result,
        //         })
        //     }
        //     return result;
        // });

        AsyncStorage.getItem("tenantExtInfo", (err, result) => {
            if (err) {
                console.log('===AsyncStorage==getItem==tenantExtInfo error' + err);
                return;
            }
            if (result != null) {
                let tenantExtInfo = JSON.parse(result);
                // 租户简称
                if (tenantExtInfo.tenantAbbreviation && tenantExtInfo.tenantAbbreviation.length > 0) {
                    this.setState({
                        tenantAbbreviation: tenantExtInfo.tenantAbbreviation,
                    })
                }
                if (tenantExtInfo.tenantLogo) {
                    this.setState({
                        tenantLogo: tenantExtInfo.tenantLogo,
                    })
                }
                if (tenantExtInfo.tenantLoginBackground) {
                    this.setState({
                        tenantLoginBackground: tenantExtInfo.tenantLoginBackground,
                    })
                }
            }
            return result;
        });
        // 读本地储存, 查看是否同意过隐私政策
        AsyncStorage.getItem("clientInfoStorage", (err, result) => {
            if (err) {
                console.log('AsyncStorage.getItem error' + err);
                this.setState({
                    appFirstStartPopup: false
                })
                return;
            }
            let clientInfoStorage = JSON.parse(result);
            if (clientInfoStorage != null && clientInfoStorage.appFirstStartPopup != null && clientInfoStorage.appFirstStartPopup === 'Y') {
                console.log("==========已经同意过《隐私政策》");
                this.setState({
                    appFirstStartPopup: true
                })

                // 读本地储存, 设备标识
                AsyncStorage.getItem("deviceUniqueId", (err, result) => {
                    if (err) {
                        console.log('===AsyncStorage==deviceUniqueId error' + err);
                        return;
                    }
                    if (result != null) {
                        console.log('===AsyncStorage==deviceUniqueId:', result);
                        this.loadUserCodePwd(result);
                    }
                    return result;
                });
            }
            else {
                this.setState({
                    appFirstStartPopup: false
                })
            }
            return result;
        });

        let urlRequest = "/biz/version/get_latest_version";
        let loadRequest = { appCode: "jzxs" };
        httpPost(urlRequest, loadRequest, (response) => {
            // console.log('=====请求APP版本接口，返回：', response);
            if (response.code == 200 && response.data) {
                var latestVersion = response.data;
                if (latestVersion && latestVersion.appVersion && latestVersion.appVersion != config.currentVersionJiZhiXueShe) {
                    Alert.alert('确认', '最新的版本号是' + latestVersion.appVersion + ",当前App版本为：" + config.currentVersionJiZhiXueShe + "，请重新下载并安装", [
                        {
                            text: "取消", onPress: () => {
                                WToast.show({ data: '点击了取消' });
                            }
                        },
                        {
                            text: "确定", onPress: () => {
                                WToast.show({ data: '点击了确定' });
                                // 下载最新的APP
                            }
                        }
                    ]);
                }
            }
        });
    }

    loadUserCodePwd = (deviceUniqueId) => {

        if (!deviceUniqueId) {
            console.debug("========deviceUniqueId is null, return;")
            return;
        }
        
        let urlRequest = "/biz/user/get_latest_login_record";
        let loadRequest = {
            'deviceId': deviceUniqueId,
        };
        httpPost(urlRequest, loadRequest, (response) => {
            console.log('=====请求最近登录信息，返回：', response);
            if (response.code == 200 && response.data) {
                this.setState({
                    userCode: response.data.userCode,
                    userPwd: response.data.userPwd,
                    // testDouble:response.data.testDouble,
                })
                // if (response.data.tenantAbbreviation && response.data.tenantAbbreviation.length > 0) {
                //     // 本地储存
                //     AsyncStorage.setItem('tenantAbbreviation', response.data.tenantAbbreviation, (error) => {
                //         if (error) {
                //             console.log("===AsyncStorage===设置本地储存失败==tenantAbbreviation", error);
                //         } else {
                //             this.setState({
                //                 tenantAbbreviation: response.data.tenantAbbreviation,
                //             })
                //         }
                //     });
                // }
                // else {
                //     AsyncStorage.removeItem('tenantAbbreviation', (error) => {
                //         if (error) {
                //             console.log("===AsyncStorage===删除本地储存失败==tenantAbbreviation", error);
                //         }
                //     });
                // }

                let tenantExtInfo = {
                    tenantAbbreviation: response.data.tenantAbbreviation,
                    tenantLogo: response.data.tenantLogo,
                    tenantLoginBackground: response.data.tenantLoginBackground,
                    tenantStyleTitle: response.data.tenantStyleTitle,
                }
                console.log('=====请求最近登录信息，tenantExtInfo：', JSON.stringify(tenantExtInfo));
                // 本地储存
                AsyncStorage.setItem('tenantExtInfo', JSON.stringify(tenantExtInfo), (error) => {
                    if (error) {
                        console.log("===AsyncStorage===设置本地储存失败==tenantExtInfo==ERROR:", error);
                    } else {
                        console.log("===AsyncStorage===设置本地储存成功==tenantExtInfo==Success:", JSON.stringify(tenantExtInfo));
                        if (tenantExtInfo.tenantAbbreviation && tenantExtInfo.tenantAbbreviation.length > 0) {
                            this.setState({
                                tenantAbbreviation: tenantExtInfo.tenantAbbreviation,
                            })
                        }
                        if (tenantExtInfo.tenantLogo) {
                            this.setState({
                                tenantLogo: tenantExtInfo.tenantLogo,
                            })
                        }
                        if (tenantExtInfo.tenantLoginBackground) {
                            this.setState({
                                tenantLoginBackground: tenantExtInfo.tenantLoginBackground,
                            })
                            console.log("===tenantExtInfo.tenantLoginBackground", this.state.tenantLoginBackground);
                        }
                    }
                });
            }
        });
    }

    // 登录
    login = () => {
        let toastOpts;
        if (!this.state.userAgreeIsChecked) {
            toastOpts = getFailToastOpts("请同意并勾选用户协议与隐私政策");
            WToast.show(toastOpts)
            return;
        }
        let userCode = this.state.userCode;
        let userPwd = this.state.userPwd;
        if (!userCode) {
            toastOpts = getFailToastOpts("请输入用户名");
            WToast.show(toastOpts)
            return;
        }
        if (!userPwd) {
            toastOpts = getFailToastOpts("请输入密码");
            WToast.show(toastOpts)
            return;
        }
        // 控制3秒内只提交一次
        const clickTime = Date.now()
        if (Math.abs(this.lastClickTime - clickTime) < 3350) {
            // this.setState({
            //     disabled: false,
            // })
            WToast.show({ data: "请不要频繁点击登录" })
            return;
        }
        this.lastClickTime = clickTime;
        // console.log("==userCode", userCode, "==userPwd:", userPwd)
        const DeviceInfo = require('react-native-device-info').default;
        let data = {
            'userCode': userCode,
            'userPwd': userPwd,
            'deviceId': DeviceInfo.getUniqueId(),
            'systemName': DeviceInfo.getSystemName(),
            'systemVersion': DeviceInfo.getSystemVersion(),
        };
        let url = "/biz/user/login";
        httpPost(url, data, this.login_call_back);
    }

    logout = () => {
        let url = "/biz/user/logout?a=123&b=234"
        httpGet(url, this.logout_call_back);
    }

    logout_call_back = (response) => {
        // console.log("=====logout_call_back:", response);
    }

    // 登录回调函数
    login_call_back = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                constants.loginUser = response.data;
                constants.currentTime = response.data.lastLoginTime;
                const DeviceInfo = require('react-native-device-info').default;
                AsyncStorage.setItem('deviceUniqueId', DeviceInfo.getUniqueId(), (error) => {
                    if (error) {
                        console.log("==设置本地储存[deviceUniqueId]失败=", error);
                    } else {
                        console.log("==设置本地储存[deviceUniqueId]成功=");
                    }
                });

                //本地存储业务主管信息
                if ( null != constants.loginUser.managerTenantId) {
                    AsyncStorage.setItem('managerTenantId', JSON.stringify(constants.loginUser.managerTenantId), (error) => {
                        if (error) {
                            console.log("==设置本地储存[managerTenantId]失败==update[managerTenantId]", error);
                        } else {
                            console.log("==设置本地储存[managerTenantId]成功==update[managerTenantId]");
                        }
                    });
                    console.log("=========login managerTenantId update", constants.loginUser.managerTenantId);
                }
                else {
                    AsyncStorage.setItem('managerTenantId',"", (error) => {
                        if (error) {
                            console.log("==设置本地储存[managerTenantId]失败==update[managerTenantId]", error);
                        } else {
                            console.log("==设置本地储存[managerTenantId]成功==update[managerTenantId]");
                        }
                    });
                    console.log("=========login managerTenantId update", constants.loginUser.managerTenantId);
                }

                if (isNumber(response.data.editDeleteTimeLimit)) {
                    constants.editDeleteTimeLimit = response.data.editDeleteTimeLimit;
                }
                if (response.data.tenantExtAttrJSON) {
                    constants.tenantExtAttrJSON = response.data.tenantExtAttrJSON;
                }
                // console.log(">>>>>>>success>>>constants", constants);
                // 请求角色信息
                // 根据权限加载菜单
                let loadUrl = "/biz/role/get";
                let loadRequest = {
                    'roleId': response.data.roleId,
                    'menuClass': "C"
                };
                httpPost(loadUrl, loadRequest, (roleResponse) => {
                    // 当前登录人的权限数据
                    if (roleResponse.code == 200) {
                        if (roleResponse.data && roleResponse.data.menuDTOList) {
                            constants.roleInfo = roleResponse.data;
                            toastOpts = getSuccessToastOpts('登录成功');
                            WToast.show(toastOpts)
                            this.props.navigation.navigate("MainStack");
                        }
                        else {
                            WToast.show({ data: "登录账号所属角色没有菜单" })
                            this.logout();
                            return
                        }
                    }
                    else {
                        toastOpts = getFailToastOpts(roleResponse.message);
                        WToast.show(toastOpts)
                        this.logout();
                        return;
                    }
                });

                loadUrl = "/biz/portal/message/remind/count";
                loadRequest = {
                    "currentPage": 1,
                    "pageSize": 1000,
                    "messageFlag": "0",
                    "messageToUserId": constants.loginUser.userId
                };
                httpPost(loadUrl, loadRequest, (response) => {
                    if (response.code == 200 && response.data && response.data.totalRecord) {
                        constants.noReadMessageCount = response.data.totalRecord;
                    }
                    else {
                        constants.noReadMessageCount = 0;
                    }
                });
                break;
            case 110000100:
                // 首次登录
                toastOpts = getFailToastOpts(response.message);
                WToast.show(toastOpts)
                this.props.navigation.navigate("ModifyPwd",
                    {
                        // 传递参数
                        userId: response.data.userId,
                        userNbr: response.data.userNbr,
                        tenantId: response.data.tenantId,
                        // 传递回调函数
                        refresh: null
                    });
                break;
            case 110000101:
                // 密码过期
                toastOpts = getFailToastOpts(response.message);
                WToast.show(toastOpts)
                this.props.navigation.navigate("ModifyPwd",
                    {
                        // 传递参数
                        userId: response.data.userId,
                        userNbr: response.data.userNbr,
                        tenantId: response.data.tenantId,
                        // 传递回调函数
                        refresh: null
                    });
                break;
            case 401:
                toastOpts = getFailToastOpts(response.message);
                // WToast.show(toastOpts)
                WToast.show({ data: response.message })
                // constants.loginUser = response;
                console.log(">>>>>>>fail>>>constants.loginUser", constants.loginUser);
                this.logout();
                // constants.loginUser = "liminnzhi";
                // this.props.navigation.navigate("MainPage");
                // WToast.show({data: '已经登录过，不用重复登录'})
                // alert("已经登录过，不用重复登录");
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    componentDidMount() {
        console.log("========constants.service_addr:", constants.service_addr);
        this.logout();
    }

    onPressChang = () => {
        this.setState({
            imageState: !this.state.imageState,
        });
    };
    onRegister = () => {
        this.props.navigation.navigate("Register");
    }

    onPrivacy = () => {
        this.props.navigation.navigate("Privacy");
    }
    onUserAgreement = () => {
        this.props.navigation.navigate("UserAgreement");
    }

    notAgreePrivacy = () => {
        this.setState({
            appFirstStartPopup: false
        })

        AsyncStorage.removeItem('clientInfoStorage', (error) => {
            if (error) {
                console.log("==删除本地储存[clientInfoStorage].失败==", error);
            }
            else {
                console.log("==删除本地储存[clientInfoStorage].成功==");
            }
        });
    }

    agreePrivacy = () => {
        this.setState({
            appFirstStartPopup: true
        })

        let clientInfoStorage = {
            appFirstStartPopup: "Y"
        }
        // 本地储存
        AsyncStorage.setItem('clientInfoStorage', JSON.stringify(clientInfoStorage), (error) => {
            if (error) {
                console.log("==设置本地储存[clientInfoStorage]失败==", error);
            } else {
                console.log("==设置本地储存[clientInfoStorage]成功==");
            }
        });
    }

    render() {
        return (
            <KeyboardAvoidingView style={[styles.container, { backgroundColor: '#ffffff' }]} behavior="padding">
                {/* <StatusBar barStyle='light-content' /> */}
                <StatusBar
                    // animated={true} //指定状态栏的变化是否应以动画形式呈现。目前支持这几种样式：backgroundColor, barStyle和hidden
                    hidden={false}  //是否隐藏状态栏。
                    backgroundColor={'#FFFFFF'} //状态栏的背景色
                    // translucent={true}//指定状态栏是否透明。设置为true时，应用会在状态栏之下绘制（即所谓“沉浸式”——被状态栏遮住一部分）。常和带有半透明背景色的状态栏搭配使用。
                    barStyle={'dark-content'} // enum('default', 'light-content', 'dark-content')
                >
                </StatusBar>
                <View style={styles.center}>
                    <Image style={styles.logo} source={{ uri: this.state.tenantLogo }} />
                    <View style={{ alignContent: 'center', marginBottom: 30 }}>
                        <Text style={{ fontSize: 30, color: '#1E6EFA', fontWeight: 'bold' }}>{this.state.tenantAbbreviation}</Text>
                    </View>
                    <View style={styles.inputStyle}>
                        <View style={{ marginLeft: 15, justifyContent: 'center', alignItems: 'flex-end' }}>
                            <Image style={{ width: 28, height: 28 }} source={require('../assets/icon/iconfont/user.png')} />
                            {/* <Text style={pageStyle.textStyle}>用户名</Text> */}
                        </View>
                        <TextInput
                            placeholder={'请输入用户名好的'}
                            editable={true}//是否可编辑
                            onChangeText={(text) => this.setState({ userCode: text })}
                            style={pageStyle.textInfoStyle}>
                            {this.state.userCode}
                        </TextInput>
                    </View>
                    <View style={styles.inputStyle}>
                        <View style={{ marginLeft: 15, justifyContent: 'center', alignItems: 'flex-end' }}>
                            <Image style={{ width: 24, height: 24 }} source={require('../assets/icon/iconfont/psd.png')} />
                            {/* <Text style={pageStyle.textStyle}>密    码</Text> */}
                        </View>
                        <TextInput
                            placeholder={'请输入密码'}
                            secureTextEntry={!this.state.imageState}//是否隐藏
                            editable={true}//是否可编辑
                            onChangeText={(text) => this.setState({ userPwd: text })}
                            style={pageStyle.textInfoStyle}>
                            {this.state.userPwd}
                        </TextInput>
                        <TouchableWithoutFeedback style={{ marginRight: 10 }} onPress={this.onPressChang}>
                            {this.state.imageState ? (
                                <Image style={{ width: 21, height: 14, alignSelf: 'center', marginRight: 10, }}
                                    source={require('../assets/icon/password_show1.png')}
                                />) : (<Image style={{ width: 20, height: 8, alignSelf: 'center', marginRight: 10, }}
                                    source={require('../assets/icon/password_hide.png')}
                                />)}
                        </TouchableWithoutFeedback>
                    </View>

                    {/* 登录 */}
                    <TouchableOpacity
                        style={[this.state.loginBtnDisabled ? null : CommonStyle.disableViewStyle]}
                        onPress={this.login.bind(this)}>
                        <View style={styles.loginBtnStyle}>
                            <Text style={{ color: 'white', fontSize: 20, fontWeight: 'bold' }}>登  录</Text>
                        </View>
                    </TouchableOpacity>
                    {/* 设置 */}
                    <View style={styles.settingStyle}>
                        <Text></Text>
                        <TouchableOpacity onPress={this.onRegister.bind(this)}>
                            <View style={{ borderBottomColor: '#A3A3A3', borderBottomWidth: 1, paddingBottom: 2 }}>
                                <Text style={{ color: '#A3A3A3', fontSize: 15 }}>没有账号，去注册</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    {/* 其他登录方式 */}
                    {/* <View style={styles.otherLoginStyle} >
                        <Text>其他登录方式</Text>
                        <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("Home");
                        }}>
                            <Image source={{uri:'http://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/qq.png'}} style={styles.otherImageStyle} />
                        </TouchableOpacity>
                        <Image source={require('../assets/icon/weChat.png')} style={styles.otherImageStyle} />
                        <Image source={require('../assets/icon/sina.png')} style={styles.otherImageStyle} />
                    </View>
                    */}

                </View>
                <View style={[styles.otherLoginStyle, { flexDirection: 'column' }]}>
                    <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
                        <CheckBox
                            style={{ flex: 1, padding: 10, }}
                            onClick={() => {
                                let userAgreeIsCheckedLet = !this.state.userAgreeIsChecked;
                                this.setState({
                                    userAgreeIsChecked:userAgreeIsCheckedLet
                                })
                                // 本地储存
                                AsyncStorage.setItem('userAgreeIsChecked', userAgreeIsCheckedLet === true ? "Y" :"N", (error) => {
                                    if (error) {
                                        console.log("==设置本地储存[userAgreeIsChecked]失败==", error);
                                    } else {
                                        console.log("==设置本地储存[userAgreeIsChecked]成功==");
                                    }
                                });
                            }}
                            isChecked={this.state.userAgreeIsChecked ? this.state.userAgreeIsChecked : false}
                            // leftText={"CheckBox"}
                            checkedImage={<Image source={require('../assets/icon/iconfont/checkboxBlack.png')} style={{ width: 18, height: 18 }} />}
                            unCheckedImage={<Image source={require('../assets/icon/iconfont/uncheckboxBlack.png')} style={{ width: 18, height: 18 }} />}
                        />
                        <Text style={[styles.otherTextStyle]}>我已阅读并同意</Text>
                        <TouchableOpacity onPress={this.onUserAgreement.bind(this)}>
                            <Text style={{ alignItems: 'center', color: '#1E6EFA' }}>《用户协议》</Text>
                        </TouchableOpacity>
                        <Text style={styles.otherTextStyle}>与</Text>
                        <TouchableOpacity onPress={this.onPrivacy.bind(this)}>
                            <Text style={{ alignItems: 'center', color: '#1E6EFA' }}>《隐私政策》</Text>
                        </TouchableOpacity>
                    </View>
                    <View style={[{flexDirection:'row'}]}>
                        <Text style={{color:'#A3A3A3'}}>版本 Version：{config.currentVersionJiZhiXueShe}</Text>
                    </View>
                </View>
                {
                    (this.state.appFirstStartPopup != null && this.state.appFirstStartPopup == false) ?
                        <View style={{ position: 'absolute', borderRadius: 10, width: '90%', padding: 15, paddingTop: 30, paddingBottom: 30, justifyContent: 'center', alignContent: 'center', backgroundColor: '#FFF' }}>
                            <View style={{ alignItems: 'center' }}>
                                <Text style={{ fontSize: 25, color: '#000' }}>温馨提示</Text>
                            </View>
                            <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginTop: 20 }}>
                                <Text style={{ fontSize: 15, color: '#666' }}>请充分阅读并理解</Text>
                                <TouchableOpacity onPress={this.onUserAgreement.bind(this)}>
                                    <Text style={{ fontSize: 15, color: 'red' }}>《用户协议》</Text>
                                </TouchableOpacity>
                                <Text style={{ fontSize: 15 }}>与</Text>
                                <TouchableOpacity onPress={this.onPrivacy.bind(this)}>
                                    <Text style={{ fontSize: 15, color: 'red' }}>《隐私政策》</Text>
                                </TouchableOpacity>
                            </View>
                            <View style={{ flexDirection: 'column', alignItems: 'flex-start', marginTop: 10 }}>
                                <Text style={{ fontSize: 15, color: '#666', lineHeight: 20 }}>
                                    1.为保障系统运行，我们会申请您的手机号码、设备信息、连接网络权限、阿里推送标识、读存SD卡（上传和保存图片使用）
                                </Text>
                                <Text style={{ fontSize: 15, color: '#666', lineHeight: 20 }}>
                                    2.您在使用{constants.appName}服务时，我们会收集并汇总您记录的操作信息
                                </Text>
                                <Text style={{ fontSize: 15, color: '#666', lineHeight: 20 }}>
                                    3.应用目前集成了facebook.fresco、google谷歌等SDK
                                </Text>
                                <Text style={{ fontSize: 15, color: '#666', lineHeight: 20 }}>
                                    4.请您谨慎并留意个人敏感信息，您同意您的个人敏感信息我们可以按本《用户协议》与《隐私政策》所述的目的和方式来处理。
                                </Text>
                            </View>
                            <TouchableOpacity onPress={() => {
                                this.agreePrivacy();
                            }}>
                                <LinearGradinet start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#9b63cd', '#e0708c']} style={{ margin: 10, marginTop: 20, padding: 10, alignItems: 'center', borderRadius: 10 }}>
                                    <Text style={{ color: '#FFF', fontSize: 18 }}>同意并继续</Text>
                                </LinearGradinet>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => {
                                WToast.show({ data: '只有同意并继续才能享有我们的服务噢' });
                                this.notAgreePrivacy();
                            }}>
                                <View style={{ alignItems: 'center', marginTop: 20 }}>
                                    <Text style={{ color: '#666', fontSize: 18 }}>不同意</Text>
                                </View>
                            </TouchableOpacity>

                        </View>
                        : null
                }

            </KeyboardAvoidingView>
        )
    }

}

const styles = StyleSheet.create({

    container: {
        width: screenWidth,
        height: screenHeight,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    center: {
        width: screenWidth * 0.8,
        // position:'absolute',
        alignItems: 'center',
        marginTop: -60,
        justifyContent: 'center',
    },
    logo: {
        width: 100,
        height: 100,
        borderRadius: 50,
        borderWidth: 0,
        // marginTop:80,
        marginBottom: 30
    },
    textIntputStyle: {
        height: 38,
        width: 350,
        backgroundColor: '#303F58',
        marginBottom: 1,
        // 内容居中
        textAlign: "center"

    },
    inputStyle: {
        height: 44,
        flexDirection: 'row',
        justifyContent: 'space-between',
        margin: 10,
        borderColor: '#D2D2D2',
        borderWidth: 1, borderRadius: 22
    },

    loginBtnStyle: {
        width: screenWidth * 0.76,
        height: 45,
        backgroundColor: '#1E6EFA',
        marginTop: 40,
        marginLeft: 10,
        marginRight: 10,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 22
    },
    settingStyle: {
        // 设置主轴的方向
        flexDirection: 'row',
        // 主轴的对齐方式
        justifyContent: 'space-between',
        width: screenWidth * 0.76,
        marginTop: 20,
    },

    otherLoginStyle: {
        position: 'absolute',
        zIndex: 99999,
        justifyContent: 'center',
        alignItems: 'center',
        bottom: 40,
    },
    otherImageStyle: {
        width: 50,
        height: 50,
        borderRadius: 25,
        marginLeft: 5,
        backgroundColor: '#F5F5F5'
    },
    otherTextStyle: {
        alignItems: 'center',
        color: '#A3A3A3'
    }

});

const pageStyle = StyleSheet.create({
    textInfoStyle: {
        alignSelf: 'center',
        marginLeft: 10,
        color: '#999999',
        fontSize: 16,
        flex: 1,
    },
    textStyle: {
        //   alignSelf: 'center',
        marginLeft: 10,
        color: '#999999',
        fontSize: 16,
        fontWeight: 'bold'

    },
});

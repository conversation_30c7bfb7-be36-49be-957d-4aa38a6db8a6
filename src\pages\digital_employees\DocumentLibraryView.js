import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Clipboard, Linking, TextInput, Image,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
let screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class DocumentLibraryView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate: "",
            documentId: null,
            documentName: null,
            courseName: null,
            courseLevelName: null,
            courseSort: null,
            courseTypeName: null,
            gmtCreated: "2020-01-12",
            documentAccessPath: null,
            userName: "",
        }
    }


    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const {dataItem} = route.params;
            this.setState(
                dataItem
            )
            console.log('@@@@@@++++',JSON.stringify(dataItem, null, 6))
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View />
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={'文档详情'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.onlyContainHeaderContentViewStyle}>

                    <TouchableOpacity onPress={()=>{
                        this.props.navigation.navigate("ViewDocument", { documentId: this.state.documentId, documentName: this.state.documentName })
                    }}>
                        <View style={{ display: 'flex', flexDirection: 'row', marginLeft: 10, marginRight: 16, width: screenWidth - 20 }}>
                            {this.state.coursePhoto ?
                                <Image style={[{ height: 106, borderRadius: 10, width: 130 }]} source={{ uri: constants.image_addr + '/' + this.state.coursePhoto }} resizeMode='contain' />
                                :
                                <Image style={[{ height: 106, borderRadius: 10, width: 130 }]} source={require('../../assets/icon/coverSheet.png')} resizeMode='contain' />
                            }
                            <View style={{ flexDirection: 'column', paddingTop: 1, marginLeft: 14, width: screenWidth - 170, }}>
                                <View style={[styles.titleViewStyle, { marginTop: 10, }]}>
                                    <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16, fontWeight: '400', width: screenWidth - 170 }} >
                                        {this.state.documentName}
                                    </Text>
                                </View>
                                <View style={{ display: 'flex', flexDirection: 'row',alignItems:"center",justifyContent: 'space-between'}}>
                                    <View style={[styles.titleViewStyle, { marginTop: 5, marginBottom: 10 }]}>
                                        <View style={{ flexDirection: 'row', marginTop: 5, paddingLeft: 9, paddingTop: 3, paddingBottom: 3, paddingRight: 12, backgroundColor: 'rgba(236, 238, 242, 1)', borderRadius: 12 }}>
                                            <Text style={[styles.itemContentStyle, { fontSize: 12, color: 'rgba(64, 73, 86, 1)' }]} >{this.state.courseName}</Text>
                                        </View>
                                    </View>
                                    <View style={{
                                        // backgroundColor:"green",
                                        //外边距
                                        marginLeft: 0,
                                        marginRight: 16,
                                        marginTop: 0,
                                        marginBottom: 0,
                                        height:24,
                                        width:24
                                    }}>
                                        <Image style={{ height: 24 , width: 24}} source={require('../../assets/icon/iconfont/preview_new.png')}></Image>
                                    </View>                                    
                                </View>

                            </View>
                        </View >                        
                    </TouchableOpacity>


                    <View style={styles.lineViewStyle}></View>

                    <View style={{ width: screenWidth - 20 }}>
                        <View style={{ flexDirection: "row", marginLeft: 24 }}>
                            <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/aboutCourse.png')}></Image>
                            <Text style={{ color: 'rgba(128, 141, 161, 1)', fontSize: 14, marginLeft: 8 }}>课程相关</Text>
                        </View>
                        <View style={{ marginLeft: 54 }}>
                            <Text style={{ fontWeight: '600', color: 'rgba(64, 73, 86, 1)', fontSize: 16 }}>
                                {'第' + this.state.courseSort + '课 ' + this.state.courseName}
                            </Text>
                        </View>

                        <View style={{ flexDirection: "row", marginLeft: 54 }}>
                            <View style={{ flexDirection: 'row', marginTop: 5, paddingLeft: 9, paddingTop: 3, paddingBottom: 3, paddingRight: 12, backgroundColor: 'rgba(253, 66, 70, 1)', borderRadius: 12 ,borderBottomLeftRadius:0}}>
                                <Text style={[styles.itemContentStyle,{ fontSize: 12, color: 'rgba(255, 255, 255, 1)', }]}>{this.state.courseLevelName + '级别课程'}</Text>
                            </View>
                            <View style={{ flexDirection: 'row', marginLeft: 10, marginTop: 5, paddingLeft: 9, paddingTop: 3, paddingBottom: 3, paddingRight: 12, backgroundColor: 'rgba(30, 110, 250, 0.20)', borderRadius: 12 }}>
                                <Text style={[styles.itemContentStyle, { fontSize: 12, color: 'rgba(37, 91, 218, 1)' }]}>{this.state.courseTypeName}</Text>
                            </View>
                        </View>
                    </View>

                    <View style={{ flexDirection: "row", marginLeft: 24, marginTop: 15, width: screenWidth - 48 }}>
                        <View>
                            <View style={{ flexDirection: "row" }}>
                                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/submitter.png')}></Image>
                                <Text style={{ color: 'rgba(128, 141, 161, 1)', fontSize: 14, marginLeft: 8 }}>提交人</Text>
                            </View>
                            <View style={{ marginLeft: 30 }}>
                                <Text style={{ fontWeight: '600', color: 'rgba(64, 73, 86, 1)', fontSize: 16 }}>
                                    {this.state.userName}
                                </Text>
                            </View>
                        </View>
                        <View style={{ marginLeft: 84 }}>
                            <View style={{ flexDirection: "row" }}>
                                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/submitTime.png')}></Image>
                                <Text style={{ color: 'rgba(128, 141, 161, 1)', fontSize: 14, marginLeft: 8 }}>提交时间</Text>
                            </View>
                            <View style={{ marginLeft: 30 }}>
                                <Text style={{ fontWeight: '600', color: 'rgba(64, 73, 86, 1)', fontSize: 16 }}>
                                    {this.state.gmtCreated.slice(0, 10)}
                                </Text>
                            </View>
                        </View>
                    </View>
                    <View style={[CommonStyle.btnRowStyle, { width: screenWidth, marginLeft: 0, marginTop: screenHeight - 410 }]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { marginLeft: 20, width: (screenWidth - 56) / 2 }]} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>返回</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => {
                            // if (this.state.documentType === 'PD') {
                                let netWork = this.state.documentAccessPath;
                            //     if (this.state.documentType === "TD") {
                            //         this.props.navigation.navigate("ViewDocument", { documentId: this.state.documentId, documentName: this.state.documentName })
                            //     }
                            //     else {
                                    Clipboard.setString(netWork);
                                    // WToast.show({ data: "访问网址:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + netWork });
                                    Alert.alert('确认', '访问网址已复制到粘贴板，使用浏览器打开:\n' + netWork + '', [
                                        {
                                            text: "不打开", onPress: () => {
                                                WToast.show({ data: '点击了不打开' });
                                            }
                                        },
                                        {
                                            text: "打开", onPress: () => {
                                                WToast.show({ data: '点击了打开' });
                                                // 直接打开外网链接 
                                                Linking.openURL(netWork)
                                            }
                                        }
                                    ]);
                            //     }
                            // }
                        }}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView, { marginRight: 20, width: (screenWidth - 56) / 2 }]}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>下载</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>

            </View>

        )
    }
}
const styles = StyleSheet.create({
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    lineViewStyle: {
        height: 1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        marginBottom: 22,
        borderBottomWidth: 0.5,
        borderColor: '#E8E9EC'
    },
    innerViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
});

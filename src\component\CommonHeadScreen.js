import React, {Component} from 'react';
import PropTypes from 'prop-types';
import {
    StyleSheet,
    Text,
    View,
    Dimensions,
    Platform, TouchableOpacity
} from 'react-native';
import { ifIphoneX, ifIphoneXHeaderHeight } from '../utils/ScreenUtil';

// 取得屏幕的宽高Dimensions
const {width,height} = Dimensions.get('window');

export default class CommonHeadScreen extends Component {

    constructor(props) {
        super(props);
        this.state = {  
            title:props.title,
            do:props.do,
            doName:''
        };
    }

    static propTypes = {
      leftItem: PropTypes.func,
      titleItem: PropTypes.func,
      rightItem: PropTypes.func,
    };

    componentDidMount(){
        console.log('componentDidMount');
        if(this.state.do === 'logout') {
            this.setState({
                doName:'登出' 
            })
        }
        else if(this.state.do === 'simi_finished_check') {
            this.setState({
                doName:'点验' 
            })
        }
        else if (this.state.do === 'encastage') {
            this.setState({
                doName:'装窑'
            })
        }
        else if (this.state.do === 'warm_record_add') {
            this.setState({
                doName:'新增'
            })
        }
        else if (this.state.do === 'check_out_add') {
            this.setState({
                doName:'新增'
            })
        }
        else {
            this.setState({
                doName:'登出' 
            })
        }
    }

    renderLeftItem(){
        if (this.props.leftItem === undefined) return;
        return this.props.leftItem();
    }
    renderTitleItem(){
        if (this.props.titleItem === undefined) return;
        return this.props.titleItem();
    }
    renderRightItem(){
        if (this.props.rightItem === undefined) {
            return (
                <View style={styles.headRightView}>
                    <Text style={styles.headRightText}>{this.state.doName}</Text>
                </View>
            )
        };
        return this.props.rightItem();
    }

    render() {
        return (
            <View style={{width:width,
                height:ifIphoneXHeaderHeight(),
                // backgroundColor: (constants.loginUser && constants.loginUser.mobileStyleHeadBackgroundColor) ? constants.loginUser.mobileStyleHeadBackgroundColor : '#CB4139',//背景色，默认白色
                backgroundColor:'#ffffff',//背景色，默认白色
            }}>
                <View style={{
                    paddingRight:10,
                    paddingLeft:10,
                    paddingBottom: 12,
                    marginTop: 0,
                    height:ifIphoneXHeaderHeight(),
                    flexDirection:'row',//横向排
                    justifyContent:'space-between',//主轴对齐方式
                    alignItems: 'flex-end',//次轴对齐方式（上下居中）
                    borderBottomWidth:0,
                    // borderBottomWidth: this.props.borderBottomWidth || 1,//是否有下边框
                    borderColor: this.props.borderColor || '#ccc',
                }}>
                    
                    <View>
                        {this.renderLeftItem()}
                    </View>
                    <View style={{}}>
                        {
                            this.state.title?
                            <Text style={styles.headCenterTitleText}>{this.state.title}</Text>
                            :
                            this.renderTitleItem()
                        }    
                    </View>
                    <View>
                        {this.renderRightItem()}
                    </View>
                </View>
            </View>
        );
    }
}
const styles = StyleSheet.create({
    container: {

    },
    headCenterTitleText:{
       fontSize:20,
       color:'#000000',
       fontWeight:'600',
    },
    headRightView:{
        height:50,
        paddingRight:10,
        alignItems:'center',
        justifyContent:'center'
    },
    headRightText:{
        color:'#33333375',
        fontSize:14,
    }
});
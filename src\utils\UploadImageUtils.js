import './Global';
import { Alert } from 'react-native';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import { WToast } from 'react-native-smart-tip';

export const uploadMultiImageLibrary = async (selectImageCountLimit, imageType, bizCallback) => {
    if (!selectImageCountLimit || selectImageCountLimit.length === 0) {
        let toastOpts = getFailToastOpts("selectImageCountLimit cannot be empty");
        WToast.show(toastOpts);
        return;
    }
    if (selectImageCountLimit > 6) {
        console.error("selectImageCountLimit max 6");
        let toastOpts = getFailToastOpts("selectImageCountLimit max 6");
        WToast.show(toastOpts);
        return;
    }
    if (!imageType || imageType.length === 0) {
        let toastOpts = getFailToastOpts("ImageType cannot be empty");
        WToast.show(toastOpts);
        return;
    }
    const options = {
        title: '选择图片',
        cancelButtonTitle: '取消',
        takePhotoButtonTitle: '拍照',
        chooseFromLibraryButtonTitle: '相册',
        cameraType: 'back',
        mediaType: 'photo',
        videoQuality: 'high',
        durationLimit: 10,
        maxWidth: 5000,
        maxHeight: 5000,
        aspectX: 2,
        aspectY: 1,
        quality: 1,
        selectionLimit: selectImageCountLimit,
        angle: 0,
        allowsEditing: false,
        noData: false,
        storageOptions: {
            skipBackup: true,
            path: 'PickLocalImg' // 存储本地地址
        }
    }
    launchImageLibrary(options, (res) => {
        launchCameraOrImageLibraryCallBack(res, null, imageType, true, bizCallback)
    });
}

export const uploadImageLibrary = async (oldFile, imageType, bizCallback, banner) => {
    if (!imageType || imageType.length === 0) {
        let toastOpts = getFailToastOpts("ImageType cannot be empty");
        WToast.show(toastOpts);
        return;
    }
    const options = {
        title: '选择图片',
        cancelButtonTitle: '取消',
        takePhotoButtonTitle: '拍照',
        chooseFromLibraryButtonTitle: '相册',
        cameraType: 'back',
        mediaType: 'photo',
        videoQuality: 'high',
        durationLimit: 10,
        maxWidth: banner ? 3000 : 720,
        maxHeight: banner ? 640 : 1280,
        aspectX: 2,
        aspectY: 1,
        quality: 1,
        angle: 0,
        allowsEditing: false,
        noData: false,
        storageOptions: {
            skipBackup: true,
            path: 'PickLocalImg' // 存储本地地址
        }
    }
    launchImageLibrary(options, (res) => {
        launchCameraOrImageLibraryCallBack(res, oldFile, imageType, false, bizCallback)
    });
}

export const uploadImageLibraryWithCrop = async (imageType , bizCallback, banner) => {
    if (!imageType || imageType.length === 0) {
        let toastOpts = getFailToastOpts("ImageType cannot be empty");
        WToast.show(toastOpts);
        return;
    }
    const options = {
        title: '选择图片',
        cancelButtonTitle: '取消',
        takePhotoButtonTitle: '拍照',
        chooseFromLibraryButtonTitle: '相册',
        cameraType: 'back',
        mediaType: 'photo',
        videoQuality: 'high',
        durationLimit: 10,
        maxWidth: banner ? 3000 : 720,
        maxHeight: banner ? 640 : 1280,
        aspectX: 2,
        aspectY: 1,
        quality: 1,
        angle: 0,
        allowsEditing: false,
        noData: false,
        storageOptions: {
            skipBackup: true,
            path: 'PickLocalImg' // 存储本地地址
        }
    }
    launchImageLibrary(options, (res) => {
        bizCallback(res)
    });
}

export const uploadCameraImage = async (oldFile, imageType, bizCallback) => {
    if (!imageType || imageType.length === 0) {
        let toastOpts = getFailToastOpts("ImageType cannot be empty");
        WToast.show(toastOpts);
        return;
    }
    const options = {
        title: '选择图片',
        cancelButtonTitle: '取消',
        takePhotoButtonTitle: '拍照',
        chooseFromLibraryButtonTitle: '相册',
        cameraType: 'back',
        mediaType: 'photo',
        videoQuality: 'high',
        durationLimit: 10,
        maxWidth: 720,
        maxHeight: 1280,
        aspectX: 2,
        aspectY: 1,
        quality: 1,
        angle: 0,
        allowsEditing: false,
        noData: false,
        storageOptions: {
            skipBackup: true,
            path: 'PickLocalImg' // 存储本地地址
        }
    }
    launchCamera(options, (res) => {
        launchCameraOrImageLibraryCallBack(res, oldFile, imageType, false, bizCallback)
    });
}

const launchCameraOrImageLibraryCallBack = (res, oldFile, imageType, multipartFile, bizCallback) => {
    if (res.didCancel) {
        console.log('User cancelled photo picker');
        WToast.show({ data: "Cancle" });
    }
    else if (res.errorCode) {
        if (res.errorCode.indexOf("camera_unavailable") > -1) {
            Alert.alert(('提示信息', 'APP需要使用相机，请打开相机权限允许APP使用'), [{
                text: '设置',
                onPress: () => {
                    Linking.openURL('app-settings:')
                        .catch(err => console.log('error', err))
                }
            }, {
                text: '取消'
            }])
        }
        else if (res.errorCode.indexOf("permission") > -1) {
            let toastOpts = getFailToastOpts("权限不足");
            WToast.show(toastOpts);
        }
        else if (res.errorCode.indexOf("others") > -1) {
            let errorMessage = "[others]";
            if (res.errorMessage) {
                errorMessage += res.errorMessage;
            }
            let toastOpts = getFailToastOpts(errorMessage);
            WToast.show(toastOpts);
        }
        else {
            let errorMessage = "[" + res.errorCode + "]";
            if (res.errorMessage) {
                errorMessage += res.errorMessage;
            }
            let toastOpts = getFailToastOpts(errorMessage);
            WToast.show(toastOpts);
        }
    }
    else {
        // console.log("===========res", res)
        // 用户授权并选择照片/拍照后，调用接口
        // 保存选中的图片
        if (multipartFile) {
            let files = [];
            var formData = new FormData();
            // 多图片批量上传
            res.assets.forEach((selImageObj) => {
                let source;
                if (Platform.OS === 'android') {
                    source = selImageObj.uri;
                } else {
                    source = selImageObj.uri.replace('file://', '');
                }
                formData.append('files', { uri: source, type: 'multipart/form-data', name: selImageObj.fileName }, selImageObj.fileName);
            })
            // formData.append('files', files);
            formData.append('imageType', imageType);
            let params = {
                formData,
            }
            //单个图片上传接口
            multiImgUpload(params, bizCallback);
        }
        else {
            // 单个图片上传
            let source;
            if (Platform.OS === 'android') {
                source = res.assets[0].uri;
            } else {
                source = res.assets[0].uri.replace('file://', '');
            }
            const formData = new FormData();
            // 文件类型根据对应的后端接口改变！！！
            let file = { uri: source, type: 'multipart/form-data', name: res.assets[0].fileName };
            formData.append('file', file);
            formData.append('oldFile', oldFile);
            formData.append('imageType', imageType);
            let params = {
                formData,
            }
            //单个图片上传接口
            singleImgUpload(params, bizCallback);
        }

    }
}


// 图片上传
const singleImgUpload = async (params, callback) => {
    let { formData } = params
    console.log("==图片==",JSON.stringify(formData, null, 6))
    return await uploadRequest('/biz/upload/image/single_image_upLoad', formData, callback)
}

// 图片批量上传
const multiImgUpload = async (params, callback) => {
    let { formData } = params
    console.log("==========formData:", formData)
    return await uploadRequest('/biz/upload/image/multi_image_upLoad', formData, callback)
}

const uploadRequest = async (url, datas, callback) => {
    const params = {
        method: 'POST',
        body: datas,
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        timeout: 10000 // 10s超时
    };

    try {
        const response = await fetch(`${constants.service_addr}${url}`, params);
        const data = await response.json()
        callback(data);
    }
    catch (error) {
        let toastOpts;
        toastOpts = getFailToastOpts(error);
        WToast.show(toastOpts)

    }

}

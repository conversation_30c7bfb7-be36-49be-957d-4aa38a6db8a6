import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class PermissionUserList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            permissionCode:"",
            permissionName:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { permissionCode, permissionName } = route.params;
            if (permissionCode) {
                console.log("=============permissionCode" + permissionCode + "");
                this.setState({
                    permissionCode:permissionCode
                })
                this.loadPermissionUserList(permissionCode);
            }
            if (permissionName) {
                console.log("=============permissionName" + permissionName + "");
                this.setState({
                    permissionName:permissionName
                })
            }
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/permission/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "permissionCode":this.state.permissionCode,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/permission/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "permissionCode":this.state.permissionCode,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadPermissionUserList();
    }

    loadPermissionUserList=(permissionCode)=>{
        let url= "/biz/permission/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "permissionCode":permissionCode?permissionCode:this.state.permissionCode,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this.loadPermissionUserListCallBack);
    }

    loadPermissionUserListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord=()=>{
        let url= "/biz/permission/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "permissionCode":this.state.permissionCode,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    
    deletePermissionUser =(userId)=> {
        console.log("=======delete=userId", userId);
        let url= "/biz/permission/delete";
        let requestParams={"permissionCode":this.state.permissionCode,'userId':userId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }


    renderRow=(item, index)=>{
        return (
            <View key={item.userId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>用户姓名：{item.userName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系电话：{item.userNbr?item.userNbr:"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>更新时间：{item.gmtCreated }</Text>
                </View>
                
                    <View style={[{width: 40, height: 40, 
                        backgroundColor: 'rgba(255,0,0,0.0)', 
                        position:'absolute', 
                        alignItems:'center',
                        justifyContent:'center',
                        right: 20,bottom:10
                        }]}>
                            <TouchableOpacity onPress={()=>{
                                Alert.alert('确认','确定取消该用户的权限吗？',[
                                    {
                                        text:"取消", onPress:()=>{
                                            WToast.show({data:'点击了取消'});
                                        }
                                    },
                                    {
                                        text:"确定", onPress:()=>{
                                            WToast.show({data:'点击了确定'});
                                            this.deletePermissionUser(item.userId)
                                        }
                                    }
                                ]);
                            }}>
                                <Image style={{width:70, height:35}} source={require('../../assets/icon/iconfont/out.png')}></Image>
                            </TouchableOpacity>
                    </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PermissionUserAddList", 
                {
                    permissionCode:this.state.permissionCode,
                    permissionName:this.state.permissionName,
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='用户管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0, index:1000}]} onLayout={this.topBlockLayout.bind(this)}>
                    {/* <View style={{flexDirection:"row"}}>
                        <Text style={[styles.titleTextStyle,{marginLeft:10, marginTop:5,fontWeight:'bold',marginRight:150}]}>职位名称：{this.state.jobName}</Text>
                        <Text style={[styles.titleTextStyle,{marginRight:10, marginTop:5,fontWeight:'bold'}]}>职位个数：{this.state.jobAmount}</Text>
                    </View> */}                    
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle,{marginLeft:10, fontWeight:'bold', marginRight:100}]}>权限名称：{this.state.permissionName}</Text>
                        {/* <Text style={[styles.titleTextStyle,{marginLeft:10, fontWeight:'bold', marginRight:100}]}>职位个数：{this.state.jobAmount}</Text> */}
                    </View>
                    
                    <View style={{marginTop:10}}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput 
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'姓名或电话'}
                                onChangeText={(text) => this.setState({searchKeyWord:text})}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        paddingBottom:5
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    searchInputText:{
        width:screenWidth / 2,
        borderColor:'#000000',
        // borderBottomWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:16,
        marginLeft:10,
        paddingLeft:10,
        paddingRight:10,
        paddingBottom:0,
        paddingTop:0
    },

    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },

});
import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image, TextInput,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ScrollView } from 'react-native-gesture-handler';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
import <PERSON>Viewer from 'react-native-image-zoom-viewer';
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import EmptyPortalTenantComponent from '../../component/EmptyPortalTenantComponent';

const leftLabWidth = 130;
export default class ConfiguringTenantsParamItem extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            pictureIndex:0,
            isShowImage:false,
            image:"",
            bizModuleCode:null,
            paramName: null,
            currentTenantId: null,
            configItemSettingList: [],
            // 判断租户是否已配置
            configItemSettingFlag: false

        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { bizModuleCode, paramName, currentTenantId } = route.params;
            console.log("bizModuleCode========",bizModuleCode);
            console.log("currentTenantId===========", currentTenantId)
            if (paramName) {
                this.setState({
                    paramName: paramName
                })
            }
            if (bizModuleCode) {
                this.setState({
                    bizModuleCode: bizModuleCode,
                })
                this.loadPortalTenantConfigList(bizModuleCode);        
            }
            if (currentTenantId) {
                this.setState({
                    currentTenantId: currentTenantId
                })
                this.loadTenantConfigItemSetting(currentTenantId);
            }
        }
    }

    // 加载所有配置项
    loadPortalTenantConfigList=(bizModuleCode)=>{
        let url= "/biz/config/item/param/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "bizModuleCode": bizModuleCode ? bizModuleCode : this.state.bizModuleCode,
            "isConfigItemSetting": "Y" // 判断是否为配置项设置
        };
        httpPost(url, loadRequest, this.loadPortalTenantConfigListCallBack);
    }

    loadPortalTenantConfigListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 加载租户配置项
    loadTenantConfigItemSetting = (currentTenantId)=>{
        let url= "/biz/tenant/config/getTenantConfigItemSetting";
        let loadRequest={
            "paramCode": "CONFIG_ITEM_SETTING",
            "currentTenantId": currentTenantId,
        };
        httpPost(url, loadRequest, this.loadTenantConfigItemSettingCallBack);
    }

    loadTenantConfigItemSettingCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var val = response.data.paramValue;
            if (val) {
                var configItemSetting = val.split(",")
                console.log("====configItemSetting====", configItemSetting)
                this.setState({
                    configItemSettingList: configItemSetting,
                })
            }
            this.setState({
                configItemSettingFlag: true
            })
        }
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/config/item/param/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "bizModuleCode": this.state.bizModuleCode,
            "isConfigItemSetting": "Y" // 判断是否为配置项设置
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

       // 回调函数
       callBackFunction = ()=>{
        let url= "/biz/config/item/param/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "bizModuleCode": this.state.bizModuleCode,
            "isConfigItemSetting": "Y" // 判断是否为配置项设置
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadPortalTenantConfigList();
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity> 
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={this.saveTenantConfigItemSetting.bind(this)}>
                <Text style={CommonStyle.headRightText}>保存</Text>
            </TouchableOpacity>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyPortalTenantComponent/>
    }

    saveTenantConfigItemSetting = () => {
        console.log("=======saveTenantConfigItemSetting");
        let url = "/biz/tenant/config/addTenantConfigItemSetting";
        if (this.state.configItemSettingFlag) {
            url = "/biz/tenant/config/modifyTenantConfigItemSetting";
        }
        console.log("url=======", url)
        let requestParams = {
            "paramCode": "CONFIG_ITEM_SETTING",
            "paramValue": this.state.configItemSettingList.toString(),
            "currentTenantId": this.state.currentTenantId,
            "valueType": "text"
        };
        console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.saveTenantConfigItemSettingCallBack);
    }

    saveTenantConfigItemSettingCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.loadTenantConfigItemSetting(this.state.currentTenantId);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    renderRow=(item, index)=>{
        // console.log("renderRow",index,JSON.stringify(item, null, 6))
        return (
            <View key={item.paramCode} style={{backgroundColor:'rgba(255, 255, 255, 1)'}}>
                <TouchableOpacity onPress={() => {
                    var selConfigItemSetting = this.state.configItemSettingList;
                    if (selConfigItemSetting.includes(item.paramCode)) {
                        arrayRemoveItem(selConfigItemSetting, item.paramCode);
                    }
                    else {
                        selConfigItemSetting = selConfigItemSetting.concat(item.paramCode)
                    }
                    this.setState({
                        configItemSettingList: selConfigItemSetting,
                    })
                    WToast.show({ data: '点击了' + item.paramName });
                    console.log("======selConfigItemSetting:", selConfigItemSetting)
                }}>
                    <View style={[styles.itemContentViewStyle]}>
                        <Text style={{ fontSize: 18, fontWeight: '600' }}>{item.paramName}</Text>
                    </View>
                    {
                        (this.state.configItemSettingList.includes(item.paramCode)) ?
                        <View style={[{ position: 'absolute', right: 20, top: 20}]}>
                            <Image style={{ width: 20, height: 20}} source={require('../../assets/icon/iconfont/staffSelected.png')}></Image>
                        </View>
                        :
                        <View/>
                    }
                </TouchableOpacity>
                <View style={styles.lineViewStyle}/> 
            </View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.paramName+"配置"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                    <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                        <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={() => this.flatListFooterComponent()}
                            onEndReached={() => this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    titleViewStyle:{
        flexDirection:'row',
        // justifyContent:'space-between',
        marginLeft:12,
        marginRight:16,
        // marginBottom:5,
        marginTop:8,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        paddingLeft: 16,
        flexDirection: 'row',
        backgroundColor: "#FFFFFF",
        paddingTop: 20,
        paddingBottom: 20,
        width: screenWidth - 50,
        alignItems: 'center',
        flexWrap: 'wrap'
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    inputRowStyle: {
        paddingLeft: 12,
        height: 80,
        flexDirection: 'row',
        backgroundColor: "#FFFFFF",
        paddingTop: 10,
        paddingBottom: 10
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        // marginTop: 15,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC',
    },

});
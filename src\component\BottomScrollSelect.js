/**
 * Created by justin on 2018/3/19.
 */
import React, { Component } from 'react';
import moment from 'moment';
import {
    AppRegistry,
    Text,
    View,
    Dimensions,
    StyleSheet,
    Platform,
    BackHandler,
    Animated,
    Easing
} from 'react-native';
import Picker from 'react-native-picker';
import deviceInfo from '../utils/deviceInfo'
import area from '../assets/data/area.json';
import PropTypes from 'prop-types';
export default class BottomScrollSelect extends Component {
    static propTypes = {
        cancel: PropTypes.string,             //取消按钮
        title: PropTypes.string,              //中间标题
        confirm: PropTypes.string,            //确认按钮
        callBackAddressValue: PropTypes.func, //地址回调方法
        callBackDateValue: PropTypes.func,    //时间回调方法
        callBackMonthValue: PropTypes.func,    //月份回调方法
        callBackHospitalValue: PropTypes.func,    //院区回调方法
        callBackBrickTypeValue: PropTypes.func, //砖型选择回调方法
        callBackOrderValue: PropTypes.func, //订单选择回调方法
        callBackCustomerValue: PropTypes.func, //客户选择回调方法
        callBackSupplierValue: PropTypes.func, //供应商选择回调方法
        callBackWorkingShiftValue: PropTypes.func,//班次选择回调函数
        callBackStaffValue: PropTypes.func,//员工选择回调函数
        callBackEducationValue: PropTypes.func,//学历选择回调函数
        callBackAuditStaffValue: PropTypes.func,//审核人选择回调函数
        callBackMajorValue: PropTypes.func,//专业选择回调函数
        callBackClassValue: PropTypes.func,//班级选择回调函数
        callBackPoliticalStatusValue: PropTypes.func,//政治面貌选择回调函数
        callBackEnglishLevelValue: PropTypes.func,//英语能力选择回调函数
        callBackParamCodeValue: PropTypes.func,//系统配置选择回调函数
        callBackRoleValue: PropTypes.func,//角色选择回调函数
        callBackOrgValue: PropTypes.func,//组织选择回调函数
        callBackPositionTypeValue: PropTypes.func,//组织选择回调函数
        callBackEnterpriseValue: PropTypes.func,//公司选择回调函数
        callBackAuditTypeValue: PropTypes.func,//申请类型回调函数
        callBackOutsourcingTenantValue: PropTypes.func,//外协厂回调函数
        callBackTelOperateResultValue: PropTypes.func,//电邀结果回调函数
        callBackPointClassIdValue: PropTypes.func,//积分类别选择回调函数
        callBackCourseTypeIdValue: PropTypes.func,//课程类型选择回调函数
        callBackCourseLevelIdValue: PropTypes.func,//课程所属职级选择回调函数
        callBackExamDateValue: PropTypes.func, //考试日期选择回调
        callBackExamTimeValue: PropTypes.func, //考试时间选择回调
        callBackCourseValue: PropTypes.func, //课程选择回调
        callBackDepartmentNameValue: PropTypes.func, //部门选择回调
        callBackStorageOutTypeNameValue: PropTypes.func, //出库类型选择回调
        callBackUnitValue: PropTypes.func,//单位选择回调函数
        callBackMedicalInsuranceTypeValue: PropTypes.func,//医保类型选择回调函数
        pickerBg: PropTypes.array,            //背景颜色
        startYear: PropTypes.number,          //开始年份
        endYear: PropTypes.number,            //结束年份
        synchronousRefresh: PropTypes.bool,   //是否同步刷新
        callBackSelectPowerLevelValue: PropTypes.func,//职位权重选择回调函数
        callBackPharmacyValue: PropTypes.func,//药房名称选择回调
        callBackIsUpProductValue: PropTypes.func,//是否上架选择回调
        callBackIsSpotFlagValue: PropTypes.func,//是否现货选择回调
        callBackSalesMonthValue: PropTypes.func,//销售看板月份选择
        callBackAssessClassIdValue: PropTypes.func,//考核类别选择回调函数
        callBackAssessDifficultyValue: PropTypes.func,//考核难度选择回调
        callBackAssessResultValue: PropTypes.func,//考核结果选择回调
        callBackTimeValue: PropTypes.func,    //一天时间回调方法
        callBackMonthToThisMonthValue: PropTypes.func, //销售看板年月选择回调
    }

    static defaultProps = {
        cancel: '取消',
        // title: '请选择',
        confirm: '确认',
        pickerBg: [255, 255, 255, 1],
        startYear: 1900,
        // startYear:new Date().getFullYear(),
        endYear: 2030,
        synchronousRefresh: false,
        currentYear: parseInt((moment(new Date()).format('YYYY'))),
        currentMonth: parseInt((moment(new Date()).format('MM')))
    }

    constructor(props) {
        super(props);
        this.state = {
            xPosition: new Animated.Value(0),
            isShow: false,      //弹框是否显示
            selectedValue: []   //选择的值  address: ['河北', '唐山', '古冶区']   date: ['2018年', '1月', '1日']
        }
    }

    componentWillMount() {
        if (Platform.OS === 'android') {
            BackHandler.addEventListener('hardwareBackPress', this.onBackAndroid);
        }
    }
    componentWillUnmount() {

        if (Platform.OS === 'android') {
            BackHandler.removeEventListener('hardwareBackPress', this.onBackAndroid);
        }
    }

    onBackAndroid = () => {
        if (this.state.isShow) {
            this.hide();
            return true;
        }
        return false
    };

    /**
     *  在外部调用 显示角色
     * @param pickedValue ['2018年', '1月', '1日']
     */
    showRole(pickedValue, roleData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showRolePicker(roleData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示院区showHospital
     * @param pickedValue ['2018年', '1月', '1日']
     */
    showHospital(pickedValue, hospitalData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showHospitalPicker(hospitalData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示角色
     * @param pickedValue ['2018年', '1月', '1日']
     */
    showPharmacy(pickedValue, pharmacyData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showPharmacyPicker(pharmacyData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示地址
     * @param pickedValue ['河北', '唐山', '古冶区']
     */
    showAddress(pickedValue) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showAreaPicker()
            this.showAnimal()
            this.state.isShow = true
        }
    }


    /**
     *  在外部调用 显示地址
     * @param pickedValue ['2018年', '1月', '1日']
     */
    showDate(pickedValue) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showDatePicker()
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示地址
     * @param pickedValue ['2018年', '1月', '1日']
     */
    showMonthToThisMonth(pickedValue) {
        if (!this.state.isShow) {
            console.log("pickedValue==", pickedValue)
            this.state.selectedValue = pickedValue
            this._showMonthToThisMonthPicker()
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示地址
     * @param pickedValue ['2018年', '1月', '1日']
     */
    showMonth(pickedValue) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showMonthPicker()
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示砖型
     * @param pickedValue ['青砖100*40']
     */
    showBrickType(pickedValue, brickTypeData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showBrickTypePicker(brickTypeData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示订单
     * @param pickedValue ['青砖100*40']
     */
    showOrder(pickedValue, orderData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showOrderPicker(orderData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示订单
     * @param pickedValue ['青砖100*40']
     */
    showCustomer(pickedValue, customerData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showCustomerPicker(customerData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示供应商
     * @param pickedValue ['青砖100*40']
     */
    showSupplier(pickedValue, supplierData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showSupplierPicker(supplierData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示订单
     * @param pickedValue ['青砖100*40']
     */
    showContract(pickedValue, contractData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showContractPicker(contractData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示订单
     * @param pickedValue ['青砖100*40']
     */
    showWorkingShift(pickedValue, workingShiftData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showWorkingShiftPicker(workingShiftData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示员工
     * @param pickedValue ['青砖100*40']
     */
    showStaff(pickedValue, staffData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showStaffPicker(staffData)
            this.showAnimal()
            this.state.isShow = true
        }
    }
    /**
         *  在外部调用 显示单位名称
         * @param pickedValue ['青砖100*40']
         */
    showMedicalInsuranceType(pickedValue, medicalInsuranceTypeData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showMedicalInsuranceTypePicker(medicalInsuranceTypeData)
            this.showAnimal()
            this.state.isShow = true
        }
    }
    /**
     *  在外部调用 显示学历
     * @param pickedValue ['青砖100*40']
     */
    showEducation(pickedValue, educationData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showEducationPicker(educationData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示审核人
     * @param pickedValue ['青砖100*40']
     */
    showAudit(pickedValue, auditStaffData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showAuditStaffPicker(auditStaffData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示专业
     * @param pickedValue ['青砖100*40']
     */
    showMajor(pickedValue, majorData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showMajorPicker(majorData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示班级
     * @param pickedValue ['青砖100*40']
     */
    showClass(pickedValue, classData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showClassPicker(classData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示政治面貌
     * @param pickedValue ['青砖100*40']
     */
    showPoliticalStatus(pickedValue, classData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showPoliticalStatus(classData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
 *  在外部调用 显示英语能力
 * @param pickedValue ['青砖100*40']
 */
    showEnglishLevel(pickedValue, classData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showEnglishLevel(classData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
 *  在外部调用 显示系统配置
 * @param pickedValue ['青砖100*40']
 */
    showParamCode(pickedValue, paramCodeData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showParamCode(paramCodeData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示组织
     * @param pickedValue ['青砖100*40']
     */
    showOrg(pickedValue, orgData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showOrgPicker(orgData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示岗位类型
     * @param pickedValue ['青砖100*40']
     */
    showPositionType(pickedValue, positionTypeData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showPositionType(positionTypeData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示公司
     * @param pickedValue ['青砖100*40']
     */
    showEnterprise(pickedValue, educationData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showEnterprisePicker(educationData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
 *  在外部调用 显示申请类型
 * @param pickedValue ['青砖100*40']
 */
    showAuditType(pickedValue, educationData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showAuditTypePicker(educationData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示外协厂
     * @param pickedValue ['青砖100*40']
     */
    showOutsourcingTenant(pickedValue, outsourcingTenantData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showOutsourcingTenant(outsourcingTenantData)
            this.showAnimal()
            this.state.isShow = true
        }
    }
    /**
     *     在外部调用 显示积分类别
     * @param pickedValue ['青砖100*40']
     */
    showPointClassId(pickedValue, pointClassIdData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showPointClassIdPicker(pointClassIdData)
            this.showAnimal()
            this.state.isShow = true
        }
    }
    /**
     *     在外部调用 显示课程类型
     * @param pickedValue ['青砖100*40']
     */
    showCourseTypeId(pickedValue, courseTypeIdData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showCourseTypeIdPicker(courseTypeIdData)
            this.showAnimal()
            this.state.isShow = true
        }
    }
    /**
     *     在外部调用 显示课程所属职级
     * @param pickedValue ['青砖100*40']
     */
    showCourseLevelId(pickedValue, courseLevelIdData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showCourseLevelIdPicker(courseLevelIdData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *     在外部调用 显示考试日期
     * @param pickedValue ['青砖100*40']
     */
    showExamDate(pickedValue, examDateData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showExamDatePicker(examDateData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *     在外部调用 显示考试时间
     * @param pickedValue ['青砖100*40']
     */
    showExamTime(pickedValue, examTimeData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showExamTimePicker(examTimeData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     * 在外部调用 显示课程名称
     * @param pickedValue ['青砖100*40']
     */
    showCourseName(pickedValue, courseName) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showCourseNamePicker(courseName)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     * 在外部调用 显示部门名称
     * @param pickedValue ['青砖100*40']
     */
    showDepartmentName(pickedValue, departmentName) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showDepartmentNamePicker(departmentName)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     * 在外部调用 显示出库类型名称
     * @param pickedValue ['青砖100*40']
     */
    showStorageOutTypeName(pickedValue, storageOutTypeName) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showStorageOutTypeNamePicker(storageOutTypeName)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示职位权重
     * @param pickedValue ['青砖100*40']
     */
    showPowerLevel(pickedValue, powerLevelData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showPowerLevel(powerLevelData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示单位
     * @param pickedValue ['青砖100*40']
     */
    showUnit(pickedValue, unitData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showUnitPicker(unitData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示是否上架
     * @param pickedValue ['青砖100*40']
     */
    showIsUpProduct(pickedValue, stateData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showIsUpProduct(stateData)
            this.showAnimal()
            this.state.isShow = true
        }
    }
    /**
    *  在外部调用 显示是否现货
    * @param pickedValue ['青砖100*40']
    */
    showIsSpotFlag(pickedValue, spotFlagStateData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showIsSpotFlag(spotFlagStateData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
    *  在外部调用 显示销售月份
    * @param pickedValue ['青砖100*40']
    */
    showSalesMonth(pickedValue, salesMonthData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showSalesMonth(salesMonthData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示考核难度
     * @param pickedValue ['青砖100*40']
     */
    showAssessDifficulty(pickedValue, assessDifficultyData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showAssessDifficulty(assessDifficultyData)
            this.showAnimal()
            this.state.isShow = true
        }
    }

    /**
     *  在外部调用 显示考核结果
     * @param pickedValue ['青砖100*40']
     */
    showAssessResult(pickedValue, assessResultData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showAssessResult(assessResultData)
            this.showAnimal()
            this.state.isShow = true
        }
    }


    /**
    *  在外部调用 显示考核类别
    * @param pickedValue ['青砖100*40']
    */
    showAssessClassId(pickedValue, assessClassIdData) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showAssessClassId(assessClassIdData)
            this.showAnimal()
            this.state.isShow = true
        }
    }


    /**
        *  在外部调用 显示地址
        * @param pickedValue ['2018年', '1月', '1日']
        */
    showTime(pickedValue) {
        if (!this.state.isShow) {
            this.state.selectedValue = pickedValue
            this._showTimePicker()
            this.showAnimal()
            this.state.isShow = true
        }
    }


    /**
     * 隐藏地址
     */
    hide() {
        if (this.state.isShow) {
            Picker.hide()
            this.hideAnimal()
            this.state.isShow = false
        }
    }

    /**
     * 地址确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmRoleValue(pickedValue) {
        this.props.callBackRoleValue && this.props.callBackRoleValue(pickedValue)
    }
    /**
     * 地址确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmHospitalValue(pickedValue) {
        this.props.callBackHospitalValue && this.props.callBackHospitalValue(pickedValue)
    }
    /**
     * 地址确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmPharmacyValue(pickedValue) {
        this.props.callBackPharmacyValue && this.props.callBackPharmacyValue(pickedValue)
    }
    /**
     * 地址确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmAddressValue(pickedValue) {
        this.props.callBackAddressValue && this.props.callBackAddressValue(pickedValue)
    }

    /**
     * 砖型确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmBrickTypeValue(pickedValue) {
        console.log("=======confirmBrickTypeValue=pickedValue:", pickedValue);
        this.props.callBackBrickTypeValue && this.props.callBackBrickTypeValue(pickedValue)
    }

    /**
     * 订单确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmOrderValue(pickedValue) {
        console.log("=======confirmOrderValue=pickedValue:", pickedValue);
        this.props.callBackOrderValue && this.props.callBackOrderValue(pickedValue)
    }

    /**
    * 客户确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmCustomerValue(pickedValue) {
        console.log("=======confirmCustomerValue=pickedValue:", pickedValue);
        this.props.callBackCustomerValue && this.props.callBackCustomerValue(pickedValue)
    }

    /**
    * 供应商确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmSupplierValue(pickedValue) {
        console.log("=======confirmSupplierValue=pickedValue:", pickedValue);
        this.props.callBackSupplierValue && this.props.callBackSupplierValue(pickedValue)
    }

    /**
     * 班次确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmWorkingShiftValue(pickedValue) {
        console.log("=======confirmWorkingShiftValue=pickedValue:", pickedValue);
        this.props.callBackWorkingShiftValue && this.props.callBackWorkingShiftValue(pickedValue)
    }

    /**
     * 员工确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmStaffValue(pickedValue) {
        console.log("=======confirmStaffValue=pickedValue:", pickedValue);
        this.props.callBackStaffValue && this.props.callBackStaffValue(pickedValue)
    }

    /**
     * 合同确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmContractValue(pickedValue) {
        console.log("=======confirmContractValue=pickedValue:", pickedValue);
        this.props.callBackContractValue && this.props.callBackContractValue(pickedValue)
    }

    /**
     * 时间确认值
     * @param pickedValue  ['2018年', '1月', '1日']
     */
    confirmDateValue(pickedValue) {
        this.props.callBackDateValue && this.props.callBackDateValue(pickedValue)
    }

    /**
    * 月份确认值
    * @param pickedValue  ['2023年', '1月',]
    */
    confirmMonthToThisMonthValue(pickedValue) {
        this.props.callBackMonthToThisMonthValue && this.props.callBackMonthToThisMonthValue(pickedValue)
    }

    /**
     * 月份确认值
     * @param pickedValue  ['2018年', '1月', '1日']
     */
    confirmMonthValue(pickedValue) {
        this.props.callBackMonthValue && this.props.callBackMonthValue(pickedValue)
    }

    /**
    * 学历确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmEducationValue(pickedValue) {
        console.log("=======confirmEducationValue=pickedValue:", pickedValue);
        this.props.callBackEducationValue && this.props.callBackEducationValue(pickedValue)
    }

    /**
   * 审核人确认值
   * @param pickedValue  ['河北', '唐山', '古冶区']
   */
    confirmAuditStaffValue(pickedValue) {
        console.log("=======confirmAuditStaffValue=pickedValue:", pickedValue);
        this.props.callBackAuditStaffValue && this.props.callBackAuditStaffValue(pickedValue)
    }

    /**
     * 专业确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmMajorValue(pickedValue) {
        console.log("=======confirmMajorValue=pickedValue:", pickedValue);
        this.props.callBackMajorValue && this.props.callBackMajorValue(pickedValue)
    }

    /**
     * 班级确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmClassValue(pickedValue) {
        console.log("=======confirmClassValue=pickedValue:", pickedValue);
        this.props.callBackClassValue && this.props.callBackClassValue(pickedValue)
    }

    /**
     * 政治面貌确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmPoliticalStatusValue(pickedValue) {
        console.log("=======confirmClassValue=pickedValue:", pickedValue);
        this.props.callBackPoliticalStatusValue && this.props.callBackPoliticalStatusValue(pickedValue)
    }

    /**
 * 英语能力确认值
 * @param pickedValue  ['河北', '唐山', '古冶区']
 */
    confirmEnglishLevelValue(pickedValue) {
        console.log("=======confirmEnglishLevel=pickedValue:", pickedValue);
        this.props.callBackEnglishLevelValue && this.props.callBackEnglishLevelValue(pickedValue)
    }

    /**
 * 系统配置确认值
 * @param pickedValue  ['河北', '唐山', '古冶区']
 */
    confirmParamCodeValue(pickedValue) {
        console.log("=======confirmParamCode=pickedValue:", pickedValue);
        this.props.callBackParamCodeValue && this.props.callBackParamCodeValue(pickedValue)
    }

    /**
     * 组织确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmOrgValue(pickedValue) {
        console.log("=======confirmOrgValue=pickedValue:", pickedValue);
        this.props.callBackOrgValue && this.props.callBackOrgValue(pickedValue)
    }

    /**
     * 岗位类型确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmPositionTypeValue(pickedValue) {
        console.log("=======confirmPositionTypeValue=pickedValue:", pickedValue);
        this.props.callBackPositionTypeValue && this.props.callBackPositionTypeValue(pickedValue)
    }

    /**
     * 公司确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmEnterpriseValue(pickedValue) {
        console.log("=======confirmEnterpriseValue=pickedValue:", pickedValue);
        this.props.callBackEnterpriseValue && this.props.callBackEnterpriseValue(pickedValue)
    }

    /**
     * 申请类型确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmAuditTypeValue(pickedValue) {
        console.log("=======confirmAuditTypeValue=pickedValue:", pickedValue);
        this.props.callBackAuditTypeValue && this.props.callBackAuditTypeValue(pickedValue)
    }

    /**
     * 外协厂确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmOutsourcingTenantValue(pickedValue) {
        console.log("=======confirmOutsourcingTenantValue=pickedValue:", pickedValue);
        this.props.callBackOutsourcingTenantValue && this.props.callBackOutsourcingTenantValue(pickedValue)
    }

    /**
    * 积分类别确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmPointClassIdValue(pickedValue) {
        console.log("=======confirmPointClassIdValue=pickedValue:", pickedValue);
        this.props.callBackPointClassIdValue && this.props.callBackPointClassIdValue(pickedValue)
    }
    /**
    * 课程类型确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmCourseTypeIdValue(pickedValue) {
        console.log("=======confirmCourseTypeIdValue=pickedValue:", pickedValue);
        this.props.callBackCourseTypeIdValue && this.props.callBackCourseTypeIdValue(pickedValue)
    }
    /**
    * 课程所属职级确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmCourseLevelIdValue(pickedValue) {
        console.log("=======confirmCourseLevelIdValue=pickedValue:", pickedValue);
        this.props.callBackCourseLevelIdValue && this.props.callBackCourseLevelIdValue(pickedValue)
    }

    /**
    * 积分类别确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmExamDateValue(pickedValue) {
        console.log("=======confirmExamDateValue=pickedValue:", pickedValue);
        this.props.callBackExamDateValue && this.props.callBackExamDateValue(pickedValue)
    }

    /**
    * 积分类别确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmExamTimeValue(pickedValue) {
        console.log("=======confirmExamTimeValue=pickedValue:", pickedValue);
        this.props.callBackExamTimeValue && this.props.callBackExamTimeValue(pickedValue)
    }

    /**
    * 课程名称确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmCourseNameValue(pickedValue) {
        console.log("=======confirmCourseNameValue=pickedValue:", pickedValue);
        this.props.callBackCourseValue && this.props.callBackCourseValue(pickedValue)
    }

    /**
    * 部门名称确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmDepartmentNameValue(pickedValue) {
        console.log("=======confirmDepartmentNameValue=pickedValue:", pickedValue);
        this.props.callBackDepartmentNameValue && this.props.callBackDepartmentNameValue(pickedValue)
    }

    /**
    * 出库类型名称确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmStorageOutTypeNameValue(pickedValue) {
        console.log("=======confirmStorageOutTypeNameValue=pickedValue:", pickedValue);
        this.props.callBackStorageOutTypeNameValue && this.props.callBackStorageOutTypeNameValue(pickedValue)
    }

    /**
 * 职位权重确认值
 * @param pickedValue  ['河北', '唐山', '古冶区']
 */
    confirmPowerLevelValue(pickedValue) {
        console.log("=======confirmPowerLevelValue+=pickedValue:", pickedValue);
        this.props.callBackPowerLevelValue && this.props.callBackPowerLevelValue(pickedValue)
    }

    /**
     * 单位确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmUnitValue(pickedValue) {
        console.log("=======confirmUnitValue=pickedValue:", pickedValue);
        this.props.callBackUnitValue && this.props.callBackUnitValue(pickedValue)
    }
    /**
    * 医保类型确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmMedicalInsuranceTypeValue(pickedValue) {
        console.log("=======confirmMedicalInsuranceTypeValue=pickedValue:", pickedValue);
        this.props.callBackMedicalInsuranceTypeValue && this.props.callBackMedicalInsuranceTypeValue(pickedValue)
    }

    /**
     * 是否上下架产品确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmStateValue(pickedValue) {
        console.log("=======confirmStateValue=pickedValue:", pickedValue);
        this.props.callBackIsUpProductValue && this.props.callBackIsUpProductValue(pickedValue)
    }
    /**
    * 是否上现货产品确认值
    * @param pickedValue  ['河北', '唐山', '古冶区']
    */
    confirmSpotFlagValue(pickedValue) {
        console.log("=======confirmSpotFlagValue=pickedValue:", pickedValue);
        this.props.callBackIsSpotFlagValue && this.props.callBackIsSpotFlagValue(pickedValue)
    }

    /**
     * 销售月份确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmSalesMonthValue(pickedValue) {
        console.log("=======confirmSpotFlagValue=confirmSalesMonthValue:", pickedValue);
        this.props.callBackSalesMonthValue && this.props.callBackSalesMonthValue(pickedValue)
    }

    /**
     * 考核类别确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmAssessClassIdValue(pickedValue) {
        console.log("=======confirmSpotFlagValue=confirmAssessClassIdValue:", pickedValue);
        this.props.callBackAssessClassIdValue && this.props.callBackAssessClassIdValue(pickedValue)
    }
    /**    
     * 考核难度确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmAssessDifficultyValue(pickedValue) {
        console.log("=======confirmSpotFlagValue=confirmAssessDifficultyValue:", pickedValue);
        this.props.callBackAssessDifficultyValue && this.props.callBackAssessDifficultyValue(pickedValue)
    }

    /**
     * 考核结果确认值
     * @param pickedValue  ['河北', '唐山', '古冶区']
     */
    confirmAssessResultValue(pickedValue) {
        console.log("=======confirmSpotFlagValue=confirmAssessResultValue:", pickedValue);
        this.props.callBackAssessResultValue && this.props.callBackAssessResultValue(pickedValue)
    }

    /**
     * 时间确认值
     * @param pickedValue  ['2018年', '1月', '1日']
     */
    confirmTimeValue(pickedValue) {
        console.log("=======confirmTimeValue=confirmTimeValue:", pickedValue);
        this.props.callBackTimeValue && this.props.callBackTimeValue(pickedValue)
    }

    // 显示角色选择
    _showRolePicker(roleData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createRoleData(roleData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                this.confirmRoleValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    this.confirmRoleValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示院区选择
    _showHospitalPicker(hospitalData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createHospitalData(hospitalData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                this.confirmHospitalValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    this.confirmHospitalValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    _showPharmacyPicker(pharmacyData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createPharmacyData(pharmacyData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                this.confirmPharmacyValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    this.confirmPharmacyValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示区域选择
    _showAreaPicker() {
        Picker.init({
            pickerData: this._createAreaData(),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                this.confirmAddressValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    this.confirmAddressValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 日期选择
    _showDatePicker() {
        Picker.init({
            pickerData: this._createDateDataNoChinese(),
            selectedValue: this.state.selectedValue,
            pickerFontColor: [0, 0, 0, 1],
            onPickerConfirm: (pickedValue, pickedIndex) => {
                this.confirmDateValue(pickedValue)
                this.hide()
            },
            onPickerCancel: (pickedValue, pickedIndex) => {
                this.hide()
            },
            onPickerSelect: (pickedValue, pickedIndex) => {
                if (this.props.synchronousRefresh) {
                    this.confirmDateValue(pickedValue)
                }
            },
            // pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            pickerConfirmBtnColor:[30,110,250,1],
            pickerCancelBtnColor:[64,73,86,1],
            pickerToolBarBg:[255,255,255,1],
            pickerBg:[255,255,255,1],
            ...this.props
        });
        Picker.show();
    }

    _showMonthToThisMonthPicker() {
        Picker.init({
            // pickerTextEllipsisLen:20,
            pickerData: this._createMonthToThisMonthData(),
            pickerFontColor: [0, 0, 0, 1],
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("selectedValue", this.state.selectedValue);
                this.confirmMonthToThisMonthValue(pickedValue)
                this.hide()
            },
            onPickerCancel: (pickedValue, pickedIndex) => {
                this.hide()
            },
            onPickerSelect: (pickedValue, pickedIndex) => {
                if (this.props.synchronousRefresh) {
                    console.log("=========pickedMonthValue", pickedValue);
                    this.confirmMonthToThisMonthValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示月份选择
    _showMonthPicker() {
        Picker.init({
            // pickerTextEllipsisLen:20,
            pickerData: this._createMonthDate(),
            pickerFontColor: [0, 0, 0, 1],
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log(pickedValue);
                this.confirmMonthValue(pickedValue)
                this.hide()
            },
            onPickerCancel: (pickedValue, pickedIndex) => {
                this.hide()
            },
            onPickerSelect: (pickedValue, pickedIndex) => {
                if (this.props.synchronousRefresh) {
                    console.log("=========pickedMonthValue", pickedValue);
                    this.confirmMonthValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示砖型选择
    _showBrickTypePicker(brickTypeData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createBrickTypeData(brickTypeData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showBrickTypePicker", pickedValue);
                this.confirmBrickTypeValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showBrickTypePicker", pickedValue);
                    this.confirmBrickTypeValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示砖型选择
    _showOrderPicker(orderData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createOrderData(orderData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showOrderPicker==Confirm", pickedValue);
                this.confirmOrderValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showOrderPicker==Confirm", pickedValue);
                    this.confirmOrderValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示砖型选择
    _showCustomerPicker(customerData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createCustomerData(customerData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showCustomerPicker==Confirm", pickedValue);
                this.confirmCustomerValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showCustomerPicker==Confirm", pickedValue);
                    this.confirmCustomerValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示供应商选择
    _showSupplierPicker(supplierData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createSupplierData(supplierData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showSupplierPicker==Confirm", pickedValue);
                this.confirmSupplierValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showSupplierPicker==Confirm", pickedValue);
                    this.confirmSupplierValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示班次选择
    _showWorkingShiftPicker(workingShiftData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createWorkingShiftData(workingShiftData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showWorkingShiftPicker==Confirm", pickedValue);
                this.confirmWorkingShiftValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showWorkingShiftPicker==Confirm", pickedValue);
                    this.confirmWorkingShiftValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示员工选择
    _showStaffPicker(staffData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createStaffData(staffData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showStaffPicker==Confirm", pickedValue);
                this.confirmStaffValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showStaffPicker==Confirm", pickedValue);
                    this.confirmStaffValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }
    // 显示单位名称选择
    _showMedicalInsuranceTypePicker(medicalInsuranceTypeData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createMedicalInsuranceTypeData(medicalInsuranceTypeData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showMedicalInsuranceTypePicker==Confirm", pickedValue);
                this.confirmMedicalInsuranceTypeValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showMedicalInsuranceTypePicker==Confirm", pickedValue);
                    this.confirmMedicalInsuranceTypeValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示合同选择
    _showContractPicker(contractData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createContractData(contractData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showContractPicker==Confirm", pickedValue);
                this.confirmContractValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showContractPicker==Confirm", pickedValue);
                    this.confirmContractValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示学历选择
    _showEducationPicker(educationData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createEducationData(educationData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showEducationPicker==Confirm", pickedValue);
                this.confirmEducationValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showEducationPicker==Confirm", pickedValue);
                    this.confirmEducationValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示审核人选择
    _showAuditStaffPicker(auditStaffData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createAuditStaffData(auditStaffData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showAuditStaffPicker==Confirm", pickedValue);
                this.confirmAuditStaffValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showEducationPicker==Confirm", pickedValue);
                    this.confirmAuditStaffValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示专业选择
    _showMajorPicker(majorData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createMajorData(majorData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showMajorPicker==Confirm", pickedValue);
                this.confirmMajorValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showMajorPicker==Confirm", pickedValue);
                    this.confirmMajorValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示班级选择
    _showClassPicker(classData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createClassData(classData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showClassPicker==Confirm", pickedValue);
                this.confirmClassValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showClassPicker==Confirm", pickedValue);
                    this.confirmClassValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示政治面貌
    _showPoliticalStatus(politicalStatusData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createPoliticalStatusData(politicalStatusData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showPoliticalStatus==Confirm", pickedValue);
                this.confirmPoliticalStatusValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showPoliticalStatus==Confirm", pickedValue);
                    this.confirmPoliticalStatusValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示英语能力
    _showEnglishLevel(EnglishLevelData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createEnglishLevelData(EnglishLevelData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showEnglishLevel==Confirm", pickedValue);
                this.confirmEnglishLevelValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showEnglishLevel==Confirm", pickedValue);
                    this.confirmEnglishLevelValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示系统配置
    _showParamCode(ParamCodeData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createParamCodeData(ParamCodeData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showParamCode==Confirm", pickedValue);
                this.confirmParamCodeValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showParamCode==Confirm", pickedValue);
                    this.confirmParamCodeValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示组织选择
    _showOrgPicker(orgData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createOrgData(orgData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showOrgPicker==Confirm", pickedValue);
                this.confirmOrgValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showOrgPicker==Confirm", pickedValue);
                    this.confirmOrgValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示岗位类型选择
    _showPositionType(positionTypeData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createPositionTypeData(positionTypeData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showPositionType==Confirm", pickedValue);
                this.confirmPositionTypeValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showPositionType==Confirm", pickedValue);
                    this.confirmPositionTypeValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示申请类型选择
    _showAuditTypePicker(auditTypeData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createAuditTypeData(auditTypeData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showAuditTypePicker==Confirm", pickedValue);
                this.confirmAuditTypeValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showAuditTypePicker==Confirm", pickedValue);
                    this.confirmAuditTypeValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示申请类型选择
    _showOutsourcingTenant(outsourcingTenantData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createOutsourcingTenantData(outsourcingTenantData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showOutsourcingTenant==Confirm", pickedValue);
                this.confirmOutsourcingTenantValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showAuditTypePicker==Confirm", pickedValue);
                    this.confirmOutsourcingTenantValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示岗位类型选择
    _showEnterprisePicker(enterpriseData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createEnterpriseData(enterpriseData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showEnterprisePicker==Confirm", pickedValue);
                this.confirmEnterpriseValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showEnterprisePicker==Confirm", pickedValue);
                    this.confirmEnterpriseValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示积分类别选择
    _showPointClassIdPicker(pointClassIdData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createPointClassIdData(pointClassIdData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showPointClassIdPicker==Confirm", pickedValue);
                this.confirmPointClassIdValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showPointClassIdPicker==Confirm", pickedValue);
                    this.confirmPointClassIdValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }
    // 显示课程类型选择
    _showCourseTypeIdPicker(courseTypeIdData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createCourseTypeIdData(courseTypeIdData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showCourseTypeIdPicker==Confirm", pickedValue);
                this.confirmCourseTypeIdValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showCourseTypeIdPicker==Confirm", pickedValue);
                    this.confirmCourseTypeIdValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }
    // 显示课程所属职级
    _showCourseLevelIdPicker(courseLevelIdData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createCourseLevelIdData(courseLevelIdData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showCourseLevelIdPicker==Confirm", pickedValue);
                this.confirmCourseLevelIdValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showCourseLevelIdPicker==Confirm", pickedValue);
                    this.confirmCourseLevelIdValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示考试日期选择
    _showExamDatePicker(examDateData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createExamDateData(examDateData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showExamDatePicker==Confirm", pickedValue);
                this.confirmExamDateValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showPointClassIdPicker==Confirm", pickedValue);
                    this.confirmExamDateValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示考试时间选择
    _showExamTimePicker(examTimeData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createExamTimeData(examTimeData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showExamTimePicker==Confirm", pickedValue);
                this.confirmExamTimeValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showPointClassIdPicker==Confirm", pickedValue);
                    this.confirmExamTimeValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示课程名称
    _showCourseNamePicker(courseData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createCourseData(courseData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showCourseNamePicker==Confirm", pickedValue);
                this.confirmCourseNameValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showCourseNamePicker==Confirm", pickedValue);
                    this.confirmCourseNameValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示部门名称
    _showDepartmentNamePicker(courseData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createDepartmentNameData(courseData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showDepartmentNamePicker==Confirm", pickedValue);
                this.confirmDepartmentNameValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showDepartmentNamePicker==Confirm", pickedValue);
                    this.confirmDepartmentNameValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示出库类型名称
    _showStorageOutTypeNamePicker(courseData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createStorageOutTypeNameData(courseData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showStorageOutTypeNamePicker==Confirm", pickedValue);
                this.confirmStorageOutTypeNameValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showStorageOutTypeNamePicker==Confirm", pickedValue);
                    this.confirmStorageOutTypeNameValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示权重
    _showPowerLevel(courseData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createPowerLevelData(courseData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showPowerLevelPicker==Confirm", pickedValue);
                this.confirmPowerLevelValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                //Picker.select(['山东', '青岛', '黄岛区'])
                if (this.props.synchronousRefresh) {
                    console.log("=========_showPowerLevelPicker==Confirm", pickedValue);
                    this.confirmPowerLevelValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            pickerConfirmBtnColor:[30,110,250,1],
            pickerCancelBtnColor:[64,73,86,1],
            pickerToolBarBg:[255,255,255,1],
            pickerBg:[255,255,255,1],
            ...this.props
        });
        Picker.show();
    }

    // 显示单位选择
    _showUnitPicker(unitData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createUnitData(unitData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showUnitPicker==Confirm", pickedValue);
                this.confirmUnitValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showUnitPicker==Confirm", pickedValue);
                    this.confirmUnitValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示单位选择
    _showIsUpProduct(stateData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createStateData(stateData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showIsUpProduct==Confirm", pickedValue);
                this.confirmStateValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showIsUpProduct==Confirm", pickedValue);
                    this.confirmStateValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }
    // 显示单位选择
    _showIsSpotFlag(spotFlagStateData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createSpotFlagStateData(spotFlagStateData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showIsSpotFlag==Confirm", pickedValue);
                this.confirmSpotFlagValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showIsSpotFlag==Confirm", pickedValue);
                    this.confirmSpotFlagValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示销售月份选择
    _showSalesMonth(salesMonthData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createSalesMonthData(salesMonthData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showSalesMonth==Confirm", pickedValue);
                this.confirmSalesMonthValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showSalesMonth==Confirm", pickedValue);
                    this.confirmSalesMonthValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }


    // 显示考核类别选择
    _showAssessClassId(assessClassIdData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createAssessClassIdData(assessClassIdData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showAssessClassId==Confirm", pickedValue);
                this.confirmAssessClassIdValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showAssessClassId==Confirm", pickedValue);
                    this.confirmAssessClassIdValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }
    // 显示考核难度选择
    _showAssessDifficulty(assessDifficultyData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createAssessDifficultyData(assessDifficultyData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showAssessDifficulty==Confirm", pickedValue);
                this.confirmAssessDifficultyValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showAssessDifficulty==Confirm", pickedValue);
                    this.confirmAssessDifficultyValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 显示考核结果选择
    _showAssessResult(assessResultData) {
        Picker.init({
            pickerTextEllipsisLen: 20,
            pickerData: this._createAssessResultData(assessResultData),
            selectedValue: this.state.selectedValue,
            onPickerConfirm: pickedValue => {
                console.log("=========_showAssessResult==Confirm", pickedValue);
                this.confirmAssessResultValue(pickedValue)
                this.hide()
            },
            onPickerCancel: pickedValue => {
                this.hide()
            },
            onPickerSelect: pickedValue => {
                if (this.props.synchronousRefresh) {
                    console.log("=========_showAssessClassId==Confirm", pickedValue);
                    this.confirmAssessClassIdValue(pickedValue)
                    console.log("=========_showAssessResult==Confirm", pickedValue);
                    this.confirmAssessResultValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 日期选择
    _showTimePicker() {
        Picker.init({
            pickerData: this._createTimeDataNoChinese(),
            selectedValue: this.state.selectedValue,
            pickerFontColor: [0, 0, 0, 1],
            onPickerConfirm: (pickedValue, pickedIndex) => {
                this.confirmTimeValue(pickedValue)
                this.hide()
            },
            onPickerCancel: (pickedValue, pickedIndex) => {
                this.hide()
            },
            onPickerSelect: (pickedValue, pickedIndex) => {
                if (this.props.synchronousRefresh) {
                    this.confirmTimeValue(pickedValue)
                }
            },
            pickerBg: this.props.pickerBg,
            pickerCancelBtnText: this.props.cancel,
            pickerConfirmBtnText: this.props.confirm,
            pickerTitleText: this.props.title,
            ...this.props
        });
        Picker.show();
    }

    // 创建角色数据
    _createRoleData(roleDataSource) {
        let data = [];
        let len = 0;
        if (roleDataSource && roleDataSource.length) {
            len = roleDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(roleDataSource[i]["roleName"]);
        }
        console.log("===_createRoleData:", JSON.stringify(data));
        return data;
    }
    // 创建院区数据
    _createHospitalData(hospitalDataSource) {
        let data = [];
        let len = 0;
        if (hospitalDataSource && hospitalDataSource.length) {
            len = hospitalDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(hospitalDataSource[i]["hospitalName"]);
        }
        console.log("===_createHospitalData:", JSON.stringify(data));
        return data;
    }

    _createPharmacyData(pharmacyDataSource) {
        let data = [];
        let len = 0;
        if (pharmacyDataSource && pharmacyDataSource.length) {
            len = pharmacyDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(pharmacyDataSource[i]["pharmacyName"]);
        }
        console.log("===_createPharmacyData:", JSON.stringify(data));
        return data;
    }

    // 创建地区数据
    _createAreaData() {
        let data = [];
        let len = area.length;
        for (let i = 0; i < len; i++) {
            let city = [];
            for (let j = 0, cityLen = area[i]['city'].length; j < cityLen; j++) {
                let _city = {};
                _city[area[i]['city'][j]['name']] = area[i]['city'][j]['area'];
                city.push(_city);
            }
            let _data = {};
            _data[area[i]['name']] = city;
            data.push(_data);
        }
        return data;
    }

    // 创建日期数据
    _createDateData() {
        let date = [];
        for (let i = this.props.startYear - 1; i < this.props.endYear; i++) {
            let month = [];
            for (let j = 1; j < 13; j++) {
                let day = [];
                if (j === 2) {
                    for (let k = 1; k < 29; k++) {
                        if (k >= 10) {
                            day.push(k + '日');
                        }
                        else {
                            day.push('0' + k + '日');
                        }
                    }
                    //Leap day for years that are divisible by 4, such as 2000, 2004
                    if (i % 4 === 0) {
                        day.push(29 + '日');
                    }
                }
                else if (j in { 1: 1, 3: 1, 5: 1, 7: 1, 8: 1, 10: 1, 12: 1 }) {
                    for (let k = 1; k < 32; k++) {
                        if (k >= 10) {
                            day.push(k + '日');
                        }
                        else {
                            day.push('0' + k + '日');
                        }
                    }
                }
                else {
                    for (let k = 1; k < 31; k++) {
                        if (k >= 10) {
                            day.push(k + '日');
                        }
                        else {
                            day.push('0' + k + '日');
                        }
                    }
                }
                let _month = {};
                if (j >= 10) {
                    _month[j + '月'] = day;
                }
                else {
                    _month['0' + j + '月'] = day;
                }
                month.push(_month);
            }
            let _date = {};
            _date[i + '年'] = month;
            date.push(_date);
        }
        return date;
    }

    _createMonthToThisMonthData() {
        let date = [];
        for (let i = this.props.currentYear - 2; i < this.props.currentYear + 1; i++) {
            let month = [];
            if (i == this.props.currentYear) {
                for (let j = 1; j < this.props.currentMonth + 1; j++) {
                    month.push(j + '月');
                }
            }
            else {
                for (let j = 1; j < 13; j++) {
                    month.push(j + '月');
                }
            }

            let _date = {};
            _date[i + '年'] = month;
            date.push(_date);
        }
        // console.log(date);
        return date;
    }


    // 创建月份数据
    _createMonthDate() {
        let date = [];
        for (let i = this.props.startYear - 1; i < this.props.endYear; i++) {
            let month = [];
            for (let j = 1; j < 13; j++) {
                month.push(j + '月');
            }

            let _date = {};
            _date[i + '年'] = month;
            date.push(_date);
        }
        // console.log(date);
        return date;
    }

    // 创建日期数据
    _createDateDataNoChinese() {
        let date = [];
        for (let i = this.props.startYear - 1; i < this.props.endYear; i++) {
            let month = [];
            for (let j = 1; j < 13; j++) {
                let day = [];
                if (j === 2) {
                    for (let k = 1; k < 29; k++) {
                        if (k >= 10) {
                            day.push(k + '');
                        }
                        else {
                            day.push('0' + k + '');
                        }
                    }
                    //Leap day for years that are divisible by 4, such as 2000, 2004
                    if (i % 4 === 0) {
                        day.push(29 + '');
                    }
                }
                else if (j in { 1: 1, 3: 1, 5: 1, 7: 1, 8: 1, 10: 1, 12: 1 }) {
                    for (let k = 1; k < 32; k++) {
                        if (k >= 10) {
                            day.push(k + '');
                        }
                        else {
                            day.push('0' + k + '');
                        }
                    }
                }
                else {
                    for (let k = 1; k < 31; k++) {
                        if (k >= 10) {
                            day.push(k + '');
                        }
                        else {
                            day.push('0' + k + '');
                        }
                    }
                }
                let _month = {};
                if (j >= 10) {
                    _month[j + ''] = day;
                }
                else {
                    _month['0' + j + ''] = day;
                }
                month.push(_month);
            }
            let _date = {};
            _date[i + ''] = month;
            date.push(_date);
        }
        return date;
    }
    // 创建砖型数据
    _createBrickTypeData(brickTypeDataSource) {
        // let data = [];
        // let len = 0;
        // if (brickTypeDataSource && brickTypeDataSource.length) {
        //     len = brickTypeDataSource.length;
        // }
        // for(let i=0;i<len;i++){
        //     data.push(brickTypeDataSource[i]["brickTypeName"]+"_"+brickTypeDataSource[i]["brickTypeId"]+"");
        // }
        console.log("===_createBrickTypeData:", JSON.stringify(brickTypeDataSource));
        return brickTypeDataSource;
    }

    // 创建订单数据
    _createOrderData(orderDataSource) {
        let data = [];
        let len = 0;
        if (orderDataSource && orderDataSource.length) {
            len = orderDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(orderDataSource[i]["orderName"]);
        }
        console.log("===_createOrderData:", JSON.stringify(data));
        return data;
    }

    // 创建班次数据
    _createWorkingShiftData(workingShiftDataSource) {
        let data = [];
        let len = 0;
        if (workingShiftDataSource && workingShiftDataSource.length) {
            len = workingShiftDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(workingShiftDataSource[i]["shiftName"]);
        }
        console.log("===_createWorkingShiftData:", JSON.stringify(data));
        return data;
    }

    _createCustomerData(customerDataSource) {
        let data = [];
        let len = 0;
        if (customerDataSource && customerDataSource.length) {
            len = customerDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(customerDataSource[i]["customerName"]);
        }
        console.log("===_createOrderData:", JSON.stringify(data));
        return data;
    }

    //创建供应商数据
    _createSupplierData(supplierDataSource) {
        let data = [];
        let len = 0;
        if (supplierDataSource && supplierDataSource.length) {
            len = supplierDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            // if(supplierDataSource[i]["supplierAbbreviation"] != null && supplierDataSource[i]["supplierAbbreviation"] != ""){
            //     data.push(supplierDataSource[i]["supplierAbbreviation"]);
            // }
            // else{
            //     data.push(supplierDataSource[i]["supplierName"]);
            // }
            data.push(supplierDataSource[i]["supplierName"]);
        }
        console.log("===_createSupplierData:", JSON.stringify(data));
        return data;
    }

    _createContractData(contractDataSource) {
        let data = [];
        let len = 0;
        if (contractDataSource && contractDataSource.length) {
            len = contractDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(contractDataSource[i]["contractName"]);
        }
        console.log("===_createContractData:", JSON.stringify(data));
        return data;
    }

    _createStaffData(staffDataSource) {
        let data = [];
        let len = 0;
        if (staffDataSource && staffDataSource.length) {
            len = staffDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(staffDataSource[i]["staffName"]);
        }
        console.log("===_createStaffData:", JSON.stringify(data));
        return data;
    }
    // 创建医保类型数据
    _createMedicalInsuranceTypeData(medicalInsuranceTypeDataSource) {
        let data = [];
        let len = 0;
        if (medicalInsuranceTypeDataSource && medicalInsuranceTypeDataSource.length) {
            len = medicalInsuranceTypeDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(medicalInsuranceTypeDataSource[i]["medicalInsuranceTypeName"]);
        }
        console.log("===_createMedicalInsuranceTypeData:", JSON.stringify(data));
        return data;
    }

    // 创建学历数据
    _createEducationData(educationDataSource) {
        let data = [];
        let len = 0;
        if (educationDataSource && educationDataSource.length) {
            len = educationDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(educationDataSource[i]["educationName"]);
        }
        console.log("===_createEducationData:", JSON.stringify(data));
        return data;
    }

    // 创建审核人数据
    _createAuditStaffData(auditStaffDataSource) {
        let data = [];
        let len = 0;
        if (auditStaffDataSource && auditStaffDataSource.length) {
            len = auditStaffDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(auditStaffDataSource[i]["userName"]);
        }
        console.log("===_createAuditStaffData:", JSON.stringify(data));
        return data;
    }

    // 创建专业数据
    _createMajorData(majorDataSource) {
        let data = [];
        let len = 0;
        if (majorDataSource && majorDataSource.length) {
            len = majorDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(majorDataSource[i]["professionalName"]);
        }
        console.log("===_createMajorData:", JSON.stringify(data));
        return data;
    }

    // 创建班级数据
    _createClassData(classDataSource) {
        let data = [];
        let len = 0;
        if (classDataSource && classDataSource.length) {
            len = classDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(classDataSource[i]["className"]);
        }
        console.log("===_createClassData:", JSON.stringify(data));
        return data;
    }

    // 创建政治面貌数据
    _createPoliticalStatusData(politicalStatusDataSource) {
        let data = [];
        let len = 0;
        if (politicalStatusDataSource && politicalStatusDataSource.length) {
            len = politicalStatusDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(politicalStatusDataSource[i]["politicalStatusName"]);
        }
        console.log("===_createPoliticalStatusData:", JSON.stringify(data));
        return data;
    }

    // 创建英语能力数据
    _createEnglishLevelData(englishLevelDataSource) {
        let data = [];
        let len = 0;
        if (englishLevelDataSource && englishLevelDataSource.length) {
            len = englishLevelDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(englishLevelDataSource[i]["englishLevelName"]);
        }
        console.log("===_createEnglishLevelData:", JSON.stringify(data));
        return data;
    }

    // 创建系统配置数据
    _createParamCodeData(paramCodeDataSource) {
        let data = [];
        let len = 0;
        if (paramCodeDataSource && paramCodeDataSource.length) {
            len = paramCodeDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(paramCodeDataSource[i]["paramCodeName"]);
        }
        console.log("===_createParamCodeData:", JSON.stringify(data));
        return data;
    }

    // 创建组织数据
    _createOrgData(orgDataSource) {
        let data = [];
        let len = 0;
        if (orgDataSource && orgDataSource.length) {
            len = orgDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(orgDataSource[i]["orgName"]);
        }
        console.log("===_createOrgData:", JSON.stringify(data));
        return data;
    }

    // 创建岗位类型数据
    _createPositionTypeData(positionTypeDataSource) {
        let data = [];
        let len = 0;
        if (positionTypeDataSource && positionTypeDataSource.length) {
            len = positionTypeDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(positionTypeDataSource[i]["positionTypeName"]);
        }
        console.log("===_createPositionTypeData:", JSON.stringify(data));
        return data;
    }

    // 创建岗位类型数据
    _createEnterpriseData(enterpriseDataSource) {
        let data = [];
        let len = 0;
        if (enterpriseDataSource && enterpriseDataSource.length) {
            len = enterpriseDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            // if(enterpriseDataSource[i]["enterpriseAbbreviation"] == null || enterpriseDataSource[i]["enterpriseAbbreviation"] == ""){
            //     data.push(enterpriseDataSource[i]["enterpriseName"]);
            // }
            // else {
            //     data.push(enterpriseDataSource[i]["enterpriseAbbreviation"]);
            // }
            data.push(enterpriseDataSource[i]["enterpriseName"]);
        }
        console.log("===_createEnterpriseData:", JSON.stringify(data));
        return data;
    }

    // 创建申请类型数据
    _createAuditTypeData(auditTypeDataSource) {
        let data = [];
        let len = 0;
        if (auditTypeDataSource && auditTypeDataSource.length) {
            len = auditTypeDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(auditTypeDataSource[i]["auditTypeName"]);
        }
        console.log("===_createAuditTypeData:", JSON.stringify(data));
        return data;
    }

    // 创建外协厂数据
    _createOutsourcingTenantData(outsourcingTenantDataSource) {
        let data = [];
        let len = 0;
        if (outsourcingTenantDataSource && outsourcingTenantDataSource.length) {
            len = outsourcingTenantDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            if (outsourcingTenantDataSource[i]["outsourcingAbbreviation"] == null || outsourcingTenantDataSource[i]["outsourcingAbbreviation"] == "") {
                data.push(outsourcingTenantDataSource[i]["outsourcingName"]);
            }
            else {
                data.push(outsourcingTenantDataSource[i]["outsourcingAbbreviation"]);
            }
            // data.push(outsourcingTenantDataSource[i]["outsourcingName"]);
        }
        console.log("===_createOutsourcingTenantData:", JSON.stringify(data));
        return data;
    }

    // 创建积分类别数据
    _createPointClassIdData(pointClassIdDataSource) {
        let data = [];
        let len = 0;
        if (pointClassIdDataSource && pointClassIdDataSource.length) {
            len = pointClassIdDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(pointClassIdDataSource[i]["pointClassName"]);
        }
        console.log("===_createPointClassIdData:", JSON.stringify(data));
        return data;
    }
    // 创建课程类型数据
    _createCourseTypeIdData(courseTypeIdDataSource) {
        let data = [];
        let len = 0;
        if (courseTypeIdDataSource && courseTypeIdDataSource.length) {
            len = courseTypeIdDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(courseTypeIdDataSource[i]["courseTypeName"]);
        }
        console.log("===_createCourseTypeIdData:", JSON.stringify(data));
        return data;
    }
    // 创建课程所属职级数据
    _createCourseLevelIdData(courseLevelIdDataSource) {
        let data = [];
        let len = 0;
        if (courseLevelIdDataSource && courseLevelIdDataSource.length) {
            len = courseLevelIdDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(courseLevelIdDataSource[i]["courseLevelName"]);
        }
        console.log("===_createCourseLevelIdData:", JSON.stringify(data));
        return data;
    }

    // 创建考试日期数据
    _createExamDateData(examDateDataSource) {
        let data = [];
        let len = 0;
        if (examDateDataSource && examDateDataSource.length) {
            len = examDateDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(examDateDataSource[i]["examDate"]);
        }
        console.log("===_createExamDateData:", JSON.stringify(data));
        return data;
    }

    // 创建考试时间数据
    _createExamTimeData(examTimeDataSource) {
        let data = [];
        let len = 0;
        if (examTimeDataSource && examTimeDataSource.length) {
            len = examTimeDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(examTimeDataSource[i]["examTime"]);
        }
        console.log("===_createExamTimeData:", JSON.stringify(data));
        return data;
    }

    // 创建课程名称数据
    _createCourseData(courseDataSource) {
        let data = [];
        let len = 0;
        if (courseDataSource && courseDataSource.length) {
            len = courseDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(courseDataSource[i]["courseName"]);
        }
        console.log("===_createCourseData:", JSON.stringify(data));
        return data;
    }

    // 创建部门名称数据
    _createDepartmentNameData(departmentDataSource) {
        let data = [];
        let len = 0;
        if (departmentDataSource && departmentDataSource.length) {
            len = departmentDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(departmentDataSource[i]["departmentName"]);
        }
        console.log("===_createDepartmentNameData:", JSON.stringify(data));
        return data;
    }

    // 创建部门名称数据
    _createStorageOutTypeNameData(storageOutTypeDataSource) {
        let data = [];
        let len = 0;
        if (storageOutTypeDataSource && storageOutTypeDataSource.length) {
            len = storageOutTypeDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(storageOutTypeDataSource[i]["storageOutTypeName"]);
        }
        console.log("===_createStorageOutTypeNameData:", JSON.stringify(data));
        return data;
    }

    // 创建职位权重数据
    _createPowerLevelData(powerLevelDataSource) {
        let data = [];
        let len = 0;
        if (powerLevelDataSource && powerLevelDataSource.length) {
            len = powerLevelDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(powerLevelDataSource[i]["powerLevelId"]);
        }
        console.log("===_createPowerLevelData:", JSON.stringify(data));
        return data;
    }

    // 创建单位数据
    _createUnitData(unitDataSource) {
        let data = [];
        let len = 0;
        if (unitDataSource && unitDataSource.length) {
            len = unitDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(unitDataSource[i]["unitName"]);
        }
        console.log("===_createUnitData:", JSON.stringify(data));
        return data;
    }

    // 创建上下架状态数据
    _createStateData(stateDataSource) {
        let data = [];
        let len = 0;
        if (stateDataSource && stateDataSource.length) {
            len = stateDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(stateDataSource[i]["stateName"]);
        }
        console.log("===_createStateData:", JSON.stringify(data));
        return data;
    }
    // 创建上下架状态数据
    _createSpotFlagStateData(spotFlagStateDataSource) {
        let data = [];
        let len = 0;
        if (spotFlagStateDataSource && spotFlagStateDataSource.length) {
            len = spotFlagStateDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(spotFlagStateDataSource[i]["spotStateName"]);
        }
        console.log("===_createSpotFlagStateData:", JSON.stringify(data));
        return data;
    }

    // 创建上下架状态数据
    _createSalesMonthData(salesDataSource) {
        let data = [];
        let len = 0;
        if (salesDataSource && salesDataSource.length) {
            len = salesDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(salesDataSource[i]["monthName"]);
        }
        console.log("===_createSalesMonthData:", JSON.stringify(data));
        return data;
    }


    // 创建上下架状态数据
    _createAssessClassIdData(assessClassIdDataSource) {
        let data = [];
        let len = 0;
        if (assessClassIdDataSource && assessClassIdDataSource.length) {
            len = assessClassIdDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(assessClassIdDataSource[i]["assessClassName"]);
        }
        console.log("===_createAssessClassIdData:", JSON.stringify(data));
        return data;
    }
    // 创建考核难度数据
    _createAssessDifficultyData(assessDifficultyDataSource) {
        let data = [];
        let len = 0;
        if (assessDifficultyDataSource && assessDifficultyDataSource.length) {
            len = assessDifficultyDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(assessDifficultyDataSource[i]["assessDifficultyName"]);
        }
        console.log("===_createAssessDifficultyData:", JSON.stringify(data));
        return data;
    }

    // 创建考核结果数据
    _createAssessResultData(assessResultDataSource) {
        let data = [];
        let len = 0;
        if (assessResultDataSource && assessResultDataSource.length) {
            len = assessResultDataSource.length;
        }
        for (let i = 0; i < len; i++) {
            data.push(assessResultDataSource[i]["assessResultName"]);
        }
        console.log("===_createAssessResultData:", JSON.stringify(data));
        return data;
    }

    // 创建日期数据
    _createTimeDataNoChinese() {
        let time = [];
        for (let i = 0; i < 24; i++) {
            let minute = [];
            for (let j = 0; j < 60; j++) {
                if (j >= 10) {
                    minute.push(j + '');
                }
                else {
                    minute.push('0' + j + '');
                }
            }

            let _time = {};
            if (i >= 10) {
                _time[i + ''] = minute;
            }
            else {
                _time['0' + i + ''] = minute;
            }
            time.push(_time);
        }
        return time;
    }

    showAnimal() {
        Animated.timing(
            this.state.xPosition,
            {
                toValue: 1,
                easing: Easing.linear,
                duration: 300,
            }
        ).start()
    }

    hideAnimal() {
        Animated.timing(
            this.state.xPosition,
            {
                toValue: 0,
                easing: Easing.linear,
                duration: 200,
            }
        ).start()
    }

    render() {
        return (
            <Animated.View
                style={[styles.contain, {
                    transform: [{
                        translateY: this.state.xPosition.interpolate({
                            inputRange: [0, 1],
                            outputRange: [deviceInfo.deviceHeight, 0]
                        }),
                    }]
                }]}>
            </Animated.View>
        )
    }
}

const styles = StyleSheet.create({
    contain: {
        width: deviceInfo.deviceWidth,
        height: deviceInfo.deviceHeight,
        position: 'absolute',
        opacity: 0.3,
        backgroundColor: "#FFFFFF",
        // backgroundColor: 'rgba(0, 0, 0, 0.64)',
        left: 0,
        bottom: 0,
    },
});
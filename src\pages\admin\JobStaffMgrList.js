import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Image, TextInput,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyDepartmentStaffComponent from '../../component/EmptyDepartmentStaffComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
const {ifIphoneXContentViewHeight} = require('../../utils/ScreenUtil');

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class DepartmentStaffMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            jobId: null,
            jobName: "",
            jobAmount: 0,
            searchKeyWord: null,
            deleteModal:false,        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { jobId, jobName, jobAmount } = route.params;
            if (jobName) {
                this.setState({
                    jobName: jobName
                })
            }
            if (jobAmount) {
                this.setState({
                    jobAmount: jobAmount
                })
            }
            if (jobId) {
                this.setState({
                    jobId: jobId,
                })
                this.loadJobStaffList(jobId);
            }
        }
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/job/user/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "jobId": this.state.jobId,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/job/user/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "jobId": this.state.jobId,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadJobStaffList();
    }

    loadJobStaffList = (jobId) => {
        let url = "/biz/job/user/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "jobId": jobId ? jobId : this.state.jobId,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this.loadJobStaffListCallBack);
    }

    loadJobStaffListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord = () => {
        // let toastOpts;
        // if (!this.state.searchKeyWord) {
        //     toastOpts = getFailToastOpts("请输入客户名称或合同名称");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url = "/biz/job/user/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "jobId": this.state.jobId,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }


    deleteJobStaff = (jobUserId) => {
        console.log("=======delete=jobUserId", jobUserId);
        let url = "/biz/job/user/delete";
        let requestParams = { 'jobUserId': jobUserId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    renderRow = (item, index) => {
        return (
            <View key={item.jobUserId} style={{backgroundColor:'rgba(255, 255, 255, 1)'}}>
                
                <View style={[styles.inputRowStyle, { paddingLeft: 12, height: 57,}]}>
                    <View style={[{ width: screenWidth - (leftLabWidth + 25), paddingTop: 5, position: 'relative' }]}>
                        <View style={{ height: 48, width: 48, borderRadius: 20, marginTop: 4, paddingTop: 3, justifyContent: "center", alignItems: "center", backgroundColor: '#1E6EFA' }}>
                            {
                                item.staffName.length <= 2 ?
                                    <Text style={{ color: 'rgba(255,255,255,1)', fontSize: 17, fontFamily: 'PingFangSC-Regular', fontWeight: "normal", textAlign: 'center', lineHeight: 20 }}>
                                        {item.staffName}
                                    </Text>
                                    :
                                    <Text style={{ color: 'rgba(255,255,255,1)', fontSize: 17, fontFamily: 'PingFangSC-Regular', fontWeight: "normal", textAlign: 'center', lineHeight: 20, }}>
                                        {item.staffName.slice(-2)}
                                    </Text>
                            }
                        </View>
                        <View style={{ justifyContent: 'flex-start', width: screenWidth - 15, flexDirection: 'row', position: 'absolute', paddingTop: 13 }}>
                            <View style={{ flexDirection: 'row', marginLeft: 58 }}>
                                <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16,fontWeight:'500' }}>{item.staffName}</Text>
                            </View>
                        </View>

                        <View style={[{ flexDirection: 'row', justifyContent: 'flex-start', width: screenWidth - 15, flexDirection: 'row', paddingLeft: 58, position: 'absolute', paddingTop: 36 }]}>
                            <Image style={{ height: 13, width: 12, marginTop: 2, marginRight: 5 }} source={require('../../assets/icon/iconfont/telephone.png')}></Image>
                            <Text style={{ fontSize: 12,color:'rgba(0,10,32,0.65)' }}>{item.staffAccNbr}</Text>
                        </View>
                        
                    </View>
                    <View style={[styles.titleViewStyle, { position: 'absolute', right: 0, top: 10,marginLeft:100, flexDirection: 'column',justifyContent: 'flex-end', }]}>
                        <TouchableOpacity onPress={() => {
                            // 删除弹窗Modal
                            this.setState({
                                deleteModal: true,
                                deleteJobUserId:item.jobUserId
                            })
                            console.log("还没确定时的数据=========",item)
                        }}>
                            <Image style={{ width: 28, height: 28,marginLeft:100 }} source={require('../../assets/icon/iconfont/fire.png')}></Image>
                        </TouchableOpacity>
                    </View>
                    
                </View>
                <View style={styles.lineViewStyle}/> 
                
            </View>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyDepartmentStaffComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("JobStaffMgrAdd",
                    {
                        jobId: this.state.jobId,
                        jobName: this.state.jobName,
                        jobAmount: this.state.jobAmount,
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }
    render() {
        return (
            <View style={{backgroundColor: 'rgba(242, 245, 252, 1)'}}>
                <CommonHeadScreen title='人员管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{ marginTop: 0, index: 1000 ,backgroundColor:'rgba(255, 255, 255, 1)',}} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{flexDirection: 'row',justifyContent: 'space-between',marginLeft: 10,marginRight: 10,marginBottom: 5,marginTop: 5,backgroundColor:'rgba(255, 255, 255, 1)'}}>
                        <Text style={{ marginLeft: 10, fontWeight: '400',fontSize:16,color:'#2B333F', marginLeft:10 }}>职位名称：{this.state.jobName}</Text>
                        <Text style={{ marginLeft: 10, fontWeight: '400',fontSize:16,color:'#2B333F', marginLeft:10, }}>职位个数：{this.state.jobAmount}</Text>
                    </View>

                    <View style={[CommonStyle.singleSearchBox,{marginTop:10,height:42,borderBottomColor:'#E8E9EC',borderBottomWidth:1}]}>
                        <View style={CommonStyle.searchBoxWithoutOthers}>
                            <View>
                                <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={{ color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 5, paddingTop: 0, paddingBottom: 0, paddingRight: 0, paddingLeft: 0 }}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'员工姓名或联系电话'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                           
                        </View>
                    </View>
                </View>
                {/* <View style={{ height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight),backgroundColor: 'rgba(242, 245, 252, 1)' }}> */}
                <View style={{backgroundColor: 'rgba(242, 245, 252, 1)',height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}}>
                    <FlatList
                        data={this.state.dataSource}
                        // ItemSeparatorComponent={this.space}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>

                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认该员工离岗吗?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
            
                                    this.deleteJobStaff(this.state.deleteJobUserId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        // borderWidth: 1,
        // borderColor: "#FFFFFF",
        backgroundColor: "#FFFFFF",
        borderRadius: 5
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        paddingBottom: 5
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth:1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop: 0
    },

    // innerViewStyle: {
    //     // marginTop:10,
    //     borderColor: "#F4F4F4",
    //     borderWidth: 8,
    // },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC',
    },
});
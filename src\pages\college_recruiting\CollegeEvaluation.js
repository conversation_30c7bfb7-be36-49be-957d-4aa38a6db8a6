import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CrHeadScreen from '../../component/CrHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
export default class CollegeEvaluation extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            collegeEvaluation:"",
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
            console.log("==========" + constants.loginUser.tenantName);
            console.log("==========" + constants.loginUser.staffId);
            var staffId = constants.loginUser.staffId;

            let url= "/biz/cr/staff/get";
            let loadRequest={"staffId":staffId};
             httpPost(url, loadRequest, this.loadCollegeEvaluationCallBack);
             this.loadAdjustFactor();
        }
    }

    loadCollegeEvaluationCallBack = (response) => {
        if (response.code == 200 && response.data) {

            this.setState({
                collegeEvaluation:response.data.collegeEvaluation,
                
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:24, height:24}} source={require('../../assets/icon/iconfont/CrBack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("TemplateMgrAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}></Text>
            </TouchableOpacity>
        )
    }

    loadAdjustFactor=()=>{
        let url = "/biz/tenant/get";
        let loadRequest = {
            "operateTenantId":constants.loginUser.tenantId
        };
        httpPost(url, loadRequest, this.loadAdjustFactorCallBack);
    }

    loadAdjustFactorCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                adjustFactor: response.data.adjustFactor,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    render(){
        return(
            <View style={{backgroundColor:'#FFFFFF',height:screenHeight}}>
                <CrHeadScreen title='学院评价'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle,,{backgroundColor:'#FFF',borderTopColor:'#EEE',borderTopWidth:1}]}>
                    {/* <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle,{fontSize:18,marginTop:15,color:'#333333'}]}>学院评价</Text>
                    </View> */}
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle,{lineHeight:30}}>{this.state.collegeEvaluation ? this.state.collegeEvaluation : "暂无信息"}</Text>
                    </View>
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({

    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    titleTextStyle: {
        fontSize: 15, 
        color: '#00000073' ,
        marginTop:5,
    },
    titleViewStyle: {
        flexDirection: 'column',
        marginLeft: 20,
        marginTop: 15,
        marginBottom: 5,
        justifyContent:'center',
        width:screenWidth - 40,
        borderBottomColor:'#EEEEEE',
        borderBottomWidth:1
    },
    inputRowStyle: {
        height: 45,
        // flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputTextStyleTextStyle: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    },
    inputTextStyleTextStyleAutoHeight: {
        width: screenWidth - (leftLabWidth + 15),
        borderRadius: 0,
        borderColor: 'red',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        justifyContent: 'center'
    }

});
import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,KeyboardAvoidingView,
    FlatList,RefreshControl,Linking,Clipboard,ScrollView,TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CrHeadScreen from '../../component/CrHeadScreen'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
import { uploadImageLibrary } from '../../utils/UploadImageUtils';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class StudentMyInterView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            staffName:"",
            staffId:"",
            staffSex:"",
            staffSexType:"",
            selSexId:1,
            staffBirthday:"",
            selectStaffBirthday:[],
            staffTel: "",
            nationality:"",
            professionalName:"",
            gradePoint:"",
            comprehensiveAbility:"",
            comprehensivePoint:"",
            electronicPhotos:"",
            height:"",
            politicsStatus:"",
            englishLevelName:"",
            englishLevelCode:"",
            selectedEnglishLevel:[],
            englishLevelDataSource:[],
            sexDataSource:[
                {
                    sexId:1,
                    sexType:"男",
                    sexName:"M"
                },
                {
                    sexId:2,
                    sexType:"女",
                    sexName:"L"
                }
            ],
            resumeClass:"",
            selResumeClassId:"",
            selResumeClassName:"",
            resumeClassDataSource:[
                {
                    resumeClassId:1,
                    resumeClassType:"实习",
                    resumeClassName:"P"
                },
                {
                    resumeClassId:2,
                    resumeClassType:"全职",
                    resumeClassName:"E"
                }
            ],
            selSexName:"",
            selectedPoliticalStatus:[],
            graduateSchool:"",
            education:"",
            nativePlace:"",
            address:"",
            email:"",
            personalHonor:"",
            collegeEvaluation:"",
            adjustFactor:"",
            graduateInstitutions:"",
            crEducation:"",
            crProfessionalName:"",

            selExtList:[],
            extDataSource:[],
            extTitle0:"",
            extContent0:"",

            userPhotoUrl:"",
            userPhoto:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        // httpPost("/biz/cr/staff/getResume", { "staffId": constants.loginUser.staffId,"userId": constants.loginUser.userId }, (response) => {
        //     if (response.code === 200) {
        //         let userPhoto = response.data.electronicPhotos;
        //         this.setState({
        //             userPhoto : response.data.electronicPhotos,
        //             userPhotoUrl: constants.image_addr + '/' + userPhoto
        //         })
        //         console.log(  constants.image_addr+ '/' + userPhoto)
        //     }
        // });

        let url = "/biz/cr/staff/englishLevelList";
        let loadRequest = {};
        httpPost(url, loadRequest, this.loadEnglishLevelListCallBack);

        let politicalStatusDataSource =[
            {
                politicalStatusId:0,
                politicalStatusName:"中共党员"
            },
            {
                politicalStatusId:1,
                politicalStatusName:"中共预备党员"
            },
            {
                politicalStatusId:2,
                politicalStatusName:"共青团员"
            },
            {
                politicalStatusId:3,
                politicalStatusName:"群众"
            }
        ]

    
        this.setState({
            politicalStatusDataSource:politicalStatusDataSource,
        })
      
        const { route, navigation } = this.props;
        if (route && route.params) {

            // 当前时间
            var currentDate = new Date();
            var currentDateYear = ("0" + (currentDate.getFullYear())).slice(-4) + "年";
            var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2) + "月";
            var currentDate = currentDateYear + currentDateMonth;
            // var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
            // console.log("当前的年份:" + currentDateYear)
            // console.log("当前的月份:" + currentDateMonth)
            // console.log("当前的时间:",currentDate)
            var staffBirthdayYear = currentDate.slice(0,5);
            var staffBirthdayMonth = currentDate.split(staffBirthdayYear);
            // console.log("staffBirthdayYear:::",staffBirthdayYear);
            // console.log("staffBirthdayMonth:::",staffBirthdayMonth);
            this.setState({
                // 调整了关于时间的初始化赋值，之前的格式为[2022, "12"]不正确，调整后的格式为["2022年", "12月"]
                // 此处通过将156-170行注释来进行测试
                selectStaffBirthday:[staffBirthdayYear,staffBirthdayMonth[1]]
                //selectStaffBirthday:[staffBirthdayYear,staffBirthdayMonth[1]]
            })

            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
            console.log("==========" + constants.loginUser.tenantName);
            console.log("==========" + constants.loginUser.staffId);
            var staffId = constants.loginUser.staffId;

            let url= "/biz/cr/staff/getResume";
            let loadRequest={"staffId":staffId,"userId": constants.loginUser.userId};
             httpPost(url, loadRequest, this.loadCrStaffCallBack);
             this.loadAdjustFactor();
             //console.log("目前的日期",this.state.selectStaffBirthday)
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/cr/staff/getResume";
        let loadRequest={"staffId":constants.loginUser.staffId,"userId": constants.loginUser.userId};
        httpPost(url, loadRequest, this.loadCrStaffCallBack);
    }


    loadCrStaffCallBack=(response) =>{
        if (response.code == 200 && response.data) {
            console.log("==========" + response.data.staffName);
            this.setState({
                staffName:response.data.staffName,
                staffId:response.data.staffId,
                professionalName:response.data.professionalName,
                userPhoto : response.data.electronicPhotos,
                userPhotoUrl: constants.image_addr + '/' +  response.data.electronicPhotos,
                gradePoint:response.data.gradePoint/100,
                graduateInstitutions:response.data.graduateInstitutions ? response.data.graduateInstitutions : "暂无信息",
                education:response.data.education,
                staffTel:response.data.staffTel,
                politicsStatus:response.data.politicsStatus,
                selectedPoliticalStatus:[response.data.politicsStatus],
                englishLevelName:response.data.englishLevelName,
                englishLevelCode:response.data.englishLevel,
                selectedEnglishLevel:[response.data.englishLevelName],
                staffSex:response.data.staffSex,
                staffSexType:response.data.staffSex == 'L' ? "女" : "男",
                selSexId:response.data.staffSex == 'L' ? 2 : 1,
                resumeClass:response.data.resumeClass,
                resumeClassType:response.data.resumeClass == 'E' ? "全职" : "实习",
                selResumeClassId:response.data.resumeClass== 'E' ? 2 : 1,
                staffBirthday:response.data.staffBirthday,
                selectStaffBirthday:[response.data.staffBirthday],
                nationality:response.data.nationality,
                height:response.data.height,
                nativePlace:response.data.nativePlace,
                address:response.data.address,
                email:response.data.email,
                personalHonor:response.data.personalHonor ? response.data.personalHonor : "暂无信息" ,
                collegeEvaluation:response.data.collegeEvaluation ? response.data.collegeEvaluation : "暂无信息",
                electronicPhotos:response.data.electronicPhotos,
                extDataSource:response.data.crStaffExtDTOList,
                extTitle0:(response.data.crStaffExtDTOList && response.data.crStaffExtDTOList.length > 0 ) ? response.data.crStaffExtDTOList[0].extTitle : "暂无信息",
                extContent0:(response.data.crStaffExtDTOList && response.data.crStaffExtDTOList.length > 0 ) ? response.data.crStaffExtDTOList[0].extContent : "暂无信息",
                comprehensiveAbility:response.data.comprehensiveAbility/100,
                comprehensivePoint:response.data.comprehensivePoint/100,
                crEducation:response.data.crEducation ? response.data.crEducation : "暂无信息",
                crProfessionalName:response.data.crProfessionalName,

            })

            if (response.data.staffBirthday && response.data.staffBirthday.length) {
                console.log("返回的日期",response.data.staffBirthday)
                // var time = JSON.parse(response.data.staffBirthday);
                // var time = eval("(" + response.data.staffBirthday + ")");
                // // var time = response.data.staffBirthday.toString();
                // console.log("time:::",tmie);
                var staffBirthdayYear = response.data.staffBirthday.slice(0,5);
                var staffBirthdayMonth = response.data.staffBirthday.split(staffBirthdayYear);
                console.log("staffBirthdayYear:::",staffBirthdayYear);
                console.log("staffBirthdayMonth:::",staffBirthdayMonth);
                // var staffBirthday = staffBirthdayYear + staffBirthdayMonth[1]
                // var vartime;
                // for(var index=0;index<1;index++) {

                //     // vartime = time.;
                //     if (index===0) {
                //         staffBirthday += vartime;
                //     }
                //     else{
                //         staffBirthday += "" + vartime;
                //     }
                // }
                this.setState({
                    staffBirthday:response.data.staffBirthday,
                    selectStaffBirthday:[staffBirthdayYear,staffBirthdayMonth[1]]
                })
            }

            if (response.data.crStaffExtDTOList && response.data.crStaffExtDTOList.length > 0) {
                // 遍历拓展字段详情
                response.data.crStaffExtDTOList.forEach((item)=>{
                    var varExtCause={
                        extId:item.extId,
                        extTitle:item.extTitle,
                        extContent:item.extContent,
                    };
                    this.setState({
                       selExtList :this.state.selExtList.concat(varExtCause)
                    })
                })
            }
            console.log(this.state.selExtList)

        }
    }

    loadEnglishLevelListCallBack=(response) =>{
        if (response.code == 200 && response.data) {
            this.setState({
                englishLevelDataSource:response.data
            })
        }
    } 

    loadAdjustFactor=()=>{
        let url = "/biz/tenant/get";
        let loadRequest = {
            "operateTenantId":constants.loginUser.tenantId
        };
        httpPost(url, loadRequest, this.loadAdjustFactorCallBack);
    }

    loadAdjustFactorCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                adjustFactor: response.data.adjustFactor,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:24, height:24}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    openBirthdayTime(){

        console.log("当前的日期",this.state.selectStaffBirthday)
        // this.refs.SelectBirthday.showMonth(this.state.selectStaffBirthday)
        this.refs.SelectBirthday.showMonth(this.state.selectStaffBirthday)
    }

    openPoliticalStatus(){      
        this.refs.SelectPoliticalStatus.showPoliticalStatus(this.state.selectedPoliticalStatus,this.state.politicalStatusDataSource)
    }

    openEnglishLevel(){   
        
        if (!this.state.englishLevelDataSource || this.state.englishLevelDataSource.length < 1) {
            WToast.show({data:"暂无数据"});
            return
        }
        
        this.refs.SelectEnglishLevel.showEnglishLevel(this.state.selectedEnglishLevel,this.state.englishLevelDataSource)
    }

    callBackSelectBirthdayValue(value){
        console.log("==========出生年月选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectStaffBirthday:value
        })
        if (this.state.selectStaffBirthday && this.state.selectStaffBirthday.length) {
            var staffBirthday = "";
            var vartime;
            for(var index=0;index<this.state.selectStaffBirthday.length;index++) {
                vartime = this.state.selectStaffBirthday[index];
                if (index===0) {
                    staffBirthday += vartime;
                }
                else{
                    staffBirthday += "" + vartime;
                }
            }
            this.setState({
                staffBirthday:staffBirthday
            })
        }
    }
    
    callBackSelectPoliticalStatusValue(value){
        console.log("==========政治面貌选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedPoliticalStatus:value
        })
        var politicalStatusName = value.toString();
        this.setState({
            politicsStatus:politicalStatusName
        })
    }

    callBackSelectEnglishLevelValue(value){
        console.log("==========英语能力选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedEnglishLevel:value
        })
        var englishLevelName = value.toString();
        this.setState({
            englishLevelName:englishLevelName
        })
        let url= "/biz/cr/staff/englishLevelByName";
        let loadRequest={"englishLevelName":englishLevelName};
        httpPost(url, loadRequest, this.loadCrEnglishLevelCallBack);
    }

    loadCrEnglishLevelCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                englishLevelCode:response.data.englishLevel
            })
        }
    }

    saveMyInterView=()=>{
        console.log("=======saveMyInterView");
        let toastOpts;
        if (!this.state.staffName) {
            toastOpts = getFailToastOpts("请输入姓名");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selSexId) {
            toastOpts = getFailToastOpts("请选择性别");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.staffBirthday) {
            toastOpts = getFailToastOpts("请选择出生年月");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.nationality) {
            toastOpts = getFailToastOpts("请输入民族");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.height) {
            toastOpts = getFailToastOpts("请输入身高");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.politicsStatus) {
            toastOpts = getFailToastOpts("请选择政治面貌");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.professionalName) {
        //     toastOpts = getFailToastOpts("请输入专业");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.gradePoint) {
        //     toastOpts = getFailToastOpts("请输入绩点");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.englishLevelName) {
            toastOpts = getFailToastOpts("请选择英语能力");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.graduateInstitutions) {
            toastOpts = getFailToastOpts("请输入毕业院校");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.crEducation) {
            toastOpts = getFailToastOpts("请输入学历");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.nativePlace) {
            toastOpts = getFailToastOpts("请输入籍贯");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.address) {
            toastOpts = getFailToastOpts("请输入住址");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.staffTel) {
            toastOpts = getFailToastOpts("请输入联系电话");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.email) {
            toastOpts = getFailToastOpts("请输入电子邮箱");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selResumeClassId) {
            toastOpts = getFailToastOpts("请选择求职类型");
            WToast.show(toastOpts)
            return;
        }
        var ex = 0;
        let url= "/biz/cr/staff/modify";
        let _spExtDTOList = [];
        this.state.selExtList.map((elem, index)=>{
            var extDTO = {
                // "extId": elem.extId,
                "extTitle": elem.extTitle,
                "extContent":elem.extContent,
            }
            if (!elem.extTitle) {
                toastOpts = getFailToastOpts("请输入项目标题");
                WToast.show(toastOpts)
                ex = 1;
                return;
            }
            if (!elem.extContent) {
                toastOpts = getFailToastOpts("请输入项目内容");
                WToast.show(toastOpts)
                ex = 1;
                return;
            }
            _spExtDTOList.push(extDTO);
        })
        if (ex ==1 ) {
            return;
        }
        let requestParams={
            "staffId":this.state.staffId ? this.state.staffId : constants.loginUser.staffId,
            "staffName":this.state.staffName,
            "gradePoint":this.state.gradePoint*100,
            "politicsStatus":this.state.politicsStatus,
            "crProfessionalName":this.state.crProfessionalName,
            "englishLevel":this.state.englishLevelCode,
            "staffSex": this.state.sexDataSource[this.state.selSexId-1].sexName,
            "resumeClass": this.state.resumeClassDataSource[this.state.selResumeClassId-1].resumeClassName,
            "staffBirthday":this.state.staffBirthday,
            "nationality":this.state.nationality,
            "height":this.state.height,
            "nativePlace":this.state.nativePlace,
            "address":this.state.address,
            "email":this.state.email,
            "electronicPhotos":this.state.electronicPhotos,
            "crStaffExtDTOList": _spExtDTOList,
            "userId":constants.loginUser.userId,
            "staffTel":this.state.staffTel,
            "graduateInstitutions":this.state.graduateInstitutions,
            "crEducation":this.state.crEducation,
            "ext":'Y',
        };
        console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.saveMyInterViewCallBack);
    }

    saveMyInterViewCallBack=(response) =>{
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('更新完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
        }
    }

    exportPdfFile=()=> {
        console.log("=======exportPdfFile");
        let url= "/biz/generate/pdf/student_my_inter_view";
        let requestParams={
            "currentPage": 1,
            "pageSize": 15,
            "staffId":constants.loginUser.staffId,
            "userId":constants.loginUser.userId,
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data); 
                WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
                Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                    {
                        text:"不打开", onPress:()=>{
                        WToast.show({data:'点击了不打开'});
                        }
                    },
                    {
                        text:"打开", onPress:()=>{
                            WToast.show({data:'点击了打开'});
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }

    //sex列表展示
    renderSexRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selSexId:item.sexId,
                        staffSexType:item.sexType,
                        selSexName:item.selSexName,
                    })
                }}>
                <View key={item.sexId} style={[item.sexId===this.state.selSexId ? 
                    [CommonStyle.selectedBlockItemViewStyle,{backgroundColor:'rgba(83,100,255,0.1)'}] 
                    : 
                    [CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(83,100,255,0)'}]] }>
                    <Text style={item.sexId===this.state.selSexId ? 
                        {color:"#1E6EFA",fontSize:16}
                        : 
                        {color:'#a0a0a0',fontSize:16} }>
                        {item.sexType}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    //求职类型列表展示
    resumeClassRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selResumeClassId:item.resumeClassId,
                        resumeClassType:item.resumeClassType,
                        selResumeClassName:item.selResumeClassName,
                    })
                }}>
                <View key={item.resumeClassId} style={[item.resumeClassId===this.state.selResumeClassId ? 
                    [CommonStyle.selectedBlockItemViewStyle,{backgroundColor:'rgba(83,100,255,0.1)'}]
                    : 
                    [CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(83,100,255,0)'}]] }>
                    <Text style={item.resumeClassId===this.state.selResumeClassId ? 
                        {color:"#1E6EFA",fontSize:16}
                        : 
                        {color:'#a0a0a0',fontSize:16} }>
                        {item.resumeClassType}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        // 动态显示拓展字段数据
        var pages = [];
        for (var i = 0; i < this.state.selExtList.length; i++) {
            const _selExtList = _.cloneDeep(this.state.selExtList);
            _selExtList.map((elem, index)=>{
                elem._index = i;
                return elem;
            })
            pages.push(        
                <View key={"view_" +this.state.selExtList[i].extId+"_"+i}>

                    <View style={[CommonStyle.rowLabView,{flexDirection:"column",height:450}]}>

                    <View style={CommonStyle.rowLabLeftView}>
                            <Text style={CommonStyle.rowLabTextStyle}>标题</Text>
                        </View>
                        <View style={[{height:40}]}>
                            <TextInput 
                                multiline={true}
                                textAlignVertical="top"
                                placeholder={'请输入标题'}
                                style={[CommonStyle.inputRowText,{height:40}]}
                                extId={this.state.selExtList[i].extId}
                                onChange={(event) => {
                                    // 通过回调事件查看控件属性
                                    // var orderId = event.target._internalFiberInstanceHandleDEV.memoizedProps.orderId;
                                    var extId = event._dispatchInstances.memoizedProps.extId;
                                    var text = event.nativeEvent.text;
                                    var varselExt;
                                    for(var index=0; index<this.state.selExtList.length;index++){
                                        varselExt = this.state.selExtList[index];
                                        console.log(this.state.selExtList);
                                        
                                        if (extId === varselExt.extId) {
                                            varselExt.extTitle = text;
                                            this.state.selExtList[index] = varselExt;
                                            console.log("==数据更新==this.state.selExtList", this.state.selExtList);
                                        }
                                    }
                                }}
                            >
                                {this.state.selExtList[i].extTitle}
                            </TextInput>
                        </View>

                         <View style={CommonStyle.rowLabLeftView}>
                            <Text style={CommonStyle.rowLabTextStyle}>内容</Text>
                        </View>
                        <View style={[{height:300}]}>
                            <TextInput 
                                multiline={true}
                                textAlignVertical="top"
                                placeholder={'请输入内容'}
                                style={[CommonStyle.inputRowText,{height:300}]}
                                extId={this.state.selExtList[i].extId}
                                onChange={(event) => {
                                    // 通过回调事件查看控件属性
                                    // var orderId = event.target._internalFiberInstanceHandleDEV.memoizedProps.orderId;
                                    var extId = event._dispatchInstances.memoizedProps.extId;
                                    var text = event.nativeEvent.text;
                                    var varselExt;
                                    for(var index=0; index<this.state.selExtList.length;index++){
                                        varselExt = this.state.selExtList[index];
                                        if (extId === varselExt.extId) {
                                            varselExt.extContent = text;
                                            this.state.selExtList[index] = varselExt;
                                            console.log("==数据更新==this.state.selExtList", this.state.selExtList);
                                        }
                                    }
                                }}
                            >
                                {this.state.selExtList[i].extContent}
                            </TextInput>
                        </View>
                    </View>
                </View>
            );
        }

        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title='我的简历'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{borderWidth:3,marginTop:0, index:1000, flexWrap:'wrap', flexDirection:'row',justifyContent:"center"}]} onLayout={this.topBlockLayout.bind(this)}>
                    <TouchableOpacity onPress={()=>{
                            // var personalResume = {
                            //     "staffName":this.state.staffName,
                            //     "staffSex":this.state.staffSex,
                            //     "staffBirthday":this.state.staffBirthday,
                            // }
                            // console.log(this.state.selExtList);
                            this.props.navigation.navigate("StudentMyInterViewPreview", 
                            {
                                // 传递回调函数
                                refresh: this.callBackFunction,
                                staffName:this.state.staffName,
                                staffSex:this.state.staffSexType,
                                resumeClass:this.state.resumeClassType,
                                staffBirthday:this.state.staffBirthday,
                                nationality:this.state.nationality,
                                height:this.state.height,
                                politicsStatus:this.state.politicsStatus,
                                professionalName:this.state.professionalName,
                                comprehensivePoint:this.state.comprehensivePoint,
                                englishLevel:this.state.englishLevelName,
                                graduateInstitutions:this.state.graduateInstitutions,
                                education:this.state.education,
                                nativePlace:this.state.nativePlace,
                                address:this.state.address,
                                staffTel: this.state.staffTel,
                                email:this.state.email,
                                electronicPhotos:this.state.electronicPhotos,
                                personalHonor:this.state.personalHonor,
                                collegeEvaluation:this.state.collegeEvaluation,
                                selExtList:this.state.selExtList,
                                userPhotoUrl:this.state.userPhotoUrl,
                                crEducation:this.state.crEducation,
                                crProfessionalName:this.state.crProfessionalName,
                                userId:constants.loginUser.userId,
                                resumeClass:this.state.resumeClassType,
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{marginRight:100,width:85,backgroundColor:'#1BBC82',flexDirection:"row"}]}>
                             <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/preview.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>预 览</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要导出PDF文件吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.exportPdfFile()
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{backgroundColor: '#1E6EFA',width:85,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/output.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>导 出</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                <ScrollView style={[CommonStyle.formContentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>基本信息</Text>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>姓名</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入姓名'}
                            onChangeText={(text) => this.setState({staffName:text})}
                            // editable={false}
                        >
                            {this.state.staffName}
                        </TextInput>
                    </View>

                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>证件照</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[{ width: 120,height:150,marginLeft:0,marginBottom:10,display:'flex',justifyContent:'center',
                            alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                            <TouchableOpacity 
                                onPress={() => {
                                    uploadImageLibrary(this.state.userPhotoUrl, "user_header", (imageUploadResponse) => {
                                        console.log("========imageUploadResponse", imageUploadResponse)
                                        if (imageUploadResponse.code === 200) {
                                            WToast.show({ data: "成功上传" });
                                            let { compressFile } = imageUploadResponse.data
                                            this.setState({
                                                //goodsImageUrl:服务器地址+图片存储地址
                                                userPhotoUrl: constants.image_addr + '/' + compressFile,
                                                electronicPhotos:compressFile,
                                            })
                                        }
                                        else {
                                            WToast.show({ data: imageUploadResponse.message });
                                        }
                                    });
                            }}>
                                    {
                                        this.state.electronicPhotos ?
                                        <Image source={{ uri: this.state.userPhotoUrl }} style={{width:120,height:150,justifyContent:'center',alignItems:'center'}} />
                                        :
                                        <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                    }
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={[styles.rowLabView,{marginRight:60}]}>
                            <Text style={styles.leftLabNameTextStyle}>性别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row',marginLeft:5}}>
                            {
                                (this.state.sexDataSource && this.state.sexDataSource.length > 0) 
                                ? 
                                this.state.sexDataSource.map((item, index)=>{
                                    return this.renderSexRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出生年月</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openBirthdayTime()}>
                            {/* <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text>{this.state.schedulingProductionTime}</Text>
                            </View> */}
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.staffBirthday ? "请选择出生年月" : this.state.staffBirthday}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>民族</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入民族'}
                            onChangeText={(text) => this.setState({nationality:text})}
                        >
                            {this.state.nationality}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>身高(cm)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入身高'}
                            onChangeText={(text) => this.setState({height:text})}
                        >
                            {this.state.height}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>政治面貌</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openPoliticalStatus()}>
                            <View style={styles.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.politicsStatus ? "请选择政治面貌" : this.state.politicsStatus}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>专业</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        {/* 自己写的：crProfessionalName       从专业表关联的：professionalName */}
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入专业'}
                            onChangeText={(text) => this.setState({crProfessionalName:text})}
                            // editable={false}
                        >
                            {this.state.crProfessionalName ? this.state.crProfessionalName : this.state.professionalName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>专业绩点</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入专业绩点'}
                            onChangeText={(text) => this.setState({gradePoint:text})}
                            editable={false}
                        >
                            {this.state.gradePoint}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>实践绩点</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入实践绩点'}
                            onChangeText={(text) => this.setState({comprehensiveAbility:text})}
                            editable={false}
                        >
                            {this.state.comprehensiveAbility}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>综合绩点</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入综合绩点'}
                            onChangeText={(text) => this.setState({comprehensivePoint:text})}
                            editable={false}
                        >
                            {this.state.comprehensivePoint}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>英语能力</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openEnglishLevel()}>
                            <View style={styles.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.englishLevelName ? "请选择英语能力" : this.state.englishLevelName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>毕业院校</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入毕业院校'}
                            onChangeText={(text) => this.setState({graduateInstitutions:text})}
                            // editable={false}
                        >
                            {this.state.graduateInstitutions}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>学历</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入学历'}
                            onChangeText={(text) => this.setState({crEducation:text})}
                            // editable={false}
                        >
                            {this.state.crEducation ? this.state.crEducation : this.state.education}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>籍贯</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入籍贯'}
                            onChangeText={(text) => this.setState({nativePlace:text})}
                        >
                            {this.state.nativePlace}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>住址</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={[styles.inputRightText,{height:60}]}
                            multiline={true}
                            textAlignVertical="top"
                            placeholder={'请输入住址'}
                            onChangeText={(text) => this.setState({address:text})}
                        >
                            {this.state.address}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle,{marginTop:25}]}>
                        <View style={[styles.leftLabView]}>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={[styles.inputRightText]}
                            placeholder={'请输入联系电话'}
                            onChangeText={(text) => this.setState({staffTel:text})}
                            // editable={false}
                        >
                            {this.state.staffTel}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>电子邮箱</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入电子邮箱'}
                            onChangeText={(text) => this.setState({email:text})}
                        >
                            {this.state.email}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={[styles.rowLabView,{marginRight:60}]}>
                            <Text style={styles.leftLabNameTextStyle}>求职类型</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row',marginLeft:5}}>
                            {
                                (this.state.resumeClassDataSource && this.state.resumeClassDataSource.length > 0) 
                                ? 
                                this.state.resumeClassDataSource.map((item, index)=>{
                                    return this.resumeClassRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>个人荣誉</Text>
                    </View>
                    <View style={[styles.inputRowStyle,{height:300}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:300}]}
                            placeholder={'请输入个人荣誉'}
                            onChangeText={(text) => this.setState({personalHonor:text})}
                            editable={false}
                        >
                            {this.state.personalHonor}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>学院评价</Text>
                    </View>
                    <View style={[styles.inputRowStyle,{height:300}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:300}]}
                            placeholder={'请输入学院评价'}
                            onChangeText={(text) => this.setState({collegeEvaluation:text})}
                            editable={false}
                        >
                            {this.state.collegeEvaluation}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>新增项目</Text>
                    </View>
                    <View>
                        {
                            pages.map((elem, index) => {
                                return elem;
                            })
                        }
                    </View>

                    <View style={styles.btnRowView}>
                        <TouchableOpacity onPress={()=>{
                                console.log("==========this.state.selExtList.length:", this.state.selExtList.length);
                                if (this.state.selExtList.length >= 0) {
                                    var selIndex = this.state.selExtList.length;
                                    var varExtType={
                                        index:selIndex,
                                        extId:this.state.selExtList.length == 0 ?1:this.state.selExtList[selIndex - 1].extId + 1,
                                        extTitle:"",
                                        extContent:""
                                    };
                                    this.setState({
                                        selExtList:this.state.selExtList.concat(varExtType)
                                    })
                                    console.log("======selExtList:", this.state.selExtList.concat(varExtType))
                                }
                        }}>
                            <View style={styles.btnAddView}> 
                                <Text style={styles.btnAddText}>+ 新增项目</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                            if (!this.state.selExtList) {
                                WToast.show({data:"没有可删除的项目"});
                                return;
                            }
                            this.setState({
                                selExtList:this.state.selExtList.slice(0,-1)
                            })
                        }}>
                            <View style={styles.btnDeleteView}>
                                <Text style={styles.btnDeleteText}>— 删除</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveMyInterView.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect 
                        ref={'SelectBirthday'} 
                        callBackMonthValue={this.callBackSelectBirthdayValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectPoliticalStatus'} 
                        callBackPoliticalStatusValue={this.callBackSelectPoliticalStatusValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectEnglishLevel'} 
                        callBackEnglishLevelValue={this.callBackSelectEnglishLevelValue.bind(this)}
                    />
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        borderColor:"#FFFFFF",
        borderWidth:0,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:screenWidth - 120,
    },
    itemContentRightChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:120,
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight:30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputTextStyleTextStyle:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        height:45,
        justifyContent:'center'
    },
    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'rgba(83,100,255,0.1)', height:30, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:6
    },
    btnAddText:{
        color:"#1E6EFA",fontSize:15
    },
    btnDeleteView:{
        backgroundColor: "rgba(246,246,246,1)", height:30,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:6
    },
    btnDeleteText:{
        color:'#a0a0a0', fontSize:15
    },
});
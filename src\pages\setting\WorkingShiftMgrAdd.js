import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class WorkingShiftMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            operateTenantId:"",
            tenantName:"",
            operate:"",
            shiftId:"",
            shiftName:"",
            shiftSort:0,
            productionLineDataSource:[],
            selProductionLineId:"",
            shiftTypeDataSource:[],
            selShiftType:""
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadProductionLineList();
        
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { shiftId ,productionLineId, shiftType} = route.params;
            if (shiftId) {
                this.setState({
                    operate:"编辑",
                    shiftId:shiftId,
                    selProductionLineId:productionLineId,
                    selShiftType:shiftType
                })               
                console.log("====shiftType===="+shiftType);
                loadTypeUrl= "/biz/working/shift/get";
                loadRequest={'shiftId':shiftId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditWorkingShiftDataCallBack);
       
            }
            else {
                this.setState({
                    operate:"新增",
                })
            }
        }
        this.loadShiftTypeList();
    }

    loadProductionLineList=()=>{
        // 生产车间列表
        let url= "/biz/production/line/list";
        let loadRequest={'currentPage':1, 'pageSize':1000};
        httpPost(url, loadRequest, this.callBackLoadProductionLine);
    }

    callBackLoadProductionLine=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            let productionLineDataSource = response.data.dataList;
            let selProductionLineId = response.data.dataList[0].productionLineId;
            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                selProductionLineId = constants.loginUser.spUserExtDTO.productionLineId;
            }

            if(this.state.selProductionLineId) {
                selProductionLineId = this.state.selProductionLineId;
            }

            this.setState({
                productionLineDataSource:productionLineDataSource,
                selProductionLineId:selProductionLineId,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadShiftTypeList=()=>{
        let url= "/biz/working/shift/type/list";
        let loadRequest={'currentPage':1, 'pageSize':1000};
        httpPost(url, loadRequest, this.callBackShiftTypeList);
    }

    callBackShiftTypeList=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                shiftTypeDataSource:response.data,
                selShiftType:this.state.shiftId?this.state.selShiftType:response.data[0].shiftType
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditWorkingShiftDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {

            this.setState({
                shiftName:response.data.shiftName,
                shiftSort:response.data.shiftSort
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("WorkingShiftMgrList")
            }}>
                <Text style={CommonStyle.headRightText}>班次管理</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveContractCollectMoneyPoint =()=> {
        console.log("=======saveWorkingShift");
        let toastOpts;
        if (!this.state.shiftName) {
            toastOpts = getFailToastOpts("请填写班次名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selProductionLineId) {
            toastOpts = getFailToastOpts("请选择生产车间");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selShiftType) {
            toastOpts = getFailToastOpts("请填写所属部门");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/working/shift/add";
        if (this.state.shiftId) {
            console.log("=========Edit===shiftId", this.state.shiftId)
            url= "/biz/working/shift/modify";
        }
        let requestParams={
            shiftId:this.state.shiftId,
            shiftName: this.state.shiftName,
            shiftSort:this.state.shiftSort,
            productionLineId:this.state.selProductionLineId,
            shiftType:this.state.selShiftType
        };
        httpPost(url, requestParams, this.saveWorkingShiftCallBack);
    }

    // 保存回调函数
    saveWorkingShiftCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 车间单项渲染
    renderProductionLineRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.shiftId) {
                    return;
                }
                this.setState({
                    selProductionLineId:item.productionLineId,
                }) 
                console.log("=======变化的id" + this.state.selProductionLineId);
            }}>
                
                
                <View key={item.productionLineId} style={[item.productionLineId===this.state.selProductionLineId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle,this.state.shiftId? CommonStyle.disableViewStyle : ''] }>
                    <Text style={item.productionLineId===this.state.selProductionLineId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.productionLineName}
                    </Text>
                </View>
            </TouchableOpacity>

        )
    }

    renderShiftTypeRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.shiftId) {
                    return;
                }
                this.setState({
                    selShiftType:item.shiftType,
                }) 
                console.log("=======变化的id" + item.shiftType);
            }}>
                <View key={item.shiftId} style={[item.shiftType===this.state.selShiftType? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle,this.state.shiftId? CommonStyle.disableViewStyle : ''] }>
                    <Text style={item.shiftType===this.state.selShiftType? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.shiftTypeName}
                    </Text>
                </View>
            </TouchableOpacity>

        )
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + "班次"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>班次名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入班次名称'}
                            onChangeText={(text) => this.setState({shiftName:text})}
                        >
                            {this.state.shiftName}
                        </TextInput>
                    </View>
                    {
                        this.state.productionLineDataSource.length == 1 ?
                        <View></View>
                        :
                        <View>
                            <View style={[styles.inputRowStyle]}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>生产车间</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                          </View>
                            <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                                {
                                    (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0) 
                                    ? 
                                    this.state.productionLineDataSource.map((item, index)=>{
                                        return this.renderProductionLineRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                        </View>
                        
                    }

                        <View>
                            <View style={[styles.inputRowStyle]}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>所属部门</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                          </View>
                            <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                                {
                                    (this.state.shiftTypeDataSource && this.state.shiftTypeDataSource.length > 0) 
                                    ? 
                                    this.state.shiftTypeDataSource.map((item, index)=>{
                                        return this.renderShiftTypeRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                        </View>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({shiftSort:text})}
                        >
                            {this.state.shiftSort}


                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveContractCollectMoneyPoint.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }

})
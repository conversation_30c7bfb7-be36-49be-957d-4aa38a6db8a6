import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
var CommonStyle = require('../../assets/css/CommonStyle');
import moment from 'moment';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
var searchDate = (moment(new Date()).format('YYYY-MM-DD'));
export default class ExamApplyAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            examPersonName:"",
            examPersonPhoto:"",
            contactNbr:"",
            identityCardNbr:"",
            birthAte:"",
            registrationNumber:"",
            examCourse:"",
            examId:"",
            examTime:"",
            examDate:"",
            examPersonEmail:"",
            selectBirthAte: [],
            examDateDataSource:[],
            selectExamDate:[],
            examTimeDataSource:[],
            selectExamTime:[],
            courseDataSource:[
                {
                    courseId:1,
                    courseName:"F1"
                },
                {
                    courseId:2,
                    courseName:"F2"
                },
                {
                    courseId:3,
                    courseName:"F3"
                },
                {
                    courseId:4,
                    courseName:"F4"
                }
            ],
            roleId:"",
            selCourseId:1,
            selCourseName:"F1",
            goodsImage:"",
            goodsImageUrl:constants.image_addr + constants.loginUser.examPersonPhoto,
            auditExplain:""
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;

        // var dateString = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        var dateString = (moment(new Date()).format('YYYY-MM-DD'));
        dateString = dateString.substring(0, 19);
        dateString = dateString.replace(/-/g, '/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var OneMoreDay= new Date(dateStringTimestamp);
        //获取当前时间的毫秒数
        var currentDate = new Date();
        var nowMilliSeconds = currentDate.getTime();
        // 用获取毫秒数 加上七天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        OneMoreDay.setTime(nowMilliSeconds + 86400000);
        //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
        //格式化月，如果小于9，前面补0  
        var OneDayLastOfMonth = ("0" + (OneMoreDay.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0  
        var OneDayLastOfDay = ("0" + OneMoreDay.getDate()).slice(-2);
        
        loadTypeUrl = "/biz/exam/config/examDateList";
        loadRequest = {
        'currentPage': 1,
        'pageSize': 1000,
        'searchDate':OneMoreDay.getFullYear() + "-" + OneDayLastOfMonth + "-" + OneDayLastOfDay
        };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadExamDateData);

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { applyId } = route.params;
            if (applyId) {
                console.log("=============applyId" + applyId + "");
                this.setState({
                    applyId:applyId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/exam/apply/get";
                loadRequest={'applyId':applyId};
                httpPost(loadTypeUrl, loadRequest, this.callBackLoadExamApplyData);
            }
            else{
                this.setState({
                    operate:"新增",
                    examPersonName:constants.loginUser.userName,
                    contactNbr:constants.loginUser.userNbr,
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectBirthAte:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    birthAte:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
        this.loadRoleList();
    }

    callBackLoadExamDateData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                examDateDataSource: response.data
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    callBackLoadExamApplyData=(response)=>{
        if (response.code == 200 && response.data) {
            var selectBirthAte = response.data.birthAte.split("-");
            console.log("======load==edit=obj=", response.data);
            this.setState({
                applyId:response.data.applyId,
                examPersonName:response.data.examPersonName,
                examPersonPhoto:response.data.examPersonPhoto,
                contactNbr:response.data.contactNbr,
                identityCardNbr:response.data.identityCardNbr,
                birthAte:response.data.birthAte,
                selectBirthAte:selectBirthAte,
                registrationNumber:response.data.registrationNumber,
                examCourse:response.data.examCourse,
                selCourseName:response.data.examCourse,
                examId:response.data.examId,
                selectExamDate:[response.data.examDate],
                examDate:response.data.examDate,
                selectExamTime:[response.data.examTime],
                examTime:response.data.examTime,
                examPersonEmail:response.data.examPersonEmail,
                goodsImageUrl:constants.image_addr + '/' + response.data.examPersonPhoto,
            })
            this.loadExamTimeListByDate(response.data.examDate);
            console.log("=======this.state", this.state);
        }
    }

    loadRoleList=()=>{
        let url= "/biz/role/get";
        let loadRequest={
            "roleId":constants.loginUser.roleId
        };
        httpPost(url, loadRequest,(response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    roleSort:response.data.roleSort
                })
            }
        });
        console.log("=======user-roleId", constants.loginUser.roleId);
    }

    saveExam =()=> {
        console.log("=======saveCustomer");
        let toastOpts;
        if (!this.state.examPersonName) {
            toastOpts = getFailToastOpts("请输入姓名");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.examPersonPhoto) {
            toastOpts = getFailToastOpts("请上传证件照");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contactNbr) {
            toastOpts = getFailToastOpts("请输入联系方式");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.identityCardNbr) {
            toastOpts = getFailToastOpts("请输入身份证号");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.registrationNumber) {
            toastOpts = getFailToastOpts("请输入注册号");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selCourseId) {
            toastOpts = getFailToastOpts("请选择报考科目");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.examDate) {
            toastOpts = getFailToastOpts("请选择考试日期");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.examTime) {
            toastOpts = getFailToastOpts("请选择考试时间");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.examPersonEmail) {
            toastOpts = getFailToastOpts("请输入邮箱");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/exam/apply/add";
        if(this.state.applyId) {
            console.log("=========Edit===applyId", this.state.applyId)
            url= "/biz/exam/apply/modify";
        }
        let requestParams={
            applyId:this.state.applyId,
            examId:this.state.examId,
            examPersonName:this.state.examPersonName,
            examPersonPhoto:this.state.examPersonPhoto,
            contactNbr:this.state.contactNbr,
            identityCardNbr:this.state.identityCardNbr,
            birthAte:this.state.birthAte,
            registrationNumber:this.state.registrationNumber,
            examCourse:this.state.selCourseName,
            examPersonEmail:this.state.examPersonEmail,
            internalFlag:this.state.roleSort ===99 ? "N" : "Y",
            auditState:"1",
            auditExplain:""
    };
        httpPost(url, requestParams, this.saveExamCallBack);
    }

    // 保存回调函数
    saveExamCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    //科目列表展示
    renderRow=(item)=>{
    return (
        <TouchableOpacity onPress={() => {
                this.setState({
                    selCourseId:item.courseId,
                    selCourseName:item.courseName
                })
                console.log("=======  " +item.courseName);
            }}>
                
            <View key={item.courseId} style={[item.courseName===this.state.selCourseName ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                <Text style={item.courseName===this.state.selCourseName ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                    {item.courseName}
                </Text>
            </View>
        </TouchableOpacity>
    )
}
    
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("ExamApply")
            }}>
                <Text style={CommonStyle.headRightText}>我的报名</Text>
            </TouchableOpacity>
        )
    }
   
    openBirthAte() {
        this.refs.SelectBirthAte.showDate(this.state.selectBirthAte)
    }
    callBackOpenBirthAteValue(value) {
        console.log("==========生日选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectBirthAte: value
        })
        if (value && value.length) {
            var birthAte = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    birthAte += vartime;
                }
                else{
                    birthAte += "-" + vartime;
                }
            }
            this.setState({
                birthAte:birthAte
            })
        }
    }

    openExamDate() {
        if (!this.state.examDateDataSource || this.state.examDateDataSource.length < 1) {
            WToast.show({ data: "暂无考试日期安排" });
            return
        }
        this.setState({
            examTimeDataSource: []
        })
        this.refs.SelectExamDate.showExamDate(this.state.selectExamDate, this.state.examDateDataSource)
    }

    openExamTime() {
        if (this.state.examDate) {
            if (!this.state.examTimeDataSource || this.state.examTimeDataSource.length < 1) {
                WToast.show({ data: "无时间安排" });
                return;
            }
            this.refs.SelectExamTime.showExamTime(this.state.selectExamTime, this.state.examTimeDataSource)
        }
        else {
            WToast.show({ data: "请先选择考试日期" });
            return;
        }
    }

    callBackExamDateValue(value) {
        console.log("==========考试日期选择结果：", value)
        if (!value) {
            return;
        }
        var examDate = value.toString();
        this.setState({
            selectExamDate: value,
            selectExamTime: [],
            examTime: "",
            examDate: examDate
        })
        this.loadExamTimeListByDate(examDate);
    }

    loadExamTimeListByDate=(examDate)=>{
        let loadUrl= "/biz/exam/config/list";
        let loadRequest={
            "currentPage":1,
            "pageSize":1000,
            "examDate":examDate
        };
        httpPost(loadUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    examTimeDataSource:response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    callBackExamTimeValue(value) {
        console.log("==========考试时间选择的结果：", value)
        if (!value) {
            return;
        }
        var examTime = value.toString();
        this.setState({
            selectExamTime: value,
            examTime: examTime
        })
        let loadUrl= "/biz/exam/config/getByDateAndTime";
        let loadRequest={
            "examDate":this.state.examDate,
            "examTime":examTime
        };
        httpPost(loadUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    examId:response.data.examId
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]}  behavior="padding">
                <CommonHeadScreen title={'考试报名'}
                // <CommonHeadScreen title={this.state.operate + '考试报名'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>姓名</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入姓名'}
                            onChangeText={(text) => this.setState({examPersonName:text})}
                        >
                            {/* {constants.loginUser.userName} */}
                            {this.state.examPersonName}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>证件照</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',
                            alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                            <TouchableOpacity 
                                onPress={() => {
                                    uploadImageLibrary(this.state.goodsImageUrl, "user_header", (imageUploadResponse) => {
                                        console.log("========imageUploadResponse", imageUploadResponse)
                                        if (imageUploadResponse.code === 200) {
                                            WToast.show({ data: "成功上传" });
                                            let { compressFile } = imageUploadResponse.data
                                            this.setState({
                                                //goodsImageUrl:服务器地址+图片存储地址
                                                goodsImageUrl: constants.image_addr + '/' + compressFile,
                                                examPersonPhoto:compressFile,
                                            })
                                            httpPost("/biz/exam/apply/uploading_image", {
                                                "applyId":this.state.applyId, 
                                                "examPersonPhoto":compressFile
                                            }, (updateResponse)=>{
                                                if (updateResponse.code === 200) {
                                                    console.log("======证件照已更新")
                                                }
                                            })
                                        }
                                        else {
                                            WToast.show({ data: imageUploadResponse.message });
                                        }
                                    });
                            }}>
                                    {
                                        this.state.examPersonPhoto ?
                                        <Image source={{ uri: this.state.goodsImageUrl }} style={{width:120,height:150,justifyContent:'center',alignItems:'center'}} />
                                        :
                                        <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                    }
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系方式</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入联系方式'}
                            onChangeText={(text) => this.setState({contactNbr:text})}
                        >
                            {this.state.contactNbr}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>身份证号</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入身份证号'}
                            onChangeText={(text) => this.setState({identityCardNbr:text})}
                        >
                            {this.state.identityCardNbr}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出生日期</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={()=>this.openBirthAte()}>
                            {/* <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text>{this.state.schedulingProductionTime}</Text>
                            </View> */}
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.birthAte ? "请选择出生日期" : this.state.birthAte}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>注册号</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入注册号'}
                            onChangeText={(text) => this.setState({registrationNumber:text})}
                        >
                            {this.state.registrationNumber}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>报考科目</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.courseDataSource && this.state.courseDataSource.length > 0) 
                                ? 
                                this.state.courseDataSource.map((item, index)=>{
                                    return this.renderRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考试日期</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openExamDate()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.examDate ? "请选择考试日期" : this.state.examDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考试时间</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openExamTime()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.examTime ? "请选择考试时间" : this.state.examTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>邮箱</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入邮箱'}
                            onChangeText={(text) => this.setState({examPersonEmail:text})}
                        >
                            {this.state.examPersonEmail}
                        </TextInput>
                    </View>

                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                            <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveExam.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                            <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect
                        ref={'SelectBirthAte'}
                        callBackDateValue={this.callBackOpenBirthAteValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectExamDate'}
                        callBackExamDateValue={this.callBackExamDateValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectExamTime'}
                        callBackExamTimeValue={this.callBackExamTimeValue.bind(this)}
                    />
             </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({ 
itemViewStyle:{
    margin:10,  
    padding:15, 
    borderRadius:2,
    backgroundColor:'#FFFFFF'
},
selectedItemViewStyle:{
    margin:10,  
    padding:15, 
    borderRadius:2,
    backgroundColor:"#CB4139"
},
itemTextStyle:{
    color:'#000000'
},
selectedItemTextStyle:{
    color:'#FFFFFF'
},
inputRowStyle:{
    height:45,
    flexDirection:'row',
    marginTop:10,
    // flex: 1,
    // justifyContent: 'space-between',
    // alignContent:'center'
    // backgroundColor:'#000FFF',
    // width:screenWidth,
    // alignContent:'space-between',
    // justifyContent:'center'
},
rowLabView:{
    height:45,
    flexDirection:'row',
    alignItems:'center',
    paddingLeft:10,
    // alignContent:'flex-start',
    // justifyContent:'center',
    // backgroundColor:'yellow',
},
leftLabView:{
    width:leftLabWidth,
    height:45,
    flexDirection:'row',
    alignItems:'center',
    paddingLeft:10,
},
leftLabNameTextStyle:{
    fontSize:18
},
leftLabRedTextStyle:{
    color:'red',
    marginLeft:5,
    marginRight:5
},
inputRightText:{
    width:screenWidth - (leftLabWidth + 5),
    borderRadius:5,
    borderColor:'#F1F1F1',
    borderWidth:1,
    marginRight:5,
    color:'#A0A0A0',
    fontSize:15,
    paddingLeft:10,
    paddingRight:10
}
});
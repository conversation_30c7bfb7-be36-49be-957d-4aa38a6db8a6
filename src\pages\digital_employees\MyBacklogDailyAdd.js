import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,KeyboardAvoidingView,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class DailyAdd extends Component {
    constructor(){
        super()
        this.state = {
            operate:"",
            dailyId: "",
            dailyDate: "",
            finishedWork: "",
            unfinishedWork: "",
            requiresCoordinationWork: "",
            workPlan: "",
            auditScore: null,
            selectedDailyDate:[],
            auditScore:null,
            auditOpinion:null,
            operateCode:null,
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { dailyId,operate } = route.params;
            if (operate) {
                if ("detail" === operate) {
                    this.setState({ 
                        operate:"审核详情",
                    })
                }
                else if ("edit_audit" === operate) {
                    this.setState({ 
                        operate:"重新审核",
                    })
                }
                else {
                    this.setState({ 
                        operate:"审核",
                    })
                }
                this.setState({ 
                    operateCode:operate,
                })
            }
            if (dailyId) {
                console.log("========Edit==dailyId:", dailyId);
                this.setState({
                    dailyId:dailyId,
                })
                loadTypeUrl= "/biz/daily/get";
                loadRequest={'dailyId':dailyId};
                httpPost(loadTypeUrl, loadRequest, this.loadDailyDataCallBack);
            }
        }
    }
    loadDailyDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            var selectedDailyDate = response.data.dailyDate.split("-");
            this.setState({
                dailyId:response.data.dailyId,
                dailyDate: response.data.dailyDate,
                finishedWork:response.data.finishedWork,
                unfinishedWork:response.data.unfinishedWork,
                requiresCoordinationWork:response.data.requiresCoordinationWork,
                workPlan:response.data.workPlan,
                selectedDailyDate:selectedDailyDate,
                auditScore:response.data.auditScore,
                auditOpinion:response.data.auditOpinion ? response.data.auditOpinion : "无",

            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    saveDaily =()=> {
        console.log("=======saveDaily");
        let toastOpts;
        if (!this.state.auditScore && this.state.auditScore !==0) {
            toastOpts = getFailToastOpts("请输入审核得分");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/daily/audit";
        // if (this.state.dailyId) {
        //     console.log("=========Edit===dailyId", this.state.dailyId)
        //     url= "/biz/daily/audit";
        // }
        let requestParams={
            dailyId:this.state.dailyId,
            auditScore: this.state.auditScore,
            auditOpinion: this.state.auditOpinion,
        };
        httpPost(url, requestParams, this.saveDailyCallBack);
    }
    
    // 保存回调函数
    saveDailyCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('审核完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    openDailyDate(){
        this.refs.SelectDailyDate.showDate(this.state.selectedDailyDate)
    }
    
    callBackSelectSelectDailyDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedDailyDate:value
        })
        if (value && value.length) {
            var dailyDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    dailyDate += vartime;
                }
                else{
                    dailyDate += "-" + vartime;
                }
            }
            this.setState({
                dailyDate:dailyDate
            })
        }
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <KeyboardAvoidingView behavior="position" keyboardVerticalOffset = {100} >
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>提交日期</Text>
                            </View>
                            <TouchableOpacity onPress={()=>this.openDailyDate()}>
                                <View style={CommonStyle.inputTextStyleTextStyle}>
                                    <Text style={{color:'#A0A0A0', fontSize:15}}>
                                        {!this.state.dailyDate ? "请选择预计完成时间" : this.state.dailyDate}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={[styles.inputRowStyle]}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>完成的工作</Text>
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle]}>
                            <Text style={styles.textRowStyle}>
                                {this.state.finishedWork}
                            </Text>
                        </View>
                        <View style={[styles.inputRowStyle]}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>工作计划</Text>
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle]}>
                            <Text style={styles.textRowStyle}>
                                {this.state.workPlan}
                            </Text>
                        </View>
                        <View style={[styles.inputRowStyle]}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>未完成工作</Text>
                            </View>
                            {
                                !this.state.unfinishedWork ? 
                                <View style={styles.rightLabView}>
                                    <Text style={styles.rightLabText}>无</Text>
                                </View>
                                :
                                <View/>
                            }
                        </View>
                        {
                            this.state.unfinishedWork ? 
                            <View style={[styles.inputRowStyle]}>
                                <Text style={styles.textRowStyle}>
                                    {this.state.unfinishedWork}
                                </Text>
                            </View>
                            :
                            <View/>
                        }

                        <View style={[styles.inputRowStyle]}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>需协调工作</Text>
                            </View>
                            {
                                !this.state.requiresCoordinationWork ? 
                                <View style={styles.rightLabView}>
                                    <Text style={styles.rightLabText}>无</Text>
                                </View>
                                :
                                <View/>
                            }
                        </View>
                        {
                            this.state.requiresCoordinationWork ? 
                            <View style={[styles.inputRowStyle]}>
                                <Text style={styles.textRowStyle}>
                                    {this.state.requiresCoordinationWork}
                                </Text>
                            </View>
                            :
                            <View/>
                        }
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>审核得分</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入审核得分'}
                                onChangeText={(text) => this.setState({auditScore:text})}
                            >
                                {this.state.auditScore}
                            </TextInput>
                        </View>
                        <View style={[styles.inputRowStyle]}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>审核意见</Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle,{height:100}]}>
                            <TextInput 
                                multiline={true}
                                textAlignVertical="top"
                                style={[CommonStyle.inputRowText,{height:100}]}
                                placeholder={'请输入审核意见'}
                                onChangeText={(text) => this.setState({auditOpinion:text})}
                            >
                                {this.state.auditOpinion}
                            </TextInput>
                        </View>
                        {
                            this.state.operateCode === 'detail' ? 
                            <View style={{height:10}}/>
                            :
                            <View style={CommonStyle.btnRowStyle}>
                                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                                    <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                        <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={this.saveDaily.bind(this)}>
                                    <View style={CommonStyle.btnRowRightSaveBtnView}>
                                        <Text style={CommonStyle.btnRowRightSaveBtnText}>完成</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        }
                        
                    </KeyboardAvoidingView>
                </ScrollView>
                <BottomScrollSelect 
                    ref={'SelectDailyDate'} 
                    callBackDateValue={this.callBackSelectSelectDailyDateValue.bind(this)}
                />
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        flexDirection:'row',
        marginTop:10,
    },
    textRowStyle:{
        paddingLeft:10,
        paddingRight:10,
        fontSize:16,
    },

    rowLabView:{
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    rightLabView:{
        width:screenWidth - (leftLabWidth + 10),
        marginTop:10,
        // flexDirection:'row',
        // alignItems:'flex-start',
    },
    rightLabText:{
        fontSize:16,
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})
import React from 'react'
import {
    Text,
    View,
    ScrollView,
    TouchableOpacity,
    Dimensions,
    Image,
} from 'react-native';
import { WToast } from 'react-native-smart-tip';
import { uploadCameraImage, uploadImageLibrary, uploadMultiImageLibrary } from '../utils/UploadImageUtils';

import '../utils/Global';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class SelectUploadImage extends React.Component {

    constructor(props) {
        super(props);
        this.state = {
            imageUrl: constants.image_addr + "/image_a/10/2021-11/27/F0B58631-622B-469A-8CCF-70AE06ADEC73_152705275_small.jpg",
            errorMessage: null,
            compressFileList: []
        }
    }
    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
    }

    // 图片列表子项
    itemImage = (item) => {
        return (
            <View key={item.compressFile} style={{  width: 50, marginRight: 10 }}>
                <Image key={"image" + item.compressFile} source={{ uri: constants.image_addr + '/' + item.compressFile }} 
                 style={{ height: 50, width: 50, marginRight: 10 }} />

                <Text key={"text" + item.compressFile}>
                    {item.compressFile}
                </Text>
            </View>
        )
    }

    render() {
        return (
            <ScrollView style={{ width: screenWidth, height: screenHeight, overflow: 'scroll', flexDirection: 'column', backgroundColor: '#fff000' }}>
                <View style={{ alignItems: 'center', justifyContent: 'center', }}>
                    <View style={{ flexDirection: 'row' }}>
                        <View >
                            <View>
                                <TouchableOpacity
                                    activeOpacity={.8}
                                    onPress={() => {
                                        console.log("======登录")
                                        httpPost("/biz/user/login", { "userCode": "test", "userPwd": "1234567" }, (resp) => {
                                            console.log("======resp", resp)
                                            if (resp.code === 200) {
                                                WToast.show({ data: "登录成功" });
                                            }
                                            else {
                                                WToast.show({ data: resp.message });
                                            }
                                        })
                                    }}
                                >
                                    <View style={{ width: screenWidth * 0.35, height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: 'red', borderRadius: 30 }}>
                                        <Text style={{ color: '#000000' }}>
                                            登录
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>

                        <View>
                            <View>
                                <TouchableOpacity
                                    activeOpacity={.8}
                                    onPress={() => {
                                        httpGet("/biz/user/logout", (resp) => {
                                            console.log("======resp", resp)
                                            if (resp.code === 200) {
                                                WToast.show({ data: "登出" });
                                            }
                                            else {
                                                WToast.show({ data: resp.message });
                                            }
                                        })
                                    }}
                                >
                                    <View style={{ width: screenWidth * 0.35, height: 50, marginLeft: 10, alignItems: 'center', justifyContent: 'center', backgroundColor: 'red', borderRadius: 30 }}>
                                        <Text style={{ color: '#000000' }}>
                                            登出
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    <View style={{ marginTop: 50 }}>
                        <TouchableOpacity onPress={() => {
                            WToast.show({ data: "拍照上传" });
                            uploadCameraImage(this.state.imageUrl, "user_header", (imageUploadResponse) => {
                                console.log("========imageUploadResponse", imageUploadResponse)
                                if (imageUploadResponse.code === 200) {
                                    // 提交订单
                                    WToast.show({ data: "上传成功" });
                                    let { compressFile } = imageUploadResponse.data
                                    this.setState({
                                        imageUrl: constants.image_addr + '/' + compressFile
                                    })
                                }
                                else {
                                    WToast.show({ data: imageUploadResponse.message });
                                }
                            });

                        }}>

                            <View style={{ width: screenWidth * 0.4, height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: 'red', borderRadius: 30 }}>
                                <Text style={{ color: '#000000' }}>
                                    拍照上传
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={{ marginTop: 50 }}>
                        <View>
                            <TouchableOpacity
                                activeOpacity={.8}
                                onPress={() => {
                                    WToast.show({ data: "选择相册图片上传" });
                                    uploadImageLibrary(this.state.imageUrl, "user_headers", (imageUploadResponse) => {
                                        console.log("========imageUploadResponse", imageUploadResponse)
                                        if (imageUploadResponse.code === 200) {
                                            // 提交订单
                                            WToast.show({ data: "上传成功" });
                                            let { compressFile } = imageUploadResponse.data
                                            this.setState({
                                                imageUrl: constants.image_addr + '/' + compressFile
                                            })
                                        }
                                        else {
                                            WToast.show({ data: imageUploadResponse.message });
                                        }
                                    });
                                }}
                            >
                                <View style={{ width: screenWidth * 0.4, height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: 'red', borderRadius: 30 }}>
                                    <Text style={{ color: '#000000' }}>选择相册图片上传</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>


                    <View style={{ marginTop: 50, width: screenWidth * 0.9, backgroundColor: 'green', padding: 30, }}>
                        <Image source={{ uri: this.state.imageUrl }} style={{ height: 50 }} />
                    </View>

                    <View style={{ marginTop: 50 }}>
                        <View>
                            <TouchableOpacity
                                activeOpacity={.8}
                                onPress={() => {
                                    WToast.show({ data: "选择批量图片上传" });
                                    uploadMultiImageLibrary(3, "attachment_image", (imageUploadResponse) => {
                                        console.log("========imageUploadResponse", imageUploadResponse)
                                        if (imageUploadResponse.code === 200) {
                                            // 提交订单
                                            WToast.show({ data: "上传成功" });
                                            let compressFileList = imageUploadResponse.data
                                            this.setState({
                                                compressFileList: compressFileList
                                            })
                                        }
                                        else {
                                            WToast.show({ data: imageUploadResponse.message });
                                        }
                                    });
                                }}
                            >
                                <View style={{ width: screenWidth * 0.4, height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: 'red', borderRadius: 30 }}>
                                    <Text style={{ color: '#000000' }}>批量图片上传</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>


                    <View style={{ marginTop: 50, width: screenWidth * 0.9, backgroundColor: '#FF0000', padding: 30, }}>
                        <View style={{ flexDirection: 'row' }}>

                            {
                                (this.state.compressFileList && this.state.compressFileList.length > 0)
                                    ?
                                    this.state.compressFileList.map((item, index) => {
                                        return this.itemImage(item)
                                    })
                                    : <View ><Text>No Data</Text></View>
                            }
                        </View>
                    </View>

                </View>
            </ScrollView>
        )
    }
}

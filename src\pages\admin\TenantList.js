import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,Modal,ScrollView,TextInput,
    FlatList,RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
import * as WeChat from 'react-native-wechat-lib';

var screenHeight = Dimensions.get('window').height;
export default class TenantList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            shareModal:false,
            menuTypeSource:[],
            showSearchItemBlock: false,
            topBlockLayoutHeight: 0,
            searchKeyWord:"",
            selTypeCode:null,
            selTypeId:1,
            selTypeName:"全部"
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        let menuTypeSource = [
            {
                typeId:1,
                typeName:"全部",
                typeCode:null
            },
            {
                typeId:2,
                typeName:"耐材行业",
                typeCode:"M"
            },
            {
                typeId:3,
                typeName:"数字化管理",
                typeCode:"D"
            },
            {
                typeId:4,
                typeName:"就业实习平台",
                typeCode:"R"
            },
            {
                typeId:5,
                typeName:"后勤运营",
                typeCode:"H"
            },
            {
                typeId:6,
                typeName:"会员系统",
                typeCode:"V"
            }
        ]
        this.setState({
            menuTypeSource:menuTypeSource
        })

        this.loadResumeConfigList();
        this.loadTenantList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/tenant/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "menuTypes":this.state.selTypeCode,
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/tenant/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "menuTypes": this.state.selTypeCode,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadTenantList();
    }

    loadTenantList=()=>{
        let url= "/biz/tenant/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "menuTypes":this.state.selTypeCode,
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this.loadTenantListCallBack);
    }

    loadTenantListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteTenant =(tenantId)=> {
        console.log("=======delete=tenantId", tenantId);
        let url= "/biz/tenant/delete";
        let requestParams={'operateTenantId':tenantId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    loadResumeConfigList = () => {
        let url = "/biz/tenant/config/list";
        let loadRequest = {
        };
        httpPost(url, loadRequest, this.loadResumeConfigListCallBack);
    }

    loadResumeConfigListCallBack=(response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            var resumeQueryTitle = data.filter(item => (item.paramCode == 'MEMBER_SHARE_TITLE'));
            var resumeQuerySubTitle = data.filter(item => (item.paramCode == 'MEMBER_SHARE_SUB_TITLE'));
            var resumeQueryLogo = data.filter(item => (item.paramCode == 'MEMBER_SHARE_LOGO'));
            console.log("resumeQueryTitle==",resumeQueryTitle && resumeQueryTitle.length == 1 ? resumeQueryTitle[0].paramValue:null);
            console.log("resumeQuerySubTitle==",resumeQuerySubTitle && resumeQuerySubTitle.length == 1 ? resumeQuerySubTitle[0].paramValue:null);
            console.log("resumeQueryLogo==",resumeQueryLogo && resumeQueryLogo.length == 1 ? resumeQueryLogo[0].paramValue:null);
            this.setState({
                resumeQueryTitle:(resumeQueryTitle && resumeQueryTitle.length == 1) ? resumeQueryTitle[0].paramValue:null,
                resumeQuerySubTitle:(resumeQuerySubTitle && resumeQuerySubTitle.length == 1) ? resumeQuerySubTitle[0].paramValue:null,
                resumeQueryLogo:(resumeQueryLogo && resumeQueryLogo.length == 1) ? resumeQueryLogo[0].paramValue:null
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.tenantId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>租户名称：{item.tenantName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>租户简称：{item.tenantAbbreviation?item.tenantAbbreviation:item.tenantName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>所属组织：{item.orgName?item.orgName:"无"}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>联系人：{item.tenantContact}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系电话：{item.tenantTel}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>租户排序：{item.tenantSort}</Text>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("TenantEnterpriseList", 
                            {
                                // 传递参数
                                tenantId:item.tenantId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{flexDirection:'row'}
                        ]}>
                            <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/company2.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>企业管理</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("ProductionLineMgrList", 
                            {
                                // 传递参数
                                tenantId:item.tenantId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{flexDirection:'row'}
                        ]}>
                            <Image style={{width:20, height:20}} source={require('../../assets/icon/iconfont/workshop.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>车间管理</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("OutsourcingTenantList", 
                            {
                                // 传递参数
                                tenantId:item.tenantId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{flexDirection:'row'}
                        ]}>
                            <Image style={{width:17, height:17}} source={require('../../assets/icon/iconfont/outsource2.png')}></Image>
                            <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{marginLeft:3}]}>外协单位</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该角色吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteTenant(item.tenantId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:80,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("TenantAdd", 
                            {
                                // 传递参数
                                operateTenantId:item.tenantId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    
    // 头部右侧
    renderRightItem() {
        return (
            <View style={{flexDirection:'row'}}>
                <TouchableOpacity onPress={() => {
                    this.setState({
                        shareModal:true
                })
                }}>
                        {/* <Text style={CommonStyle.headRightText}>审核入库</Text> */}
                    <Image style={{ width:27, height:27,marginRight:8 }} source={require('../../assets/icon/iconfont/shareBlack.png')}></Image>
                    
                </TouchableOpacity>

                <TouchableOpacity onPress={() => {
                    this.props.navigation.navigate("TenantAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })
                }}>
                    <Image style={{ width:27, height:27 , marginRight:5}} source={require('../../assets/icon/iconfont/add.png')}></Image>
                
                </TouchableOpacity>
            </View>
        )
    }

    // 显示搜索项目
    showSearchItemSelect() {
        this.setState({
            showSearchItemBlock: this.state.showSearchItemBlock ? false : true,
        })
    }

    searchByKeyWord = () => {
        let url= "/biz/tenant/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "menuTypes":this.state.selTypeCode,
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 菜单类型
    renderMenuTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selTypeId: item.typeId,
                    selTypeName: item.typeName,
                    selTypeCode: item.typeCode,
                  
                })
            }}>
                <View key={item.typeId} style={[item.typeId === this.state.selDepartmentId ?
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                CommonStyle.choseToSearchItemsViewSize
                ]}>
                    <Text style={[item.typeId === this.state.selTypeId ?
                        CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.typeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='租户管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                    
                    <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                        <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                            <TouchableOpacity style = {{width:160}} onPress={() => this.showSearchItemSelect()}>
                                {
                                    this.state.showSearchItemBlock ?
                                        <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: '#ffffff', padding: 10, margin: 5, flexDirection: 'row' }]}>
                                            <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(30,110,250,1)", paddingRight: 10 }]}>
                                                {this.state.selTypeId ? this.state.selTypeName:"选择菜单类型"}
                                            </Text>
                                            <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/arrow-up.png')}></Image>
                                        </View>
                                        :
                                        <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: '#ffffff', padding: 10, margin: 5, flexDirection: 'row' }]}>
                                            <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(30,110,250,1)", paddingRight: 10 }]}>
                                                {this.state.selTypeId ? this.state.selTypeName:"选择菜单类型"}
                                            </Text>
                                            <Image style={{ width: 20, height: 20 }} source={require('../../assets/icon/iconfont/arrow_down.png')}></Image>
                                        </View>
                                }
                            </TouchableOpacity>

                            <View style={[styles.innerViewStyle,{marginTop:0,marginLeft:20,borderRadius:20}]} onLayout={this.topBlockLayout.bind(this)}>
                                <View style={{}}>
                                    <View style={styles.inputRowStyle}>
                                        <View style={styles.leftLabView}>
                                            {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                                            <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                        </View>
                                        <TextInput
                                            style={[styles.searchInputText, {}]}
                                            returnKeyType="search"
                                            returnKeyLabel="搜索"
                                            onSubmitEditing={e => {
                                            this.searchByKeyWord();
                                            }}
                                            placeholder={'搜索租户名称'}
                                            onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                        >
                                            {this.state.searchKeyWord}
                                        </TextInput>
                                        {/*<TouchableOpacity onPress={() => {
                                            this.searchByKeyWord();
                                        }}>
                                            <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 70,backgroundColor:'#DEB887',borderColor:'#DEB887' }]}>
                                                <Text style={[CommonStyle.itemBottomDeleteBtnTextStyle,{color:'#FFFFFF'}]}>查询</Text>
                                            </View>
                                    </TouchableOpacity>*/}
                                    </View>
                                    {/* <View style={{ height: 5, backgroundColor: '#FFFFFF' }}></View> */}
                                </View>
                            </View>


                        </View>
                    </View>
                     
                    

                {/* <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View> */}

                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.shareModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() =>console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut,{backgroundColor: 'rgba(0,0,0,0.5)'}]}>
                        <View style={{width:screenWidth,height:250,bottom:0,position:'absolute',backgroundColor:'#f0f0f0'}}>
                            <View style={{height:55,justifyContent:'center',alignItems:'center'}}>
                                <Text style={{fontSize:18}}>
                                    选择分享方式
                                </Text>
                            </View>
                            <View style={{height:105,flexDirection:'row',justifyContent:'center',index:1000}}>
                                <TouchableOpacity 
                                    onPress={() => {
                                        // console.log("标题=====",this.state.resumeQueryTitle?this.state.resumeQueryTitle:'极致私域资源运营服务平台')
                                        // console.log("副标题=====",this.state.resumeQuerySubTitle?this.state.resumeQuerySubTitle:'增加会员获得感，加强会员粘性，吸纳新会员')
                                        // console.log("log=====",this.state.resumeQueryLogo?this.state.resumeQueryLogo:'极致教育logo')
                                        // 分享微信好友
                                        WeChat.shareWebpage({
                                            title: '极致私域资源共享平台',
                                            description: '增加会员获得感，加强会员粘性，吸纳新会员',
                                            thumbImageUrl: 'http://image.njjzgk.com/images/logo/jzxs_app_logo.jpg',
                                            webpageUrl: 'https://jzxs.njjzgk.com/html/memberQuery/tenantList.html?tenantId=' + constants.loginUser.tenantId,
                                            scene: 0
                                        })
                                        .then((respJSON) => {
                                            WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                        })
                                        .catch((error) => {
                                            WToast.show({ data: error });
                                            Alert.alert(error.message);
                                        });
                                    }}>
                                    <View style={[{width:100,flexDirection:"column",margin:5,justifyContent:'center',alignItems:'center'}]}>
                                        <Image  style={{width:40, height:40,marginRight:2}} source={require('../../assets/icon/iconfont/WeChat.png')}></Image>
                                        <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{color:'#000000'}]}>微信好友</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => {
                                        // 分享朋友圈
                                        WeChat.shareWebpage({
                                            title: '极致私域资源共享平台',
                                            description: '增加会员获得感，加强会员粘性，吸纳新会员',
                                            thumbImageUrl: 'http://image.njjzgk.com/images/logo/jzxs_app_logo.jpg',
                                            webpageUrl: 'https://jzxs.njjzgk.com/html/memberQuery/tenantList.html?tenantId=' + constants.loginUser.tenantId,
                                            scene: 1
                                        })
                                            .catch((error) => {
                                                WToast.show({ data: error });
                                                Alert.alert(error.message);
                                            });
                                    }}>
                                    <View style={[{width:100,flexDirection:"column",margin:5,justifyContent:'center',alignItems:'center'}]}>
                                        <Image style={{ width:40, height:40}} source={require('../../assets/icon/iconfont/WeChatFriendsCircle2.png')}></Image>
                                        <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{color:'#000000'}]}>朋友圈</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{height:50,justifyContent:'center',alignItems:'center',borderTopWidth:1,borderTopColor:'#cccccc'}}>
                                <TouchableOpacity onPress={()=>{
                                    this.setState({
                                        shareModal:false
                                    })
                                }}>
                                    <View style={{width:screenWidth,justifyContent:'center',alignItems:'center'}}>
                                        <Text style={[{fontSize:18}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            

                        </View>
                    </View>
                </Modal>

                <View>
                    {
                        this.state.showSearchItemBlock ?
                            <View style={[CommonStyle.choseToSearchBigBoxViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                                <View style={CommonStyle.heightLimited}>
                                    <ScrollView>
                                        <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                            <View style={[{ backgroundColor: 'rgba(255,255,255,1)' }, CommonStyle.choseToSearchItemsViewSize]}>
                                                <Text style={{ fontSize: 16, fontWeight: 'bold' }}>菜单类型：</Text>
                                            </View>
                                            {
                                                (this.state.menuTypeSource && this.state.menuTypeSource.length > 0)
                                                    ?
                                                    this.state.menuTypeSource.map((item, index) => {
                                                        return this.renderMenuTypeRow(item)
                                                    })
                                                    : null
                                            }
                                        </View>   
                                    </ScrollView>
                                </View>
                                <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        console.log("当前选择的code",this.state.selTypeCode)
                                        let loadUrl = "/biz/tenant/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "menuTypes":this.state.selTypeCode,
                                            "searchKeyWord":this.state.searchKeyWord,
                                        };
                                        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                        this.setState({
                                            showSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            null
                    }
                    <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                        <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                            />
                    </View>
                </View>

            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:2,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    searchInputText: {
        width: screenWidth / 3.2,
        //borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 2,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    inputRowStyle: {
        paddingLeft: 15,
        height: 40,
        flexDirection: 'row',
        //borderWidth:1,
        //borderColor:"#FFFFFF",
        //backgroundColor:"#FFFFFF",
        //borderRadius:20,
    },
    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 0,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
});
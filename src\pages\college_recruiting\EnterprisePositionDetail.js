import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,ScrollView,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CrHeadScreen from '../../component/CrHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class EnterprisePositionDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            crTopBlockLayoutHeight:0,
            positionTypeChooseDataSource:[],
            selPositionType:"all",
            enterpriseId:"",
            crHiringPositionDataSource:[]
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

     //更新State
     _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let positionTypeChooseDataSource = [
            {
                positionType:'all',
                positionTypeName:'全部',
            },
            {
                positionType:"F",
                positionTypeName:"全职",
            },
            {
                positionType:"P",
                positionTypeName:"兼职",
            },
            {
                positionType:"I",
                positionTypeName:"实习"
            }

        ]
        this.setState({
            positionTypeChooseDataSource:positionTypeChooseDataSource,
        })
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId ,enterpriseId} = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
            if (enterpriseId) {
                console.log("=============enterpriseId" + enterpriseId + "");
                this.setState({
                    enterpriseId:enterpriseId
                })
            }
            this.loadEnterpriseList(enterpriseId);
        }
        
    }


    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                crHiringPositionDataSource:response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEnterpriseList=(enterpriseId)=>{
        let url= "/biz/enterprise/get";
        let loadRequest={
            "enterpriseId":enterpriseId ? enterpriseId : this.state.enterpriseId,
        };
        httpPost(url, loadRequest, this.loadEnterpriseCallBack);
    }

    loadEnterpriseCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                enterpriseLogo:response.data.enterpriseLogo,
                enterpriseName:response.data.enterpriseName,
                positionNumber:response.data.positionNumber,
                crHiringPositionDataSource:response.data.crHiringPositionDTOList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }


    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    crTopBlockLayout = (event) => {
        this.setState({
            crTopBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:24, height:24}} source={require('../../assets/icon/iconfont/CrBack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    //全部、全职、兼职、实习
    positionTypeChooseStateRow=(item, index)=>{
        return (
            <View key={item.positionType} >
                <TouchableOpacity onPress={()=>{
                    var selPositionType = item.positionType;
                    this.setState({
                        selPositionType:selPositionType
                    })

                    let url= "/biz/hiring/position/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "enterpriseId":this.state.enterpriseId,
                        "positionType":selPositionType === "all" ? null : selPositionType,
                    };
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.positionType} style={[item.positionType===this.state.selPositionType? [CommonStyle.selectedBlockItemViewStyle,{borderBottomWidth:2,borderBottomColor:'#1D80FF'}] : [CommonStyle.blockItemViewStyle,{borderBottomWidth:1,borderBottomColor:'#EEEEEE'}], { margin:0,padding:0,backgroundColor:"#FFFFFF" ,width:95,borderRadius:0}]}>
                        <Text style={[item.positionType===this.state.selPositionType? [{color:"#1D80FF",fontSize:16,textAlign:'center'}] : [{color:"#303030",fontSize:16,textAlign:'center'}], { fontWeight: 'bold',marginTop:5,marginBottom:5 }]}>
                            {item.positionTypeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.shiftId} style={[styles.crInnerViewStyle]}>
                <View style={[styles.inInnerViewStyle,{ }]}>
                    <TouchableOpacity onPress={()=>{
                        this.props.navigation.navigate("EnterprisecrHiringPositionDetail",
                        {
                            // 传递参数
                            positionId: item.positionId,
                            // 传递回调函数
                            refresh: this.callBackFunction
                        })
                    }}>                    

                <View style={[{flexDirection:'row',flexWrap:'wrap',marginTop:5}]}>
                    <View style={{}}>
                        <Text style={styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18,color:'#000000D9'}}>{item.positionName}</Text>
                    </View>
                    <View style={{position:'absolute',right:40}}>
                        <Text style={styles.itemContentChildTextStyle,{color:'#1D80FF', fontSize:14}}>{item.positionSalaryFrom}k - {item.positionSalaryTo}k</Text>
                    </View>
                </View>

                    <View style={[{flexDirection:'row',flexWrap:'wrap',marginTop:8}]}>
                        <View style={{}}>
                            <Text style={styles.itemContentChildTextStyle,{color:'#000000A6', fontSize:12,backgroundColor:'#EFF6F9' }}>{item.positionTypeName}</Text>
                        </View>
                        <View style={{}}>
                            <Text style={styles.itemContentChildTextStyle,{color:'#000000A6', fontSize:12,backgroundColor:'#EFF6F9',marginLeft:10}}>{item.enterpriseArea}</Text>
                        </View>
                        
                    </View>

                    </TouchableOpacity>
                </View>
            </View>
        )
    }


    render(){
        return(
            <View style = {{backgroundColor:'#F7FBFC'}}>
                <CrHeadScreen title='公司岗位'
                        leftItem={() => this.renderLeftItem()}
                        rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.itemContentViewStyle,{ marginTop:10,marginLeft:20,justifyContent:'center',alignItems:'center',backgroundColor:'#FFF'}]} onLayout={this.crTopBlockLayout.bind(this)}>     
                    <Image style={[styles.enterpriseLogoStyle,{marginBottom:15,marginLeft:20}]} source={{uri:this.state.enterpriseLogo}}></Image>
                    
                    <View style={styles.itemContentRightChildViewStyle,{marginLeft:0,marginTop:5,width:320}}>
                        <Text style={styles.itemContentChildTextStyle,{marginLeft:20,fontWeight:'bold',color:'#000000D9', fontSize:16}}>{this.state.enterpriseName}</Text>
                        <Text style={styles.itemContentChildTextStyle,{marginLeft:20,color:'#2C2C2CA6', fontSize:14,marginTop:5}}>在招职位数：{this.state.positionNumber}</Text>
                    </View>
                </View>

                {/* 全部、全职、兼职、实习 */}
                <View style={[styles.innerViewStyle,{backgroundColor:'#FFFFFF',marginBottom:10,borderTopColor:'#EEEEEE',borderTopWidth:1,marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row'}}>
                    {
                        (this.state.positionTypeChooseDataSource && this.state.positionTypeChooseDataSource.length > 0)
                            ?
                            this.state.positionTypeChooseDataSource.map((item, index) => {
                                return this.positionTypeChooseStateRow(item)
                            })
                            : <View />
                    }
                    </View>
                </View>
                <ScrollView style={{marginBottom:30,height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight + this.state.crTopBlockLayoutHeight)}}>
                    <View style={{width:screenWidth -40 ,marginLeft:20}}>
                        {
                            (this.state.crHiringPositionDataSource && this.state.crHiringPositionDataSource.length > 0) 
                            ? 
                            this.state.crHiringPositionDataSource.map((item, index)=>{
                                return(
                                    this.renderRow(item)
                                )                           
                        })
                            : <View /> 
                        }
                    </View>
                </ScrollView>
            </View>
        )
    }

}

const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0
    },
    innerViewStyle: {
        marginTop: 10,
        // borderColor: "#F4F4F4",
        // borderWidth: 14,
        borderWidth: 0,
    },
    crInnerViewStyle:{
        marginTop:10,
        backgroundColor:'#FFF',
        width:screenWidth - 40,
        borderRadius:20,
        paddingLeft:20,
        marginBottom:10
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:screenWidth - 120,
    },
    itemContentRightChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:120,
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        // justifyContent: 'space-between',
        marginLeft: 18
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    enterpriseLogoStyle:{
        borderRadius:10,
        width:59,
        height:49,
        marginTop:20
    },
    inInnerViewStyle:{
        marginVertical:6,
        paddingLeft:5,
        paddingVertical:14,
        // borderColor:"#FFF500",
        // borderWidth:8
    },
})
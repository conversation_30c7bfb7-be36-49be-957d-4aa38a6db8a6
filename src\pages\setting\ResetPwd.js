import React,{Component} from 'react';
import {View, ScrollView, Text, Image,TextInput, StyleSheet,TouchableOpacity,Dimensions,Alert} from 'react-native';
import CommonHeadScreen from '../../component/CommonHeadScreen';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
class ResetPwd extends Component {
    constructor(props) {
        super(props);
        this.state ={
            oldUserPwd:'',
            userPwd:'',
            confirmUserPwd:''
        }
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    modifyPwd =()=> {
        let toastOpts;
        if (!this.state.oldUserPwd) {
            toastOpts = getFailToastOpts("请输入当前密码");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.userPwd) {
            toastOpts = getFailToastOpts("请输入新密码");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.confirmUserPwd) {
            toastOpts = getFailToastOpts("请输入确认新密码");
            WToast.show(toastOpts)
            return;
        }

        if (this.state.confirmUserPwd != this.state.userPwd) {
            toastOpts = getFailToastOpts("确认密码和新密码不一致，请重新输入");
            WToast.show(toastOpts)
            return;
        }

        Alert.alert('确认','您确定要修改密码吗？',[
            {
                text:"取消", onPress:()=>{
                WToast.show({data:'点击了取消'});
                // this在这里可用，传到方法里还有问题
                // this.props.navigation.goBack();
                }
            },
            {
                text:"确定", onPress:()=>{
                    WToast.show({data:'点击了确定'});
                    let url= "/biz/user/account/change/pwd";
                    let requestParams={
                        'oldUserPwd':this.state.oldUserPwd,
                        'userPwd':this.state.userPwd,
                    };
                    httpPost(url, requestParams, this.modify_pwd_call_back);
                }
            }
        ]);
    }
    modify_pwd_call_back=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('密码修改完成，重新登录');
                WToast.show(toastOpts)
                this.logout();
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    logout=()=>{
        console.log("===logout");
        let url = "/biz/user/logout?a=123&b=234"
        httpGet(url, this.logout_call_back);
    }

    logout_call_back=(response)=>{
        console.log("=====logout_call_back:", response);
        this.props.navigation.navigate('LoginView');
    }

    render(){
        return(
            <View >
                <CommonHeadScreen title='修改密码'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                当前密码
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={[styles.inputRightText,{width: screenWidth-(leftLabWidth+30)}]}
                            placeholder={'请输入当前密码'}
                            onChangeText={(text) => this.setState({oldUserPwd:text})}
                        >
                            {this.state.oldUserPwd}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                新密码
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={[styles.inputRightText,{width: screenWidth-(leftLabWidth+30)}]}
                            placeholder={'请输入新密码'}
                            onChangeText={(text) => this.setState({userPwd:text})}
                        >
                            {this.state.userPwd}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                确认新密码
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={[styles.inputRightText,{width: screenWidth-(leftLabWidth+30)}]}
                            placeholder={'请输入确认新密码'}
                            onChangeText={(text) => this.setState({confirmUserPwd:text})}
                        >
                            {this.state.confirmUserPwd}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={()=>{
                            this.props.navigation.goBack()
                        }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{}]} >
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.modifyPwd.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                            <Image  style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>

            </View>
        )}
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     backgroundColor:'#FFFFFF',
    //     height:screenHeight - 140
    // },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})
module.exports = ResetPwd;
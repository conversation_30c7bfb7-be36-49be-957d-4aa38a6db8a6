import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,Modal,
    FlatList,RefreshControl,ScrollView,TextInput,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
import ImageViewer from 'react-native-image-zoom-viewer';
export default class ExamApplyAudit extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            topBlockLayoutHeight:0,
            totalRecord:1,
            examTypeChooseDataSource: [],
            selExamType: "all",
            selExamTypeName:'全部',
            display:"N",
            compressFileList:[],
            urls:[],
            isShowImage: false,
            pictureIndex:0,

            //审核结果
            OperateResultDataSource:[],
            modal:false,
            
            auditExplain:"",
            auditOperateResult:"",
            selOperateResultStateCode:"4"
            
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        let OperateResultDataSource = [
            {
                stateCode:'4',
                stateName:'通过',
            },
            {
                stateCode:'3',
                stateName:'驳回',
            },
        ]

        this.setState({
            OperateResultDataSource:OperateResultDataSource,
        })

        let examTypeChooseDataSource = [
            {
                examType: 'all',
                examTypeName: '全部',
            },
            {
                examType: '1',
                examTypeName: "待审核",
            },
            {
                examType: '4',
                examTypeName: "通过",
            },
            {
                examType: '3',
                examTypeName: "驳回"
            }

        ]
        this.setState({
            examTypeChooseDataSource: examTypeChooseDataSource,
        })
        this.loadExamApplyList();
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }
    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    loadExamApplyList=()=>{
        let url= "/biz/exam/apply/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "excludeParamCode":"ADMISSION_TICKET_NOTICE",
            "auditState":this.state.selExamType === 'all' ? null : this.state.selExamType
        };
        httpPost(url, loadRequest, this.callBackLoadExamApplyList);
    }
    
    callBackLoadExamApplyList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N" ,pictureDisplay: "N",personPictureDisplay:false}))
            })
            this.setState({
                dataSource: listNew,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 回调函数
    callBackFunction=()=>{
        this.setState({
            auditExplain:""
        })
        let url= "/biz/exam/apply/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "excludeParamCode":"ADMISSION_TICKET_NOTICE",
            "auditState":this.state.selExamType === 'all' ? null : this.state.selExamType
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/exam/apply/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "excludeParamCode":"ADMISSION_TICKET_NOTICE",
            "auditState":this.state.selExamType === 'all' ? null : this.state.selExamType
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N" ,pictureDisplay: "N",personPictureDisplay:false}))
            })
            this.setState({
                dataSource: listNew,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadExamApplyList();
    }

    // 保存函数
    saveAudit=()=>{
        console.log("=======saveAudit");
        let toastOpts;
        
        if (!this.state.selOperateResultStateCode) {
            this.setState({
                errorMsg:"请选择审核结果"
            })
            return;
        }
        if(this.state.selOperateResultStateCode==="3"){
            if (!this.state.auditExplain) {
                this.setState({
                    errorMsg:"请输入审核意见",
                    auditExplain:"",
                })
                return;
            }
        }
        let url= "/biz/exam/apply/modify";
        let requestParams={
            applyId:this.state.applyId,
            auditState:this.state.selOperateResultStateCode,
            auditExplain:this.state.auditExplain,
        };
        httpPost(url, requestParams, this.saveCallBack);
    }
    
    saveCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                //关闭弹窗
                this.setState({
                    modal:false,
                    errorMsg:"",
                }) 
                this.callBackFunction();
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
            }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    examTypeChooseStateRow = (item, index) => {
        return (
            <View key={item.examType} >
                <TouchableOpacity onPress={() => {
                    var selExamType = item.examType;
                    console.log("selExamType",selExamType);
                    this.setState({
                        selExamType: selExamType
                    })

                    let url= "/biz/exam/apply/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": 15,
                        "excludeParamCode":"ADMISSION_TICKET_NOTICE",
                        "auditState":selExamType === 'all' ? null : selExamType,

                    };
                    // console.log("selYearsChooseName+1:"+ this.addOneYear(selYearsChooseName))
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.examType} style={[item.examType === this.state.selExamType ? [CommonStyle.selectedBlockItemViewStyle,{borderBottomWidth:2,borderBottomColor:"#CB4139"}] : [CommonStyle.blockItemViewStyle,{}], { backgroundColor:"#F0F0F0",paddingLeft:4, paddingRight:4 ,width:70,borderRadius:0}]}>
                        <Text style={[item.examType === this.state.selExamType ? [{color:"#CB4139",fontSize:18,textAlign:'center'}] : [{color:"#000000",fontSize:18,textAlign:'center'}], { fontWeight: 'bold' }]}>
                            {item.examTypeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    renderOperateResultRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    this.setState({
                        selOperateResultStateCode:item.stateCode,
                        auditOperateResult:item.stateCode
                    })
                }}>
                    <View 
                        key={item.stateCode} 
                        style={[item.stateCode===this.state.selOperateResultStateCode? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}
                        >
                        <Text style={[item.stateCode===this.state.selOperateResultStateCode? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }


    renderRow=(item,index)=>{
        return (
            <View key={item.applyId} style={styles.innerViewStyle}>
                {
                    item.auditState ==1 ?
                    <View style={[styles.titleViewStyle,{height:35, backgroundColor:'rgba(191,191,191,0.7)',borderRadius:5, justifyContent:'center',alignItems:'center'}]}>
                        <Text style={[styles.titleTextStyle,{fontWeight:'bold',fontSize:18,color:'#636363'}]}>
                        待审核</Text>
                        {/* {
                            item.internalFlag != 'Y' ? 
                            <Text></Text>
                            :
                            <Text style={{paddingTop:3, paddingBottom:3,paddingRight:5,position:'relative',paddingLeft:5,left:screenWidth / 4,borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>经川</Text>
                        } */}
                        {
                            item.internalFlag == 'Y' ?
                            <Text style={{position:'absolute',right:25,fontWeight:'bold',color:'#1E90FF'}} >经川财经</Text>
                            :
                            <View />
                        }
                    </View> :
                    (
                        item.auditState ==3 ?
                        <View style={[styles.titleViewStyle,{height:35, backgroundColor:'rgba(255,0,0,0.5)',borderRadius:5, justifyContent:'center',alignItems:'center'}]}>
                            <Text style={[styles.titleTextStyle,{fontWeight:'bold',fontSize:18,color:'#832f2f'}]}>
                            驳回</Text>
                            {/* {
                                item.internalFlag != 'Y' ? 
                                <Text></Text>
                                :
                                <Text style={{paddingTop:3, paddingBottom:3,paddingRight:5,position:'relative',paddingLeft:5,left:screenWidth / 4,borderRadius:12, backgroundColor:'rgba(0,0,255,0.4)', color:'#FFFFFF'}}>经川</Text>
                            } */}
                            {
                                item.internalFlag == 'Y' ?
                                <Text style={{position:'absolute',right:25,fontWeight:'bold',color:'#1E90FF'}} >经川财经</Text>
                                :
                                <View />
                            }
                        </View> : 
                        <View style={[styles.titleViewStyle,{height:35, backgroundColor:'rgba(57,195,66,0.5)',borderRadius:5, justifyContent:'center',alignItems:'center'}]}>
                            <Text style={[styles.titleTextStyle,{fontWeight:'bold',fontSize:18,color:'#237a28'}]}>
                            报名成功</Text>
                            {/* {
                                item.internalFlag != 'Y' ? 
                                <Text></Text>
                                :
                                <Text style={{paddingTop:3, paddingBottom:3,paddingRight:5,position:'relative',paddingLeft:5,left:screenWidth / 4,borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>经川</Text>
                            } */}
                            {
                                item.internalFlag == 'Y' ?
                                <Text style={{position:'absolute',right:25,fontWeight:'bold',color:'#1E90FF'}} >经川财经</Text>
                                :
                                <View />
                            }
                        </View> 
                    )
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>姓名：{item.examPersonName}</Text>
                </View>
                <View style={{position:'absolute',right:10,top:45}}>
                    {
                        item.examPersonPhoto?
                        <View style={{height: 80, width:80}}>
                            <View>   
                            <TouchableOpacity onPress={() => {
                                let list = this.state.dataSource;
                                list.map((elem, index) => {
                                    if(elem.applyId == item.applyId){
                                        item.personPictureDisplay = true;
                                    }
                                })
                                console.log("personPictureDisplay",item.personPictureDisplay)
                                this.setState({
                                    dataSource:list,
                                })
                            }}>
                            <Image source={{ uri: (constants.image_addr + '/' + item.examPersonPhoto) }} style={{width:80,height:80,justifyContent:'center',alignItems:'center'}} />                                                    
                            </TouchableOpacity>
                            <Modal visible={item.personPictureDisplay} transparent={true}>
                                <ImageViewer enableSwipeDown={false} menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} onSave={() => alert("点击了保存图片")} 
                                saveToLocalByLongPress={false}
                                onClick={() => { // 图片单击事件
                                    let list = this.state.dataSource;
                                    list.map((elem, index) => {
                                        if(elem.applyId == item.applyId){
                                            item.personPictureDisplay = false;
                                        }
                                    })
                                    this.setState({
                                        dataSource:list,
                                    })
                                }}
                                imageUrls={[{url:(constants.image_addr + '/' + item.examPersonPhoto)}]} />
                            </Modal>
                            </View>
                            {/* <Text>{constants.image_addr+ '/' + item.exchangeGoodsImage}</Text> */}
                        </View>
                        :
                        <View style={{height: 80, width:80,borderColor:'#AAAAAA',borderWidth:0.3,justifyContent:'center',alignItems:'center'}}>
                            <Text style={[styles.titleTextStyle,{color:"#aaaaaa"}]}>无</Text>
                        </View>
                    }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系方式：{item.contactNbr}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>出生日期：{item.birthAte}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>身份证号：{item.identityCardNbr}</Text>
                </View>   
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>注册号：{item.registrationNumber}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>报考科目：{item.examCourse}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考试日期：{item.examDate}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考试时间：{item.examTime}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>邮箱：{item.examPersonEmail}</Text>
                </View>
                {
                    item.auditExplain ?
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle,{color:'rgba(255,0,0,0.9)'}]}>审核意见：{item.auditExplain}</Text>
                    </View> : <View/>
                }
                {/* {
                    item.auditState == 1 ? */}
                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    <TouchableOpacity onPress={()=>{
                            this.setState({
                                modal:true,
                                applyId:item.applyId,
                                auditExplain:item.auditExplain,
                                selOperateResultStateCode:item.auditState!="1"?item.auditState:"4"
                            })
                        }
                            }>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{backgroundColor:"#5eaef5",width:75,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/audit2.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>审核</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                {/* : <View />
                } */}
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='报名审核'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <View style={[styles.innerViewStyle, { marginTop: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.examTypeChooseDataSource && this.state.examTypeChooseDataSource.length > 0)
                                ?
                                this.state.examTypeChooseDataSource.map((item, index) => {
                                    return this.examTypeChooseStateRow(item)
                                })
                                : 
                            <View />
                        }
                    </View>
                </View>

                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
                
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.modal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle,{height:40, backgroundColor:'#5eaef5',borderRadius:5, justifyContent:'center',alignItems:'center',marginTop:10}]}>
                                <Text style={[styles.titleTextStyle,{fontSize:18,fontWeight:'bold',color:"#ffffff"}]}>报名审核</Text>
                            </View>
                            <ScrollView>   
                                <View>
                                    {
                                        (this.state.OperateResultDataSource && this.state.OperateResultDataSource.length > 0)
                                            ?
                                            <View>
                                                <View style={styles.inputRowStyle}>
                                                    <View style={styles.leftLabView}>
                                                        <Text style={styles.leftLabNameTextStyle}>审核结果</Text>
                                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                                    </View>
                                                </View>
                                                <View style={{ width: screenWidth-40, flexWrap: 'wrap', flexDirection: 'row' }}>
                                                    {
                                                        (this.state.OperateResultDataSource && this.state.OperateResultDataSource.length > 0)
                                                            ?
                                                            this.state.OperateResultDataSource.map((item, index) => {
                                                                return this.renderOperateResultRow(item)
                                                            })
                                                            : <EmptyRowViewComponent />
                                                    }
                                                </View>
                                            </View>
                                            : <View />
                                    }
                                </View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>审核意见</Text>
                                        {
                                            this.state.selOperateResultStateCode=="3"?
                                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                                            :
                                            <View/>
                                        }
                                    </View>                                
                                </View>
                                <View style={[styles.inputRowStyle,{height:105}]}>
                                    <TextInput
                                        editable={!this.state.isTel} 
                                        multiline={true}
                                        textAlignVertical="top"
                                        style={[CommonStyle.inputRowText,{height:100,width:screenWidth - 70}]}
                                        placeholder={'请输入审核意见'}
                                        onChangeText={(text) => this.setState({auditExplain:text})}
                                    >
                                        {this.state.auditExplain}
                                    </TextInput>
                                </View>
                            </ScrollView>
                            <View style={CommonStyle.alignCenterStyle}>
                                <Text style={[CommonStyle.rowLabRedTextStyle, CommonStyle.boldTextStyle]}>{this.state.errorMsg}</Text>
                            </View>
                            <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                <TouchableOpacity onPress={() => { 
                                    this.setState({
                                        modal:false,
                                        errorMsg:""
                                    }) 
                                }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                        <Image  style={{width:22, height:22,marginRight:10}} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={
                                    this.saveAudit.bind(this)
                                    }>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                        <Image  style={{width:25, height:25,marginRight:10}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                        <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>保存</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>      
                    </View>
                </Modal>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    contentViewStyle:{
       height:screenHeight - 70,
       backgroundColor:'#FFFFFF'
   },
   inputRowStyle: {
       paddingLeft: 5,
       height: 40,
       flexDirection: 'row',
   },

   leftLabView: {
       height: 45,
       flexDirection: 'row',
       alignItems: 'center',
       paddingLeft: 10,
   },
   leftLabNameTextStyle: {
       fontSize: 18,
   },
   searchInputText: {
       width: screenWidth / 2,
       borderColor: '#000000',
       borderBottomWidth: 1,
       marginRight: 5,
       color: '#A0A0A0',
       fontSize: 16,
       marginLeft: 10,
       paddingLeft: 10,
       paddingRight: 10,
       paddingBottom: 0
   },
   innerViewStyle: {
    //    marginTop: 10,
       borderColor: "#F4F4F4",
       borderWidth: 8
   },
   titleViewStyle: {
       flexDirection: 'row',
       justifyContent: 'space-between',
       marginLeft: 10,
       marginRight: 10,
       marginBottom: 5,
       marginTop: 5,
   },
   titleTextStyle: {
       fontSize: 16
   },
   itemContentStyle: {
       flexDirection: 'row',
       alignItems: 'center'
   },
   itemContentImageStyle: {
       width: 120,
       height: 120
   },
   itemContentViewStyle: {
       flexDirection: 'row',
       justifyContent: 'space-between',
       marginLeft: 25
   },
   itemContentChildViewStyle: {
       flexDirection: 'column'
   },
   itemContentChildTextStyle: {
       marginLeft: 10,
       marginTop: 15,
       fontSize: 16
   },
   leftLabNameTextStyle: {
    fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
})
import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import { Calendar } from 'react-native-calendars';
import DateTimePicker from '@react-native-community/datetimepicker';
// import Calendar from '../../component/Calendar';
var CommonStyle = require('../../assets/css/CommonStyle');
import moment from 'moment';
import { ScrollView } from 'react-native-gesture-handler';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
var currentDate = (moment(new Date()).format('YYYY-MM-DD'));
var currentTime=(new Date()).getTime() + 8*3600*1000
export default class CourseSchedulingTable extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            day:currentDate,
            time:"",
            open:false,
            currentTime:currentTime,
            departmentIdList:[],
            topBlockLayoutHeight: 0,
            courseDateList:[]
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        console.log("departmentInfo=======",constants.loginUser.portalDepartmentDTOList);
        var portalDepartmentDTOList = constants.loginUser.portalDepartmentDTOList;
        var departmentIdList = [];
        if(portalDepartmentDTOList && portalDepartmentDTOList.length > 0){
            for(var i = 0;i < portalDepartmentDTOList.length;i++){
                departmentIdList = departmentIdList.concat(portalDepartmentDTOList[i].departmentId);
            }
        }
        console.log("departmentIdList==========",departmentIdList);
        this.setState({
            departmentIdList:departmentIdList
        })
        this.loadCourseList(departmentIdList);
        this.loadCourseDateList(departmentIdList);
    }

    loadCourseList=(departmentIdList)=>{
        let url= "/biz/course/scheduling/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "teachingDate": this.state.day,
            "departmentIdList":departmentIdList?departmentIdList:this.state.departmentIdList,
        };
        console.log(loadRequest);
        httpPost(url, loadRequest, this.loadCourseListCallBack);
    }

    loadCourseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            // console.log(dataAll);
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadCourseDateList=(departmentIdList)=>{
        let url= "/biz/course/scheduling/dateList";
        let loadRequest={
            "departmentIdList":departmentIdList?departmentIdList:this.state.departmentIdList,
        };
        console.log(loadRequest);
        httpPost(url, loadRequest, this.loadCourseDateListCallBack);
    }

    loadCourseDateListCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                courseDateList:response.data,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/course/scheduling/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "teachingDate": this.state.day,
            "departmentIdList":this.state.departmentIdList,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    
    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadCourseList();
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    dateChanged=(day)=>{
        console.log(day.dateString)
        this.setState({
            day:day.dateString
        })
    }


    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return(
            <View style={[{backgroundColor:'#FFFFFF',alignItems: 'center', justifyContent: 'center',height:this.state.topBlockLayoutHeight}]}>
                <Text style={{color:'#A0A0A0',fontSize:25}}>暂无数据</Text>
            </View>
        )
    }

    renderRow = (item, index) => {
        return (
            <View key={item.schedulingId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>课程名称：{item.courseName}</Text>
                </View>
                {
                    item.teachingTeacher ? 
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>授课老师：{item.teachingTeacher}</Text>
                    </View>
                    :
                    <View/>
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>课程时间：{item.teachingTime}</Text>
                </View>
                {
                    item.teachingClassroom ? 
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>上课教室：{item.teachingClassroom}</Text>
                    </View> 
                    :
                    <View/>
                }
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='排课表'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <View onLayout={this.topBlockLayout.bind(this)}>
                        <Calendar
                            dayComponent={({date, state}) => {
                                return (
                                <View style={[{marginBottom:2.5,marginTop:2.5,height:25},date.dateString===this.state.day ? {backgroundColor:'#00BFFF',borderRadius:50,height:25,width:25}:null
                                ]}>
                                    <TouchableOpacity 
                                    style={{height:25,width:25}}
                                    onPress={()=>{
                                        if(state === 'disabled'){
                                            return;
                                        }
                                        console.log(date)
                                        this.setState({
                                            day:date.dateString
                                        })
                                        let url= "/biz/course/scheduling/list";
                                        let loadRequest={
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "teachingDate": date.dateString,
                                            "departmentIdList":this.state.departmentIdList,
                                        };
                                        httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                    }}>
                                        <View style={{height:25,width:25,justifyContent:'center',alignItems:'center'}}>
                                            <Text style={[{textAlign: 'center'}, state === 'disabled' ? {color: '#A9A9A9'} :(date.dateString===this.state.day ?  {color: 'white'}:{color: 'black'})]}>
                                                {date.day}
                                            </Text>
                                        </View>
                                        {
                                            this.state.courseDateList.includes(date.dateString)?
                                            <View style={{width:5,height:5,backgroundColor:'red',borderRadius:50,marginLeft:10}}>

                                            </View>
                                            :
                                            <View/>
                                        }
                                    </TouchableOpacity>
                                </View>
                                );
                            }}
                            current={this.state.day}
                        ></Calendar>
                    </View>
                    
                    <View style={[CommonStyle.contentViewStyle, {justifyContent:'center', height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                        <FlatList
                            data={this.state.dataSource}
                            renderItem={({ item, index }) => this.renderRow(item, index)}
                            ListEmptyComponent={()=>{
                                return(
                                    <View style={[{backgroundColor:'#FFFFFF',alignItems: 'center', justifyContent: 'center',height:this.state.topBlockLayoutHeight}]}>
                                        <Text style={{color:'#A0A0A0',fontSize:25}}>暂无数据</Text>
                                    </View>
                                )
                            }}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                    tintColor="#FF0000"
                                    title="loading"
                                    colors={['#FF0000', '#00FF00', '#0000FF']}
                                    progressBackgroundColor="#FFFF00"
                                    refreshing={this.state.refreshing}
                                    onRefresh={() => {
                                        this._loadFreshData()
                                    }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={() => this.flatListFooterComponent()}
                            onEndReached={() => this._loadNextData()}
                        />                    
                </View>
                </View>
                
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle: {
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});
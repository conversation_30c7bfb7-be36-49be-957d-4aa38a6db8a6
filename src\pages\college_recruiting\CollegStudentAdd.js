import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,KeyboardAvoidingView,
    FlatList, RefreshControl, ScrollView, TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class CollegStudentAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            staffId: "",
            staffName: "",
            className: "",
            staffTel: "",
            staffSort: 0,
            gradePoint: "",
            comprehensiveAbility: 0,
            adjustFactor:"",
            personalHonor: "",
            collegeEvaluation: "",
            operate: "",
            classDataSource: [],
            selectedClass: [],
            roleId: "",
            comprehensivePoint:"",
            classId:""
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        // 加载班级列表
        this.loadClassList();
        // 获取租户下调节系数
        this.loadAdjustFactor();
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { staffId } = route.params;
            if (staffId) {
                // console.log("=============courseId" + courseId + "");
                this.setState({
                    staffId: staffId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/cr/staff/get";
                loadRequest = { 'staffId': staffId };
                httpPost(loadTypeUrl, loadRequest, this.loadCollegStudentCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
        }
    }

    loadCollegStudentCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                staffId: response.data.staffId,
                staffName: response.data.staffName,
                staffTel: response.data.staffTel,
                staffSort: response.data.staffSort,
                className: response.data.className,
                selectedClass: [response.data.className],
                gradePoint: response.data.gradePoint / 100,
                comprehensiveAbility: response.data.comprehensiveAbility / 100,
                comprehensivePoint: response.data.comprehensivePoint,
                personalHonor: response.data.personalHonor,
                collegeEvaluation: response.data.collegeEvaluation
            })
        }

    }

    loadAdjustFactor=()=>{
        let url = "/biz/tenant/get";
        let loadRequest = {
            "operateTenantId":constants.loginUser.tenantId
        };
        httpPost(url, loadRequest, this.loadAdjustFactorCallBack);
    }

    loadAdjustFactorCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                adjustFactor: response.data.adjustFactor,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadClassList = () => {
        let url = "/biz/college/class/grades/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadClassListCallBack);
    }

    loadClassListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                classDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("CollegStudentList",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Text style={CommonStyle.headRightText}>学生管理</Text>
            </TouchableOpacity>
        )
    }

    saveStaff = () => {
        console.log("=======saveStaff");
        let toastOpts;
        if (!this.state.staffName) {
            toastOpts = getFailToastOpts("请输入学生姓名");
            return;
        }
        if (!this.state.staffTel) {
            toastOpts = getFailToastOpts("请输入联系电话");
            return;
        }
        let url = "/biz/cr/staff/add";
        if (this.state.staffId) {
            console.log("=========Edit===staffId", this.state.staffId)
            url = "/biz/cr/staff/modify";
        }
        let requestParams = {
            staffId: this.state.staffId,
            staffName: this.state.staffName,
            staffTel: this.state.staffTel,
            roleId: this.state.roleId,
            comprehensiveAbility: this.state.comprehensiveAbility * 100,
            comprehensivePoint: (this.state.comprehensivePoint/100).toFixed(2)*100,
            gradePoint: this.state.gradePoint * 100,
            // staffSort: this.state.staffSort,
            staffSort:0,
            personalHonor: this.state.personalHonor,
            collegeEvaluation: this.state.collegeEvaluation,
            classId:this.state.classId,
        };
        console.log("============requestParams", requestParams)
        httpPost(url, requestParams, this.saveStaffCallBack);
    }

    // 保存回调函数
    saveStaffCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    openClass() {
        if (!this.state.classDataSource || this.state.classDataSource.length < 1) {
            WToast.show({data:"请先添加班级"});
            return
        }
        this.refs.SelectClass.showClass(this.state.selectedClass, this.state.classDataSource);
    }

    callBackSelectClassValue(value) {
        console.log("==========班级选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedClass: value
        })
        var className = value.toString();
        this.setState({
            className: className
        })
        let loadUrl = "/biz/college/class/grades/getClassByName";
        let loadRequest = {
            "className": className
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    roleId: response.data.roleId,
                    classId: response.data.classId,
                })
            }
            else if (response.code == 401) {
                WToast.show({ data: response.message });
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }

    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '学生'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                    <ScrollView style={[CommonStyle.formContentViewStyle]}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>学生姓名</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入学生姓名'}
                                onChangeText={(text) => this.setState({ staffName: text })}
                            >
                                {this.state.staffName}
                            </TextInput>
                        </View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入学生联系电话'}
                                onChangeText={(text) => this.setState({ staffTel: text })}
                            >
                                {this.state.staffTel}
                            </TextInput>
                        </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>所属班级</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TouchableOpacity onPress={() => this.openClass()}>
                                <View style={CommonStyle.inputTextStyleTextStyle}>
                                    <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                        {!this.state.className ? "请选择所属班级" : this.state.className}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>专业绩点</Text>
                            </View>
                            <TextInput
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入学生绩点'}
                                onChangeText={
                                    (number) => {
                                        this.setState({ 
                                            gradePoint: number,
                                            comprehensivePoint: number*100 + this.state.comprehensiveAbility*this.state.adjustFactor
                                        })
                                    }
                                }
                            >
                                {this.state.gradePoint}
                            </TextInput>
                        </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>实践绩点</Text>

                            </View>
                            <TextInput
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入学生综合能力'}
                                onChangeText={
                                    (text) =>{
                                        this.setState({ 
                                            comprehensiveAbility: text ,
                                            comprehensivePoint: this.state.gradePoint*100 + text*this.state.adjustFactor

                                        })
                                    }
                                } 
                            >
                                {this.state.comprehensiveAbility}
                            </TextInput>
                        </View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>综合绩点</Text>
                            </View>
                            {
                                this.state.adjustFactor ?
                                <TextInput
                                    editable={false}
                                    keyboardType='numeric'
                                    style={styles.inputRightText}
                                    placeholder={'请输入学生绩点和综合能力'}
                                    onChangeText={(text) => this.setState({ comprehensivePoint: text })}
                                >
                                    {(this.state.comprehensivePoint/100).toFixed(2)}
                                </TextInput> :
                                <TextInput
                                    editable={false}
                                    style={styles.inputRightText}
                                    placeholder={'本租户未填写绩点计算比例，请联系管理员'}
                                >
                                </TextInput>
                                }
                          
                        </View>
                        
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>个人荣誉</Text>
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle, { height: 150 }]}>
                            <TextInput
                                multiline={true}
                                textAlignVertical="top"
                                style={[CommonStyle.inputRowText, { height: 150 }]}
                                placeholder={'请输入个人荣誉'}
                                onChangeText={(text) => this.setState({ personalHonor: text })}
                            >
                                {this.state.personalHonor}
                            </TextInput>
                        </View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>学院评价</Text>
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle, { height: 150 }]}>
                            <TextInput
                                multiline={true}
                                textAlignVertical="top"
                                style={[CommonStyle.inputRowText, { height: 150 }]}
                                placeholder={'请输入学院评价'}
                                onChangeText={(text) => this.setState({ collegeEvaluation: text })}
                            >
                                {this.state.collegeEvaluation}
                            </TextInput>
                        </View>
                        {/* <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入学生排序'}
                                onChangeText={(text) => this.setState({ staffSort: text })}
                            >
                                {this.state.staffSort}
                            </TextInput>
                        </View> */}

                        <View style={CommonStyle.btnRowStyle}>
                            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                                <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                    <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={this.saveStaff.bind(this)}>
                                <View style={CommonStyle.btnRowRightSaveBtnView}>
                                    <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <BottomScrollSelect
                            ref={'SelectClass'}
                            callBackClassValue={this.callBackSelectClassValue.bind(this)}
                        />
                    </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}

let styles = StyleSheet.create({

    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,

    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,

    },
    leftLabNameTextStyle: {
        fontSize: 18,

    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
})
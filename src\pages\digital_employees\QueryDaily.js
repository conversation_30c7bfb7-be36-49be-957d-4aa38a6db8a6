import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, FlatList,
    RefreshControl, Linking, Clipboard, TextInput, Image, ScrollView, Modal, ImageBackground,
    KeyboardAvoidingView,Platform
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class QueryDaily extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate: "",
            standardType: "E",
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 5,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            showSearchItemBlock: false,

            departmentDataSource: null,
            selDepartmentId: null,
            selDepartmentName: null,
            selDepartmentStaffList: null,
            selStaffId: null,
            selStaffName: null,
            selDepartmentStaffDataSource: [],

            qryStartTime: null,
            selectedQryStartDate: [],
            dailyState: "",
            jobUserDataSource: [],

            userPhoto: "",
            dailyItem: {},
            exportPdfModal: false,
            messageModal: false,
            messageFkId: "",
            parentMessageId: "",
            messageContent: "",
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    initqryStartTime = () => {
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 1);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _qryStartTime = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectedQryStartDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            qryStartTime: _qryStartTime
        })
        return _qryStartTime;
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        console.log('constants.loginUser================================', constants.loginUser)
        var _qryStartTime = this.initqryStartTime();
        console.log('componentWillMount==这是日报查询的页面_qryStartTime +++ ', _qryStartTime);

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { selStaffId, departmentId, departmentName, userId, userName } = route.params;
            if (selStaffId) {
                console.log("=============selStaffId" + selStaffId + "");
                this.setState({
                    selStaffId: selStaffId,
                    operate: "工作日报"
                })
            }
            else {
                console.log("=============(1)没有selStaffId  ");
                this.setState({
                    operate: "日报查询"
                })
            }
            if (departmentId) {
                console.log("=============selDepartmentId" + departmentId + "");
                this.setState({
                    selDepartmentId: departmentId,
                    selDepartmentName: departmentName,
                    selStaffId: userId,
                    selStaffName: userName
                })
            }
        }
        else {
            this.setState({
                operate: "日报查询"
            })
        }


        // 判断员工是否有职位、权重
        let loadJobTypeUrl = "/biz/job/user/list";
        let loadJobRequest = {
            "userId": constants.loginUser.userId,
        };
        httpPost(loadJobTypeUrl, loadJobRequest, (response) => {
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    jobUserDataSource: response.data.dataList,
                })
                console.log("当前的职位数据为：", this.state.jobUserDataSource)
                if (!this.state.jobUserDataSource || this.state.jobUserDataSource < 1) {
                    // if (!response.data.datalist || response.data.datalist < 1) {
                    // WToast.show({ data: "您的权限不足，请联系管理员" });
                    // return
                    Alert.alert('确认', '您的权限不足，请联系管理员', [
                        {
                            text: "取消", onPress: () => {
                                this.props.navigation.goBack()
                            }
                        },
                        {
                            text: "确定", onPress: () => {
                                this.props.navigation.goBack()
                            }
                        }
                    ]);
                }
                console.log("=======user-userId===", constants.loginUser.userId);
                console.log("=======jobUserDataSource====", this.state.jobUserDataSource);
            }

            // 部门
            let loadTypeUrl = "/biz/department/list_for_tenant";
            let loadRequest = { "qryAll": "Y", "currentPage": 1, "pageSize": 1000 };
            httpPost(loadTypeUrl, loadRequest, (response) => {
                if (response.code == 200 && response.data) {
                    this.setState({
                        departmentDataSource: response.data,
                    })
                }
            });
            const { route, navigation } = this.props;
            if (route && route.params) {
                const { selStaffId } = route.params;
                if (selStaffId) {
                    console.log("=============selStaffId" + selStaffId + "");
                    this.setState({
                        selStaffId: selStaffId,
                    })
                    this.loadDailyList(_qryStartTime, selStaffId);
                }
                else {
                    if (!this.state.jobUserDataSource || this.state.jobUserDataSource.length < 1) {
                        this.setState({
                            totalPage: 0
                        })
                        return;
                    }
                    this.loadDailyList(_qryStartTime);
                }
            }
            else {
                if (!this.state.jobUserDataSource || this.state.jobUserDataSource.length < 1) {
                    this.setState({
                        totalPage: 0
                    })
                    return
                }
                this.loadDailyList(_qryStartTime);
            }

        });
    }

    // 回调函数
    callBackFunction = () => {
        if (!this.state.jobUserDataSource || this.state.jobUserDataSource < 1) {
            WToast.show({ data: "您的权限不足，请联系管理员" });
            return
        }
        console.log("huilailllllllllllll")
        let url = "/biz/daily/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId": this.state.selDepartmentId,
            "userId": this.state.selStaffId,
            "qryStartTime": this.state.qryStartTime,
            "dailyState": "0AA"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (!this.state.jobUserDataSource || this.state.jobUserDataSource < 1) {
            WToast.show({ data: "您的权限不足，请联系管理员" });
            return
        }
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        var _qryStartTime = this.initqryStartTime();
        this.setState({
            qryStartTime: _qryStartTime,
            currentPage: 1
        })
        let loadTypeUrl = "/biz/daily/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId": this.state.selDepartmentId,
            "userId": this.state.selStaffId,
            "qryStartTime": _qryStartTime,
            "dailyState": "0AA"
        };
        httpPost(loadTypeUrl, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        if (!this.state.jobUserDataSource || this.state.jobUserDataSource < 1) {
            WToast.show({ data: "您的权限不足，请联系管理员" });
            return
        }
        this.setState({
            refreshing: true
        })
        this.loadDailyList();
    }

    loadDailyList = (_qryStartTime, selStaffId) => {
        let url = "/biz/daily/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "departmentId": this.state.selDepartmentId,
            "qryStartTime": _qryStartTime ? _qryStartTime : this.state.qryStartTime,
            "userId": selStaffId ? selStaffId : this.state.selStaffId,
            "dailyState": "0AA"
        };
        httpPost(url, loadRequest, this.loadDailyListCallBack);
    }

    loadDailyListCallBack = (response) => {
        let toastOpts;
        console.log("++++", response.code);
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            toastOpts = getFailToastOpts(response.message);
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
            this._loadFreshData();
        }
    }

    deleteDaily = (dailyId) => {
        console.log("=======delete=dailyId", dailyId);
        let url = "/biz/daily/delete";
        let requestParams = { 'dailyId': dailyId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    // 保存留言
    saveDailyMessage = () => {
        console.log("=======saveDailyMessage");
        let toastOpts;
        if (!this.state.messageContent) {
            toastOpts = getFailToastOpts("请输入留言内容");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/portal/message/board/add";
        let requestParams = {
            messageContent: this.state.messageContent,
            messageFkId: this.state.dailyItem.dailyId,
            parentMessageId: this.state.parentMessageId,
            messageFkType: "D"
        };
        httpPost(url, requestParams, this.saveDailyMessageCallBack);
    }

    // 保存留言的回调函数
    saveDailyMessageCallBack = (response) => {
        this.setState({
            messageContent: ""
        })
        let toastOpts;
        switch (response.code) {
            case 200:
                WToast.show({ data: "留言发送成功" });
                this.callBackFunction();
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    // 查看更多日报回调函数
    moreDailyCallBackFunction = (departmentId, departmentName, userId, userName) => {
        console.log('departmentId====================', departmentId)
        this.setState({
            selDepartmentId: departmentId,
            selDepartmentName: departmentName,
            selStaffId: userId,
            selStaffName: userName
        })
        this.callBackFunction();
    }

    renderRow = (item, index) => {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("DailyDetail", {
                    dailyId: item.dailyId,
                    userName: item.userName,
                    // 传递回调函数
                    refresh: this.callBackFunction,
                    moreDaily: this.moreDailyCallBackFunction
                })
            }}>

                <View key={item.dailyId} style={[CommonStyle.innerViewStyle]}>
                    {/* 日报顶部信息 */}
                    <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
                        {
                            item.userPhoto ?
                                <Image source={{ uri: (constants.image_addr + '/' + item.userPhoto)}} style={{ height: 48, width: 48, borderRadius: 50}} />
                                :
                                <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                    <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                        {
                                            item.userName.length <= 2 ? 
                                            <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                {item.userName}
                                            </Text>
                                            :
                                            <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                {item.userName.slice(-2)}
                                            </Text>
                                        }
                                    </View>
                                </ImageBackground>
                        }
                        
                        <View style={{marginLeft:11, flexDirection: 'column'}}>
                            <View style={{flexDirection: 'row', marginTop: 4 }}>
                                <View style={{ flexDirection: 'row' }}>
                                    <Text style={{ fontSize: 16 }}>{item.userName}的日报</Text>
                                </View>

                                <View style={{flexDirection: 'row'}}>
                                    {
                                        item.auditScore !== null || item.auditScore == 0 ?
                                            <View>
                                                <View style={{ width: 68, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#1BBC82' }}>
                                                    <Text style={{fontSize: 13, color: '#FFFFFF' }}>审核通过</Text>
                                                </View>
                                            </View>
                                            :
                                            <View>
                                                {
                                                    item.dailyState === "0BB" ?
                                                        <View style={{ width: 44, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: '#E63633'}}>
                                                            <Text style={{ height: 17, width: 36, color: '#FFFFFF', textAlign: 'center'}}>草稿</Text>
                                                        </View>
                                                        :
                                                        <View style={{ width: 52, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: '#FF8C28' }}>
                                                            <Text style={{fontSize: 13, color: '#FFFFFF' }}>待审核</Text>
                                                        </View>
                                                }
                                            </View>
                                    }
                                </View>
                            </View>

                            <View style={{flexDirection: 'row'}}>
                            <Image style={{ height: 13 , width: 12, marginTop: 5, marginLeft: 1, marginRight: 5}} source={require('../../assets/icon/iconfont/clock.png')}></Image> 
                                <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                                    <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)' }]}>{item.dailyDate} 提交</Text>
                                </View>
                            </View>
                        </View>
                        {
                            item.auditScore !== null || item.auditScore == 0 ? 
                                <View style={{ flexDirection: 'column', justifyContent:'center', alignContent: 'center', position: 'absolute', right: 52, top: 0}}>
                                    <Text style={{height: 30, color: "#FC0000", fontSize: 24, flexDirection: 'row', alignItems: 'center', fontWeight: '600', paddingLeft: 5}}>{item.auditScore}</Text>
                                    <Image style={{width: 46,height:14}} source={require('../../assets/icon/iconfont/scoreLine2.png')}></Image>
                                </View>
                                :
                                <View/>
                        }
                    </View>

                    {/* 分隔线 */}
                    <View style={styles.lineViewStyle} />
                    
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>完成的工作</Text>
                    </View>
                    <View style={styles.itemContentTextStyle}>
                        <Text style={styles.itemContentStyle}>{item.finishedWork}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>工作计划</Text>
                    </View>
                    <View style={styles.itemContentTextStyle}>
                        <Text style={styles.itemContentStyle}>{item.workPlan}</Text>
                    </View>
                    {
                        item.unfinishedWork ?
                            <View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>未完成工作</Text>
                                </View>
                                <View style={styles.itemContentTextStyle}>
                                    <Text style={styles.itemContentStyle}>{item.unfinishedWork}</Text>
                                </View>
                            </View>
                            :
                            <View></View>
                    }
                    {
                        item.requiresCoordinationWork ?
                            <View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>需协调工作</Text>
                                </View>
                                <View style={styles.itemContentTextStyle}>
                                    <Text style={styles.itemContentStyle}>{item.requiresCoordinationWork}</Text>
                                </View>
                            </View>
                            :
                            <View></View>
                    }
                    {/* {
                        item.auditScore !== null || item.auditScore == 0 ?
                            <View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>审核得分：{item.auditScore}</Text>
                                </View>
                                {
                                    (item.auditOpinion !== null && item.auditOpinion !== "无") ?
                                        <View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>审核意见：</Text>
                                            </View>
                                            <View style={styles.itemContentTextStyle}>
                                                <Text style={styles.itemContentStyle}>{item.auditOpinion}</Text>
                                            </View>
                                        </View>
                                        :
                                        <View></View>
                                }
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>审核人：</Text>
                                </View>
                                <View style={styles.itemContentTextStyle}>
                                    <Text style={styles.itemContentStyle}>{item.auditOperator}</Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>审核时间：</Text>
                                </View>
                                <View style={styles.itemContentTextStyle}>
                                    <Text style={styles.itemContentStyle}>{item.auditTime}</Text>
                                </View>
                            </View>
                            :
                            <View></View>
                    }
                    {
                        item.gmtModified == null ?
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>创建时间：</Text>
                                <Text style={styles.itemContentStyle}>{item.gmtCreated}</Text>
                            </View>
                            :
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>创建时间：</Text>
                                <Text style={styles.itemContentStyle}>{item.gmtModified}</Text>
                            </View>
                    } */}

                    {
                        this.state.operate == "日报查询" ?
                            <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        messageModal: true,
                                        dailyItem: item
                                    })
                                    // this.props.navigation.navigate("DailyMessageList",
                                    //     {
                                    //         // 传递参数
                                    //         messageFkId: item.dailyId,
                                    //         // 传递回调函数
                                    //         refresh: this.callBackFunction
                                    //     })
                                }}>
                                    <View style={[{
                                        width: 78, height: 28, flexDirection: "row", alignItems: 'center', margin: 10,
                                        marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                                    }]}>
                                        {/* <Image style={{ width: 24, height: 24, marginRight: 6, marginLeft: 5 }} source={require('../../assets/icon/iconfont/newMessageBlack.png')}></Image>
                                    <Text style={[{ color: 'rgba(83, 106, 247, 1)', fontSize: 14 }]}>{item.messageNum}</Text> */}
                                        <Image style={{ width: 20, height: 20, marginRight: 8, marginLeft: 12 }} source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.65)', fontSize: 14 }]}>留言</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            :
                            <View />
                    }

                    {/* 留言 */}
                    {
                        (item.messageList && item.messageList.length > 0) ?
                            <View style={{backgroundColor:'rgba(242, 245, 252, 0.5)', borderRadius:10,width:screenWidth-24, marginLeft: 12, marginRight: 12, paddingTop: 5, marginBottom: 5}}>
                                {
                                    item.messageList.map((item, index)=>{
                                        return(
                                            <View key={item.messageId} style={{ flexDirection: 'row', marginLeft: 10, marginTop: 10, paddingBottom: 10}}>
                                                {
                                                    item.operatorPhoto ?
                                                        <Image source={{ uri: (constants.image_addr + '/' + item.operatorPhoto) }} style={{ height: 36, width: 36, borderRadius: 50}} />
                                                        :
                                                        <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ height: 36, width: 36}}>
                                                            <View style={{height: 36, width: 36,justifyContent: "center",alignItems: "center"}}>
                                                                {
                                                                    item.operatorName <= 2 ? 
                                                                    <Text style={{color:'#FFFFFF',fontSize:13,fontWeight:"normal",textAlign:'center', lineHeight:20}}>
                                                                        {item.operatorName}
                                                                    </Text>
                                                                    :
                                                                    <Text style={{color:'#FFFFFF',fontSize:13,fontWeight:"normal",textAlign:'center', lineHeight:20,}}>
                                                                        {item.operatorName.slice(-2)}
                                                                    </Text>
                                                                }
                                                            </View>
                                                        </ImageBackground>
                                                }

                                                <View style={{ flexDirection: 'column', marginLeft: 10, flex: 1}}>
                                                    <View style={{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'center', paddingTop: 4 }}>
                                                        <View style={{ flexDirection: 'row'}}>
                                                            <Text style={{ fontSize: 16 }}>{item.operatorName}</Text>
                                                        </View>
                                                        <View style={{ flexDirection: 'row', marginLeft: 6}}>
                                                            <Text style={[{ fontSize: 12, color: 'rgba(0,10,32,0.45)' }]}>{item.gmtCreated.slice(0,16)}</Text>
                                                        </View>
                                                        
                                                    </View>

                                                    {
                                                        item.parentMessageId ?
                                                            <View style={[{flexDirection: 'column', justifyContent: 'flex-start'}]}>
                                                                <View style={[{flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'flex-start', marginLeft: 9, marginTop: 11}]}>
                                                                    <Text style={[styles.itemContentStyle, {color:'rgba(0,10,32,0.45)'}]}>{"回复 "+ item.parentUserName + ": "+ item.parentMessageContent}</Text>
                                                                </View>
                                                                <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 8}]}>
                                                                    <Text style={styles.itemContentStyle}>{item.messageContent}</Text>
                                                                </View>
                                                            </View>
                                                            :
                                                            <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 10}]}>
                                                                <Text style={styles.itemContentStyle}>{item.messageContent}</Text>
                                                            </View>
                                                    }
                                                </View>
                                            </View>
                                        )                           
                                    })
                                }
                            </View>
                            :
                            <View/>
                    }

                    
                </View>

            </TouchableOpacity>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }

    // 部门
    renderDepartmentRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selDepartmentId: item.departmentId,
                    selDepartmentName: item.departmentName,
                    selDepartmentStaffDataSource: item.departmentUserDTOList,
                    selStaffId: null,
                    selStaffName: null,
                })
            }}>
                <View key={"department_" + item.departmentId} style={[item.departmentId === this.state.selDepartmentId ?
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                CommonStyle.choseToSearchItemsViewSize
                ]}>
                    <Text style={[item.departmentId === this.state.selDepartmentId ?
                        CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.departmentName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }


    renderDepartmentStaffRow = (item, index) => {
        return (
            <View key={item.jobUserId} >
                <TouchableOpacity onPress={() => {
                    this.setState({
                        selStaffId: item.userId,
                        selStaffName: item.staffName,
                    })
                }}>
                    <View key={"jobuser_" + item.jobUserId} style={[item.userId === this.state.selStaffId ?
                        CommonStyle.choseToSearchItemsSelectedViewColor
                        :
                        CommonStyle.choseToSearchItemsViewColor
                        ,
                    CommonStyle.choseToSearchItemsViewSize
                    ]}>
                        <Text style={[item.userId === this.state.selStaffId ?
                            CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                        ]}>
                            {item.staffName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                if (!this.state.jobUserDataSource || this.state.jobUserDataSource < 1) {
                    WToast.show({ data: "您的权限不足，请联系管理员" });
                    return
                }
                this.setState({
                    exportPdfModal: true
                })
                // Alert.alert('确认', '您确定要导出PDF文件吗？', [
                //     {
                //         text: "取消", onPress: () => {
                //             WToast.show({ data: '点击了取消' });
                //         }
                //     },
                //     {
                //         text: "确定", onPress: () => {
                //             WToast.show({ data: '点击了确定' });
                //             this.exportPdfFile()
                //         }
                //     }
                // ]);
            }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 24, height: 24 }} source={require('../../assets/icon/iconfont/Export.png')}></Image>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    // 显示搜索项目
    showSearchItemSelect() {
        if (!this.state.jobUserDataSource || this.state.jobUserDataSource < 1) {
            WToast.show({ data: "您的权限不足，请联系管理员" });
            return
        }
        if (!this.state.departmentDataSource || this.state.departmentDataSource.length < 1) {
            WToast.show({ data: "请先添加部门" });
            return
        }
        this.setState({
            showSearchItemBlock: true,
        })
    }

    exportPdfFile = () => {
        console.log("=======exportPdfFile");
        let url = "/biz/generate/pdf/daily_query";
        // if(this.state.selDepartmentId != null  && this.state.selStaffId == null) {

        //     return ;
        // }
        // if(this.state.selStaffId == null) {
        //     WToast.show({data:'请选择提交人'});
        //     return ;
        // }
        let requestParams = {
            "qryStartTime": this.state.qryStartTime,
            "currentPage": 1,
            "pageSize": 1000,
            "departmentId": this.state.selDepartmentId,
            "userId": this.state.selStaffId,
            "dailyState": "0AA"
        };
        httpPost(url, requestParams, (response) => {
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data);
                WToast.show({ data: "导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data });
                Linking.openURL(response.data);
                // Alert.alert('确认', '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?', [
                //     {
                //         text: "不打开", onPress: () => {
                //             WToast.show({ data: '点击了不打开' });
                //         }
                //     },
                //     {
                //         text: "打开", onPress: () => {
                //             WToast.show({ data: '点击了打开' });
                //             // 直接打开外网链接 
                //             Linking.openURL(response.data)
                //         }
                //     }
                // ]);
            }
        });
    }

    openQryStartDate() {
        if (!this.state.jobUserDataSource || this.state.jobUserDataSource < 1) {
            WToast.show({ data: "您的权限不足，请联系管理员" });
            return
        }
        this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate)
    }

    callBackSelectQryStartDateValue(value) {
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryStartDate: value
        })
        if (value && value.length) {
            var qryStartTime = "";
            var vartime;
            for (var index = 0; index < value.length; index++) {
                vartime = value[index];
                if (index === 0) {
                    qryStartTime += vartime;
                }
                else {
                    qryStartTime += "-" + vartime;
                }
            }
            this.setState({
                qryStartTime: qryStartTime
            })


            let loadUrl = "/biz/daily/list";
            let loadRequest = {
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "departmentId": this.state.selDepartmentId,
                "userId": this.state.selStaffId,
                "qryStartTime": qryStartTime,
                "dailyState": "0AA"
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={this.state.operate}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.rightTop50FloatingBlockView,
                {
                    height: 32,
                    width: 110,
                    opacity: 0.6,
                    borderRadius: 8,
                    backgroundColor: "rgba(242, 245, 252, 1)"
                }]}>
                    <TouchableOpacity onPress={() => this.openQryStartDate()}>
                        <Text style={{ color: 'rgba(0,10,32,0.85)', fontSize: 14 }}>
                            {!this.state.qryStartTime ? "时间" : this.state.qryStartTime}
                        </Text>
                    </TouchableOpacity>
                </View>

                {
                    this.state.operate == "日报查询" ?
                        <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                            <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                                <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                                    {
                                        this.state.showSearchItemBlock ?
                                            <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: '#ffffff', padding: 10, margin: 5, flexDirection: 'row' }]}>
                                                <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(30,110,250,1)", paddingRight: 10 }]}>
                                                    {this.state.selDepartmentId && this.state.selDepartmentName ?
                                                        [
                                                            this.state.selStaffId && this.state.selStaffName ?
                                                                (this.state.selStaffName)
                                                                :
                                                                (this.state.selDepartmentName)
                                                        ]
                                                        :
                                                        "选择部门"}
                                                </Text>
                                                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/arrow-up.png')}></Image>
                                            </View>
                                            :
                                            <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: '#ffffff', padding: 10, margin: 5, flexDirection: 'row' }]}>
                                                {
                                                    this.state.selDepartmentId && this.state.selDepartmentName ?
                                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(30,110,250,1)", paddingRight: 10 }]}>
                                                            {/* {this.state.selDepartmentName} */}
                                                            {this.state.selDepartmentId && this.state.selDepartmentName ?
                                                                [
                                                                    this.state.selStaffId && this.state.selStaffName ?
                                                                        (this.state.selStaffName)
                                                                        :
                                                                        (this.state.selDepartmentName)
                                                                ]
                                                                :
                                                                "选择部门"}
                                                        </Text>
                                                        :
                                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(0, 10, 2, 0.45)", paddingRight: 10 }]}>
                                                            {"选择部门"}
                                                        </Text>
                                                }
                                                <Image style={{ width: 20, height: 20 }} source={require('../../assets/icon/iconfont/arrow_down.png')}></Image>
                                            </View>
                                    }
                                </TouchableOpacity>
                                {/* {
                                    this.state.selStaffId && this.state.selStaffName ? 
                                        <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                                            <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: '#ffffff', padding: 10, margin: 5, flexDirection: 'row' }]}>
                                                <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(30,110,250,1)", paddingRight: 10 }]}>
                                                    {this.state.selStaffName}
                                                </Text>
                                            </View>
                                        </TouchableOpacity>
                                        : null
                                } */}
                            </View>
                        </View>
                        :
                        <View />
                }
                <View>
                    {
                        this.state.showSearchItemBlock ?
                            <View style={[CommonStyle.choseToSearchBigBoxViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                                <View style={CommonStyle.heightLimited}>
                                    <ScrollView>
                                        <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                            <View style={[{ backgroundColor: 'rgba(255,255,255,1)' }, CommonStyle.choseToSearchItemsViewSize]}>
                                                <Text style={{ fontSize: 16, fontWeight: 'bold' }}>部门：</Text>
                                            </View>
                                            {
                                                (this.state.departmentDataSource && this.state.departmentDataSource.length > 0)
                                                    ?
                                                    this.state.departmentDataSource.map((item, index) => {
                                                        return this.renderDepartmentRow(item)
                                                    })
                                                    : null
                                            }
                                        </View>
                                        {
                                            (this.state.selDepartmentStaffDataSource && this.state.selDepartmentStaffDataSource.length > 0)
                                                ?
                                                <View style={[{
                                                    paddingTop: 13, paddingBottom: 7,
                                                    paddingLeft: 10, paddingRight: 10,
                                                    width: screenWidth,
                                                    flexWrap: 'wrap',
                                                    flexDirection: 'row',
                                                    backgroundColor: 'rgba(255,255,255,1)',
                                                    borderBottomWidth: 0.8,
                                                    borderBottomColor: "rgba(0,10,32,0.15)",
                                                    // borderColor: '#0000ff',
                                                    // borderStyle : 'solid',
                                                    // borderTopWidth: 1,
                                                    // borderTopColor: "rgba(0,10,32,0.15)"
                                                }]}>
                                                    <View style={[{ backgroundColor: 'rgba(255,255,255,1)' }, CommonStyle.choseToSearchItemsViewSize]}>
                                                        <Text style={{ fontSize: 16, fontWeight: 'bold' }}>提交人：</Text>
                                                    </View>
                                                    {
                                                        this.state.selDepartmentStaffDataSource.map((item, index) => {
                                                            return this.renderDepartmentStaffRow(item)
                                                        })
                                                    }
                                                </View>
                                                : null
                                        }
                                    </ScrollView>
                                </View>
                                <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        let loadUrl = "/biz/daily/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "departmentId": this.state.selDepartmentId,
                                            "userId": this.state.selStaffId,
                                            "qryStartTime": this.state.qryStartTime,
                                            "dailyState": "0AA"
                                        };
                                        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                        this.setState({
                                            showSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            null
                    }

                    {/* 导出pdf弹窗 */}
                    <Modal
                        animationType='fade'
                        transparent={true}
                        visible={this.state.exportPdfModal}
                        //  onShow={this.onShow.bind(this)}
                        onRequestClose={() => console.log('onRequestClose...')}
                    >
                        <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                            <View style={{ width: 291, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                                <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                    <Text style={{ fontSize: 18 }}>确认导出日报？</Text>
                                </View>
                                <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                    <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>导出地址已复制到粘贴板，使用浏览器打开</Text>
                                </View>

                                <View style={{ flexDirection: 'row', width: 291, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            exportPdfModal: false
                                        });
                                        WToast.show({ data: '点击了不打开' });
                                    }}>

                                        <View style={{ width: 145, height: 56, alignItems: 'center', justifyContent: 'center' }} >
                                            <Text style={{ fontSize: 17, fontWeight: '400', color: '#000A20', }}>不打开</Text>
                                        </View>
                                    </TouchableOpacity>

                                    <TouchableOpacity onPress={() => {
                                        WToast.show({ data: '点击了打开' });
                                        this.setState({
                                            exportPdfModal: false
                                        })
                                        this.exportPdfFile()
                                    }}>

                                        <View style={{ width: 145, height: 56, alignItems: 'center', justifyContent: 'center', borderLeftWidth: 1, borderColor: '#DFE3E8' }}>
                                            <Text style={{ fontSize: 17,fontWeight: '400', color: '#1E6EFA' }}>打开</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    {/* 留言输入框弹窗 */}
                    <Modal
                        animationType='slide'
                        transparent={true}
                        visible={this.state.messageModal}
                    >
                        <KeyboardAvoidingView
                        behavior={Platform.OS == "ios" ? "padding" : "height"}
                        style={{flex:1}}
                        >
                            <TouchableOpacity style={{ flex: 1, position: 'relative' }}
                                onPress={() => {
                                    this.setState({
                                        messageModal: false,
                                        messageContent: ""
                                    })
                                }}>
                                <View style={{
                                    backgroundColor: '#FFFFFF', flexDirection: 'row', alignItems: 'center',
                                    position: 'absolute', width: '100%', left: 0, bottom: 0, padding: 5
                                }}>
                                    <TextInput
                                        autoFocus
                                        multiline={true}
                                        placeholder="小小鼓励，让团队更凝聚"
                                        style={{ backgroundColor: '#F2F5FC', flex: 5, borderRadius: 15, height: 40, marginLeft: 10, paddingLeft: 15 }}
                                        onChangeText={(text) => this.setState({ messageContent: text })}
                                    />
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.messageContent) {
                                            return;
                                        }
                                        this.setState({
                                            messageModal: false,
                                        })
                                        this.saveDailyMessage();
                                    }}>
                                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { flex: 1, width: 64, height: 32, backgroundColor: '#1E6EFA', borderRadius: 20 },
                                        (this.state.messageContent) ? "" : CommonStyle.disableViewStyle]}>
                                            <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { textAlign: 'center', fontSize: 14 }]}>发送</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </TouchableOpacity>
                        </KeyboardAvoidingView>
                    </Modal>

                    <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                        <FlatList
                            data={this.state.dataSource}
                            renderItem={({ item, index }) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                    tintColor="#FF0000"
                                    title="loading"
                                    colors={['#FF0000', '#00FF00', '#0000FF']}
                                    progressBackgroundColor="#FFFF00"
                                    refreshing={this.state.refreshing}
                                    onRefresh={() => {
                                        this._loadFreshData()
                                    }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={() => this.flatListFooterComponent()}
                            onEndReached={() => this._loadNextData()}
                        />
                    </View>
                </View>
                <BottomScrollSelect
                    ref={'SelectQryStartDate'}
                    callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        // marginTop:10,
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        borderWidth: 8
    },

    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentTextStyle: {
        marginLeft: 14,
        marginRight: 16,
        marginTop: 3
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 14,
        marginRight: 16,
        marginTop: 5,
        alignItems:'center'
    },
    titleTextStyle: {
        fontSize: 16,
        lineHeight: 24
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        marginBottom: 6,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    }
});
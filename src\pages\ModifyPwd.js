import React, { Component } from 'react';
import {
    TouchableOpacity,
    StyleSheet,
    TextInput,
    View,
    Text,
    Dimensions, Alert, Modal,ScrollView
} from 'react-native';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../component/CommonHeadScreen';
import EmptyRowViewComponent from '../component/EmptyRowViewComponent';
import CountdownUtil from '../utils/CountdownUtil';
var CommonStyle = require('../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class ModifyPwd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            userCode: "",
            userPwd: '',
            confirmUserPwd: '',
            userName: "",
            userNbr: "",
            userEmail: "",
            tenantAbbreviation: "智慧工厂",
            tenantLogo: "http://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/jzgk_logo.jpeg",
            tenantLoginBackground: "#303F58",

            selTenantId:0,
            selTenantName:null,
            tenantsDataSource:[],
            modal:false,
            searchKeyWord:null,
            timerTitle:'获取验证码',
            isSentVerify:true,
        }
        // 上次点击登录按钮时间
        this.lastClickTime = 0
    }
    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { userId, userNbr, tenantId } = route.params;
            if (userId) {
                console.log("==========userId:", userId);
                this.setState({
                    userId:userId,
                })
            }
            if (userNbr) {
                console.log("==========userNbr:", userNbr);
                this.setState({
                    selUserNbr:userNbr,
                })
            }
            if (tenantId) {
                console.log("==========tenantId:", tenantId);
                this.setState({
                    tenantId:tenantId,
                })
            }
        }
    }


    

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View />
        )
    }

   

    // 修改密码
    onModifyPWD = () => {
        let toastOpts;
        if (!this.state.userId) {
            toastOpts = getFailToastOpts("参数错误，请返回重新进入");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.userPwd) {
            toastOpts = getFailToastOpts("请输入新密码");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.confirmUserPwd) {
            toastOpts = getFailToastOpts("请输入确认新密码");
            WToast.show(toastOpts)
            return;
        }

        if (this.state.confirmUserPwd != this.state.userPwd) {
            toastOpts = getFailToastOpts("确认密码和新密码不一致，请重新输入");
            WToast.show(toastOpts)
            return;
        }


        Alert.alert('确认', '修改密码确认提交？', [
            {
                text: "取消", onPress: () => {
                    WToast.show({ data: '点击了取消' });
                    // this在这里可用，传到方法里还有问题
                    // this.props.navigation.goBack();
                }
            },
            {
                text: "确定", onPress: () => {
                    WToast.show({ data: '点击了确定' });
                    let url= "/biz/user/account/change/pwd";
                    let requestParams={
                        'oldUserPwd':this.state.oldUserPwd,
                        'userPwd':this.state.userPwd,
                        'modifyPWDUserId':this.state.userId,
                    };
                    httpPost(url, requestParams, this.callBackModifyPwd);
                }
            }
        ]);
    }


    callBackModifyPwd=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('密码修改完成，使用新密码重新登录');
                WToast.show(toastOpts)
                this.logout();
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    logout=()=>{
        console.log("===logout");
        let requestUrl = "/biz/user/logout?a=123&b=234"
        httpGet(requestUrl, this.callBackLogout);
    }

    callBackLogout=(response)=>{
        console.log("=====callBackLogout:", response);
        this.props.navigation.navigate('LoginView');
    }

    // 订单回调加载
    callBackRegister=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                tenantsDataSource:response.data,
            })
            Alert.alert('确认', '注册成功，跳转到登录页面', [
                {
                    text: "确定", onPress: () => {
                        this.props.navigation.navigate("LoginView");
                    }
                }
            ]);
        }
        else if (response.code == 401) {
            let toastOpts = getFailToastOpts(response.message);
            WToast.show(toastOpts)
            this.props.navigation.navigate("LoginView");
        }
        else {
            let toastOpts = getFailToastOpts(response.message);
            WToast.show(toastOpts)
        }
    }

    render() {
        return (

            <View>
                <CommonHeadScreen title='修改密码'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View>
                    <View style={CommonStyle.contentViewStyle}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                当前密码
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 30) }]}
                                placeholder={'请输入当前密码'}
                                secureTextEntry={true}
                                onChangeText={(text) => this.setState({ oldUserPwd: text })}
                            >
                                {this.state.oldUserPwd}
                            </TextInput>
                        </View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                    新密码
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 30) }]}
                                placeholder={'请输入新密码'}
                                secureTextEntry={true}
                                onChangeText={(text) => this.setState({ userPwd: text })}
                            >
                                {this.state.userPwd}
                            </TextInput>
                        </View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                    确认密码
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 30) }]}
                                placeholder={'请输入确认密码'}
                                secureTextEntry={true}
                                onChangeText={(text) => this.setState({ confirmUserPwd: text })}
                            >
                                {this.state.confirmUserPwd}
                            </TextInput>
                        </View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                    电话
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 30) }]}
                                placeholder={'请输入电话'}
                                onChangeText={(text) => this.setState({ userNbr: text })}
                            >
                                {this.state.userNbr}
                            </TextInput>
                        </View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                    验证码
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={[styles.inputRightText, { width: (screenWidth - (leftLabWidth + 30)) / 2 }]}
                                placeholder={'验证码'}
                                onChangeText={(text) => this.setState({ verificationCode: text })}
                            >
                                {this.state.verificationCode}
                            </TextInput>
                            <TouchableOpacity onPress={()=>{
                                if (!this.state.isSentVerify) {
                                    return;
                                }
                                let toastOpts;
                                if (!this.state.tenantId) {
                                    toastOpts = getFailToastOpts("请指定租户");
                                    WToast.show(toastOpts)
                                    return;
                                }
                                if (!this.state.userNbr) {
                                    toastOpts = getFailToastOpts("请输入电话");
                                    WToast.show(toastOpts)
                                    return;
                                }
                                if (!isMobileNumber(this.state.userNbr)) {
                                    toastOpts = getFailToastOpts("电话格式错误");
                                    WToast.show(toastOpts)
                                    return;
                                }

                                if (this.state.selUserNbr && isMobileNumber(this.state.selUserNbr) && this.state.selUserNbr != this.state.userNbr) {
                                    let userNbrStrLast = this.state.selUserNbr.slice(this.state.selUserNbr.length - 1, this.state.selUserNbr.length);
                                    let userNbrStrSliceLast5 = this.state.selUserNbr.slice(0, this.state.selUserNbr.length - 5);
                                    toastOpts = getFailToastOpts("与预留的号码不一致(" + userNbrStrSliceLast5 + "****" + userNbrStrLast + ")");
                                    WToast.show(toastOpts)
                                    return;
                                }

                                this.setState({
                                    isSentVerify: false
                                });
                                let requestUrl = "/biz/user/send_verification_sms_by_module_code";
                                let requestParams = {
                                    "accNbr": this.state.userNbr,
                                    "moduleCode": "M",
                                    "tenantId" : this.state.tenantId,
                                };
                                httpPost(requestUrl, requestParams, (response)=>{
                                    if (response.code == 200 && response.data && response.data === true) {
                                        WToast.show({ data: '验证码已发送' });

                                        // 倒计时时间
                                        let countdownDate = new Date(new Date().getTime() + 60 * 1000)
                                        // 点击之后验证码不能发送网络请求
                                        this.setState({
                                            isSentVerify: false
                                        });
                                        CountdownUtil.settimer(countdownDate, (time) => {
                                            this.setState({
                                              timerTitle: time.sec > 0 ? time.sec + 's' : '重新获取'
                                            }, () => {
                                              if (this.state.timerTitle == "重新获取") {
                                                this.setState({
                                                  isSentVerify: true
                                                })
                                              }
                                            })
                                        })
                                    }
                                    else if (response.code == 200 && response.data && response.data === false) {
                                        WToast.show({ data: '验证码发送失败' });
                                        this.setState({
                                            isSentVerify: true
                                        });
                                    }
                                    else if (response.code != 200 && response.message) {
                                        WToast.show({ data: response.message });
                                        this.setState({
                                            isSentVerify: true
                                        });
                                    }
                                    else if (response.message) {
                                        WToast.show({ data: response.message });
                                        this.setState({
                                            isSentVerify: true
                                        });
                                    }
                                });

                            }}>
                                <View style={[styles.loginBtnStyle, { opacity: this.state.isSentVerify ? 1 : 0.6, width: (screenWidth - (leftLabWidth + 30)) / 2 - 5}]}>
                                    <Text style={{ color: 'white', fontSize: 16, }}>{this.state.timerTitle}</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={{padding:10 }}>
                            <TouchableOpacity
                                onPress={this.onModifyPWD.bind(this)}>
                                <View style={styles.loginBtnStyle}>
                                    <Text style={{ color: 'white', fontSize: 20, fontWeight: 'bold' }}>修改密码</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>

                </View>


            </View>
        );
    }
}

const styles = StyleSheet.create({
    // contentViewStyle:{
    //     backgroundColor:'#FFFFFF',
    //     height:screenHeight - 140
    // },
    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    loginBtnStyle: {
        width: screenWidth * 0.76,
        height: 45,
        backgroundColor: '#CE3C27',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 8,
        alignSelf:'center'
    },
})
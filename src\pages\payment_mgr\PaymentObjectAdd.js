import React,{Component} from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,KeyboardAvoidingView,TouchableOpacity,Dimensions,Picker,Image} from 'react-native';
import {WToast} from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
var CommonStyle = require('../../assets/css/CommonStyle');
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class PaymentObjectAdd extends Component {
    constructor(props) {
        super(props);
        this.state ={
            operate:"",
            customerId:'',
            customerName:'',
            customerAddr:'',
            customerConcat:'',
            customerTel:'',
            tin:'',
            bankOfDeposit:'',
            bankAccount:'',
            bankAccountHolder:'',
        }
    }
    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { customerId } = route.params;
            if (customerId) {
                console.log("========Edit==customerId:", customerId);
                this.setState({
                    customerId:customerId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/tenant/customer/get";
                loadRequest={'customerId':customerId};
                httpPost(loadTypeUrl, loadRequest, this.callBackLoadCustomerData);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    callBackLoadCustomerData=(response)=>{
        if (response.code == 200 && response.data) {
            console.log("======load==edit=obj=", response.data);
            this.setState({
                customerId:response.data.customerId,
                customerName:response.data.customerName,
                customerAddr:response.data.customerAddr,
                customerConcat:response.data.customerConcat,
                customerTel:response.data.customerTel,
                tin:response.data.tin,
                bankOfDeposit:response.data.bankOfDeposit,
                bankAccount:response.data.bankAccount,
                bankAccountHolder:response.data.bankAccountHolder,
            })
            console.log("=======this.state", this.state);
        }
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.navigate("PaymentObjectList") }}>
                <Text style={CommonStyle.headRightText}>支付对象</Text>
            </TouchableOpacity>
        )
    }

    saveCustomer =()=> {
        console.log("=======saveCustomer");
        let toastOpts;
        if (!this.state.customerName) {
            toastOpts = getFailToastOpts("请输入支付对象");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.bankAccountHolder) {
            toastOpts = getFailToastOpts("请输入开户名");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.bankOfDeposit) {
            toastOpts = getFailToastOpts("请输入开户银行");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.bankAccount) {
            toastOpts = getFailToastOpts("请输入银行账号");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/tenant/customer/add";
        if(this.state.customerId) {
            console.log("=========Edit===customerId", this.state.customerId)
            url= "/biz/tenant/customer/modify";
        }
        let requestParams={
            customerId:this.state.customerId,
            customerName:this.state.customerName,
            customerAddr:this.state.customerAddr,
            customerConcat:this.state.customerConcat,
            customerTel:this.state.customerTel,
            tin:this.state.tin,
            bankOfDeposit:this.state.bankOfDeposit,
            bankAccount:this.state.bankAccount,
            bankAccountHolder:this.state.bankAccountHolder,
            customerType:"P"
        };
        httpPost(url, requestParams, this.saveCustomer_call_back);
    }

    // 保存回调函数
    saveCustomer_call_back=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
       return(
        <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
        {/* <TopScreen title='新建订单'/> */}
            <CommonHeadScreen title={this.state.operate + '支付对象'}
                leftItem={() => this.renderLeftItem()}
                rightItem={() => this.renderRightItem()}
            />
            <ScrollView style={CommonStyle.formContentViewStyle}>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>
                            支付对象
                        </Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput 
                        style={styles.inputRightText}
                        placeholder={'请输入支付对象'}
                        onChangeText={(text) => this.setState({customerName:text})}
                    >
                        {this.state.customerName}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <TextInput 
                        style={styles.inputRightText}
                        placeholder={'请输入联系人'}
                        onChangeText={(text) => this.setState({customerConcat:text})}
                    >
                        {this.state.customerConcat}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <TextInput 
                        keyboardType='numeric'
                        style={styles.inputRightText}
                        placeholder={'请输入联系电话'}
                        onChangeText={(text) => this.setState({customerTel:text})}
                    >
                        {this.state.customerTel}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>地址</Text>
                        <Text style={styles.leftLabRedTextStyle}></Text>
                    </View>
                    <TextInput 
                        style={styles.inputRightText}
                        placeholder={'请输入地址'}
                        onChangeText={(text) => this.setState({customerAddr:text})}
                    >
                        {this.state.customerAddr}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>纳税人识别号</Text>
                    </View>
                    <TextInput 
                        style={styles.inputRightText}
                        placeholder={'请输入纳税人识别号'}
                        onChangeText={(text) => this.setState({tin:text})}
                    >
                        {this.state.tin}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>开户名</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput 
                        style={styles.inputRightText}
                        placeholder={'请输入开户名'}
                        onChangeText={(text) => this.setState({bankAccountHolder:text})}
                    >
                        {this.state.bankAccountHolder}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>开户银行</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput 
                        style={styles.inputRightText}
                        placeholder={'请输入开户银行'}
                        onChangeText={(text) => this.setState({bankOfDeposit:text})}
                    >
                        {this.state.bankOfDeposit}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>银行账号</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput 
                        keyboardType='numeric'
                        style={styles.inputRightText}
                        placeholder={'请输入账号'}
                        onChangeText={(text) => this.setState({bankAccount:text})}
                    >
                        {this.state.bankAccount}
                    </TextInput>
                </View>
                <View style={CommonStyle.btnRowStyle}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView]} >
                        <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveCustomer.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView]}>
                        <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
       )

    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     backgroundColor:'#FFFFFF',
    //     height:screenHeight - 140
    // },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})
import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image, TextInput,Modal
} from 'react-native';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ScrollView } from 'react-native-gesture-handler';
var CommonStyle = require('../../assets/css/CommonStyle');
import { WebView } from 'react-native-webview';
import ImageViewer from 'react-native-image-zoom-viewer';
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import EmptyPortalTenantComponent from '../../component/EmptyPortalTenantComponent';
const {ifIphoneXContentViewHeight} = require('../../utils/ScreenUtil');

const leftLabWidth = 130;
export default class ConfigPreview extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            pictureIndex: 0,
            configContent: "",
            webViewHeight: 0
        }
    }

 


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { configTitle, configContent } = route.params;
            console.log("configTitle===", configTitle)
            console.log("configContent===", configContent)
            if (configTitle) {
                this.setState({
                    configTitle: configTitle
                })
            }
            if (configContent) {
                this.setState({
                    configContent: configContent
                })
            }
        }
        
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity> 
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyPortalTenantComponent/>
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render(){
        var htmlTemplate = '<!DOCTYPE html>'+
                        '<html lang="en">'+
                        '<script src="https://code.jquery.com/jquery-3.6.0.min.js">' 
                        + '</script>' +
                        '<head >'+
                            '<meta charset="UTF-8">'+
                            '<meta http-equiv="X-UA-Compatible" content="IE=edge"></meta>'+
                            '<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />' +
                        '<style>'+  
                            '.img-width{max-width: 100%;}' +
                        '</style>'+
                        '<script>' +
                        '$(document).ready(function (event) { ' +
                            '$("img").addClass("img-width");' +
                           ' }) ' +
                         '</script>' +
                            '</head>'+
                        '<body>'+
                         //'<div style={{width=200px}}>'
                            this.state.configContent +
                        // '</div>'
                        '</body>'+
                        '</html>'
        return(
            <View>
                <CommonHeadScreen title={"图文预览"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    {/* <View style={[styles.titleViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                        <Text style={[styles.titleTextStyle]}>{this.state.configTitle}</Text>
                    </View> */}
                    <View style={{flex: 1}}>
                         <WebView
                            scalesPageToFit={true}
                            scrollEnabled={true}
                            source={{html: htmlTemplate}}
                            style={{width: screenWidth, height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}}>
                        </WebView> 
                    </View>
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    titleViewStyle:{
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 5,
    },
    titleTextStyle:{
        fontSize: 22,
        // lineHeight: 22,
        fontWeight: "bold"
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    lineViewStyle:{
        // height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 10,
        marginBottom: 5,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },

});
// package com.sp_mobile_front_jznc.push;


// import com.facebook.react.ReactPackage;
// import com.facebook.react.bridge.JavaScriptModule;
// import com.facebook.react.bridge.NativeModule;
// import com.facebook.react.bridge.ReactApplicationContext;
// import com.facebook.react.uimanager.ViewManager;

// import java.util.ArrayList;
// import java.util.Collections;
// import java.util.List;

// /**
//  * Created by liyazhou on 17/5/18.
//  */

// public class PushPackage implements ReactPackage {
//     @Override
//     public List<NativeModule> createNativeModules(ReactApplicationContext reactContext) {
//         List<NativeModule> modules = new ArrayList<>();
//         modules.add(new PushModule(reactContext));
//         return modules;
//     }

//     @Override
//     public List<ViewManager> createViewManagers(ReactApplicationContext reactContext) {
//         return Collections.emptyList();
//     }
// }



import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,TextInput,ScrollView,KeyboardAvoidingView,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
var screenHeight = Dimensions.get('window').height;
export default class AssessApplyAdd extends Component {
    constructor() {
        super()
        this.state = {
            assessRecordId: "",
            assessRecordState: "",
            applyDate: "",
            assessTitle:"",
            assessContent:"",
            assessClassId:"",
            assessUserId:"",
            assessUserName:"",
            selAssessUserId:"",
            selAssessUserName:"",
            expectAssessDate:"",
            operate: "",
            selectedApplyDate:[],
            selectedExpectAssessDate:[],
            selAssessClassId:null,
            assessClassName:"",
            selAssessClassName:null,
            assessClassIdDataSource:[],
            selectedAssessClass:[],
            //员工相关数据
            userModal:false,
            userSearchKeyWord:"",
            userId:"",
            userName:"",
            userDataSource:[],
            _userDataSource:[],
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');

        this.loadAssessClassList();
        this.loadUserList();
        // console.log("==========考核类别数据源：", this.state.assessClassIdDataSource);
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { assessRecordId } = route.params;
            if (assessRecordId ) {
                console.log("========Edit==assessRecordId:", assessRecordId);
                this.setState({
                    operate: "编辑",
                    assessRecordId:assessRecordId
                    
                })
                loadTypeUrl= "/biz/assess/record/get";
                loadRequest={'assessRecordId':assessRecordId};
                httpPost(loadTypeUrl, loadRequest, this.loadAssessApplyCallBack);

            }
            else {
                this.setState({
                    operate: "新增",
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectedApplyDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    applyDate: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                })
                var dateString = this.state.applyDate + ' 00:00:01';
                dateString = dateString.substring(0, 19);
                dateString = dateString.replace(/-/g, '/');
                var dateStringTimestamp = new Date(dateString).getTime();
                // 根据毫秒数构建 Date 对象
                var ThreeDaysLast = new Date(dateStringTimestamp);
                //获取当前时间的毫秒数
                var nowMilliSeconds = currentDate.getTime();
                // 用获取毫秒数 加上30天的毫秒数 赋值给ThreeDaysLast对象（一天有86400000毫秒）
                ThreeDaysLast.setTime(nowMilliSeconds + (3 * 86400000));
                //通过赋值后的ThreeDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
                //格式化月，如果小于9，前面补0
                var ThreeDaysLastOfMonth = ("0" + (ThreeDaysLast.getMonth() + 1)).slice(-2);
                //格式化日，如果小于9，前面补0
                var ThreeDaysLastOfDay = ("0" + ThreeDaysLast.getDate()).slice(-2);
                this.setState({
                    selectedExpectAssessDate: [ThreeDaysLast.getFullYear(), ThreeDaysLastOfMonth, ThreeDaysLastOfDay],
                    expectAssessDate: ThreeDaysLast.getFullYear() + "-" + ThreeDaysLastOfMonth + "-" + ThreeDaysLastOfDay
                })
            }
        }
    }


    //考核类别选择
    loadAssessClassList = () => {
        let url = "/biz/assess/class/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadAssessClassListCallBack);
    }

    loadAssessClassListCallBack = (response) => {
        if (response.code == 200 && response.data.dataList) {
            this.setState({
                assessClassIdDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    //查询员工方法
    loadUserList=()=>{
        let url= "/biz/portal/user/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadUserListCallBack);
    }
    loadUserListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataSource = response.data.dataList;
            dataSource = dataSource.filter(item => item.userId != constants.loginUser.userId)
            this.setState({
                userDataSource:dataSource
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    loadUser = () => {
        var _userDataSource = copyArr(this.state.userDataSource);
        if (this.state.userSearchKeyWord && this.state.userSearchKeyWord.length > 0) {
            _userDataSource = _userDataSource.filter(item => item.userName.indexOf(this.state.userSearchKeyWord) > -1);
        }
        this.setState({
            _userDataSource: _userDataSource,
        })
    }


    loadAssessApplyCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            var selectedApplyDate = response.data.applyDate.split("-");
            var selectedExpectAssessDate = [];
            if (response.data.expectAssessDate) {
                selectedExpectAssessDate = response.data.expectAssessDate.split("-");
            }
            this.setState({
                assessRecordId:response.data.assessRecordId,
                assessTitle:response.data.assessTitle,
                assessContent:response.data.assessContent,
                applyDate:response.data.applyDate,
                selectedApplyDate:selectedApplyDate,
                expectAssessDate:response.data.expectAssessDate,
                selectedExpectAssessDate:selectedExpectAssessDate,
                selectedAssessClass:[response.data.assessClassName],
                assessClassName:response.data.assessClassName,
                selAssessUserId: response.data.assessUserId,
                selAssessUserName:response.data.assessUserName,
            })
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("AssessApplyList")
            }}>
                <Text style={CommonStyle.headRightText}>考核申请</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveAssessApply =()=> {
        console.log("=======saveAssessApply");
        let toastOpts;
        if (!this.state.applyDate) {
            toastOpts = getFailToastOpts("请填写申请日期");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.assessTitle) {
            toastOpts = getFailToastOpts("请填写考核标题");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.assessContent) {
            toastOpts = getFailToastOpts("请填写考核内容");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.assessClassName) {
            toastOpts = getFailToastOpts("请选择考核类别");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selAssessUserId) {
            toastOpts = getFailToastOpts("请选择考核人");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.expectAssessDate) {
            toastOpts = getFailToastOpts("请选择预计考核日期");
            WToast.show(toastOpts)
            return;
        }


        let url= "/biz/assess/record/add";
        if (this.state.assessRecordId) {
            console.log("=========Edit===assessRecordId", this.state.assessRecordId)
            url= "/biz/assess/record/modify";
        }
        let requestParams={
            "assessRecordId":this.state.assessRecordId,
            "applyDate":this.state.applyDate,
            "applyUserId":constants.loginUser.userId,
            "assessTitle":this.state.assessTitle,
            "assessContent":this.state.assessContent,
            "assessClassId":this.state.assessClassId,
            "expectAssessDate":this.state.expectAssessDate,
            "assessUserId":this.state.selAssessUserId,

        };
        httpPost(url, requestParams, this.saveAssessApplyCallBack);
    }

    //员工显示组件
    renderUserRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selAssessUserId: item.userId,
                    selAssessUserName:item.userName,
                })

            }}>
                <View key={item.userId} style={[item.userId === this.state.selAssessUserId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.userId === this.state.selAssessUserId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.userName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 保存回调函数
    saveAssessApplyCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }


    openApplyDate(){
        this.refs.SelectApplyDate.showDate(this.state.selectedApplyDate)
    }
    callBackSelectSelectApplyDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedApplyDate:value
        })
        if (value && value.length) {
            var applyDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    applyDate += vartime;
                }
                else{
                    applyDate += "-" + vartime;
                }
            }
            this.setState({
                applyDate:applyDate
            })
        }
        var dateString = this.state.applyDate + ' 00:00:01';
        dateString = dateString.substring(0, 19);
        dateString = dateString.replace(/-/g, '/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var ThreeDaysLast = new Date(dateStringTimestamp);
        // 用获取毫秒数 加上30天的毫秒数 赋值给ThreeDaysLast对象（一天有86400000毫秒）
        ThreeDaysLast.setTime(dateStringTimestamp + (3 * 86400000));
        //通过赋值后的ThreeDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
        //格式化月，如果小于9，前面补0
        var ThreeDaysLastOfMonth = ("0" + (ThreeDaysLast.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0
        var ThreeDaysLastOfDay = ("0" + ThreeDaysLast.getDate()).slice(-2);
        this.setState({
            selectedExpectAssessDate: [ThreeDaysLast.getFullYear(), ThreeDaysLastOfMonth, ThreeDaysLastOfDay],
            expectAssessDate: ThreeDaysLast.getFullYear() + "-" + ThreeDaysLastOfMonth + "-" + ThreeDaysLastOfDay
        })
        if (this.state.selectedExpectAssessDate && this.state.selectedExpectAssessDate.length) {
            var expectAssessDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectedExpectAssessDate.length; index++) {
                vartime = this.state.selectedExpectAssessDate[index];
                if (index === 0) {
                    expectAssessDate += vartime;
                }
                else {
                    expectAssessDate += "-" + vartime;
                }
            }
            this.setState({
                expectAssessDate: expectAssessDate
            })
        }
    }


    openExpectAssessDate(){
        this.refs.SelectExpectAssessDate.showDate(this.state.selectedExpectAssessDate)
    }
    callBackSelectSelectExpectAssessDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedExpectAssessDate:value
        })
        if (value && value.length) {
            var expectAssessDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    expectAssessDate += vartime;
                }
                else{
                    expectAssessDate += "-" + vartime;
                }
            }
            this.setState({
                expectAssessDate:expectAssessDate
            })
        }
    }


    openAssessClassIdSelect() {
        if (!this.state.assessClassIdDataSource || this.state.assessClassIdDataSource.length < 1) {
            WToast.show({ data: "请先添加考核类别" });
            return
        }
        this.refs.SelectAssessClassId.showAssessClassId(this.state.selectedAssessClass, this.state.assessClassIdDataSource)
    }

    callBackAssessClassIdValue(value) {
        // console.log("==========考核类别选择的结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedAssessClass: value,
        })
        var assessClassName = value.toString();
        let loadUrl = "/biz/assess/class/getAssessClassByName";
        let loadRequest = {
            "assessClassName": assessClassName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadAssessClassData);
    }
    callBackLoadAssessClassData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                assessClassName: response.data.assessClassName,
                assessClassId:response.data.assessClassId
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }




    render() {
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + "申请"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>申请日期</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openApplyDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 5)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.applyDate ? "请选择申请日期" : this.state.applyDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核标题</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入考核标题'}
                            onChangeText={(text) => this.setState({ assessTitle: text })}
                        >
                            {this.state.assessTitle}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核内容</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:150}]}
                            placeholder={'请输入考核内容'}
                            onChangeText={(text) => this.setState({assessContent:text})}
                        >
                            {this.state.assessContent}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核类别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openAssessClassIdSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 5)}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.assessClassName ? "请选择考核类别" : this.state.assessClassName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核人</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        
                        <View style={[{flexWrap:'wrap'}, this.state.assessRecordId? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
            

                                if (this.state.userDataSource && this.state.userDataSource.length > 0) {
                                    this.setState({
                                        _userDataSource: copyArr(this.state.userDataSource)
                                    })
                                }
                                this.setState({
                                    userModal: true,
                                    userSearchKeyWord: ""  //部门的搜索关键字
                                })
                                if (!this.state.selAssessUserId && this.state._userDataSource && this.state._userDataSource.length > 0) {
                                    this.setState({
                                        selAssessUserId: this.state._userDataSource[0].userId,
                                        selAssessUserName: this.state._userDataSource[0].userName,
                                        

                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, { flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                {
                                    this.state.selAssessUserId && this.state.selAssessUserName ?
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>{this.state.selAssessUserName}</Text>
                                    :
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>选择考核人</Text>
                                }
                                </View>
                            </TouchableOpacity>
                        </View>
                        <Modal
                            animationType={'slide'}
                            transparent={true}
                            onRequestClose={() => console.log('onRequestClose...')}
                            visible={this.state.userModal}>
                            <View style={CommonStyle.fullScreenKeepOut}>
                                <View style={CommonStyle.modalContentViewStyle}>
                                    <View style={CommonStyle.rowLabView}>
                                        <View style={styles.leftLabViewImage}>
                                            <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                            <TextInput
                                                style={[styles.searchInputText]}
                                                returnKeyType="search"
                                                returnKeyLabel="搜索"
                                                onSubmitEditing={e => {
                                                    this.loadUser();
                                            }}        
                                                placeholder={'输入考核人姓名'}
                                                onChangeText={(text) => this.setState({ userSearchKeyWord: text })}
                                            >
                                                {this.state.userSearchKeyWord}
                                            </TextInput>
                                        </View>
                                    </View>
                                    <ScrollView style={{}}>
                                        <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                            {
                                                (this.state._userDataSource && this.state._userDataSource.length > 0)
                                                    ?
                                                    this.state._userDataSource.map((item, index) => {
                                                        if (index < 1000) {
                                                            return this.renderUserRow(item)
                                                        }
                                                    })
                                                    : <EmptyRowViewComponent />
                                            }
                                        </View>
                                    </ScrollView>
                                    <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                        <TouchableOpacity onPress={() => {
                                            this.setState({
                                                userModal: false,
                                            })
                                        }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                        </TouchableOpacity>
                                        <TouchableOpacity onPress={() => {
                                            if (!this.state.selAssessUserId) {
                                                let toastOpts = getFailToastOpts("您还没有选择考核人");
                                                WToast.show(toastOpts);
                                                return;
                                            }

                                            this.setState({
                                                userModal: false,
                                            })
                                        }}>
                                            <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                                <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                                <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>
                        </Modal>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>预计考核日期</Text>
                            <Text style={[styles.leftLabRedTextStyle,{marginLeft:1}]}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openExpectAssessDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 5)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.expectAssessDate ? "请选择预计考核日期" : this.state.expectAssessDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveAssessApply.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                <BottomScrollSelect 
                    ref={'SelectApplyDate'} 
                    callBackDateValue={this.callBackSelectSelectApplyDateValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectExpectAssessDate'} 
                    callBackDateValue={this.callBackSelectSelectExpectAssessDateValue.bind(this)}
                />

                <BottomScrollSelect
                    ref={'SelectAssessClassId'}
                    callBackAssessClassIdValue={this.callBackAssessClassIdValue.bind(this)}
                />
            </View>
        );
    }
}

const styles = StyleSheet.create({

    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    searchInputText: {
        width: screenWidth -100,
        // borderColor: '#000000',
        // borderBottomWidth: 1,
        // marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        // marginLeft: 0,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabViewImage: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        borderWidth:1,
        borderColor:"#E4E4E4",
        borderRadius:5,
        marginTop:5,
        // marginRight:5
    },  
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }

});
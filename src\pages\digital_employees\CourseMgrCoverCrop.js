import React, { Component } from 'react';
import {
  Image,
  StyleSheet,
  Text,
  TouchableHighlight,
  View,Dimensions,TouchableOpacity,ImageBackground,Modal,Animated,PanResponder
} from 'react-native';
import { uploadImageLibraryWithCrop } from '../../utils/UploadImageUtils';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class CourseMgrCoverCrop extends Component{

  constructor(props) {
    super(props)
    this.state={
      //是否打开
      visible:false,
      //图片路径
      coursePhoto:"",
      //原有图片路径
      oldFile:"",
      //图片实际高度
      photoHeight:null,
      //图片实际宽度
      photoWidth:null,
      //图片展示高度
      height:null,
      //图片展示宽度
      width:null,
      //裁剪框横纵比
      bili:0.64,
      //图片文件名
      name:null,

      //裁剪框初始大小
      corpBoxdefaultWith:100,
      //裁剪框边界
      corpBoxMinWith:50,
      corpBoxLeftBorder:10,
      corpBoxRightBorder:props.width-40,
      corpBoxBottomBorder:10,
      corpBoxTopBorder:props.height-40,
    }
  }

  sx1 = new Animated.Value(10);
  sy1 = new Animated.Value(10);
  sx2 = new Animated.Value(10+100);
  sy2 = new Animated.Value(10+100*0.64);

    a=new Animated.Value(0);

    yellow_flag=false;
    green_flag=false;
    red_flag=false;
    blue_flag=false;
    white_flag=false;
    picture_flag=false

    x1=10;y1=10;x2=10+100;y2=10+100*0.64;

    initDistend=0;

    UNSAFE_componentWillMount(){
        console.log(this.props.srcPhotoData)
        this.sx1.addListener((params)=>{
            // console.log("异步监听sx1",params)
            // console.log("异步监听sx1",this.b)
        })
        this.sy1.addListener((params)=>{
            // console.log("异步监听sy1",params)
        })
        this.sx2.addListener((params)=>{
            // console.log("异步监听sx2",params)
        })
        this.sy2.addListener((params)=>{
            // console.log("异步监听sy2",params)
        })
    }

    openCropModal=( res )=>{
      console.log("===openCropModal===",JSON.stringify(res, null, 6))
      if(res==null){
        return;
      }
      console.log(this.sx1,this.sy1,this.sx2,this.sy2)
      this.sx1.flattenOffset();
      this.sy1.flattenOffset();
      this.sx2.flattenOffset();
      this.sy2.flattenOffset();
      this.sx1.setValue(10)
      this.sy1.setValue(10)
      this.sx2.setValue(10+100)
      this.sy2.setValue(10+100*0.64)
      let height
      let width
      if(res.assets[0].width/res.assets[0].height<0.5625)
      {
        let bili=res.assets[0].height/(screenHeight-500)
         height=screenHeight-500
         width=res.assets[0].width/bili
        
      }
      else{
        let bili=res.assets[0].width/(screenWidth-30-40)
         height=res.assets[0].height/bili
         width=(screenWidth-30-40)                  
      }

      this.setState({

        oldFile:res.oldFile,

        visible:true,
        coursePhoto:res.assets[0].uri,
        height:height,
        width:width,
        photoHeight:res.assets[0].height,
        photoWidth:res.assets[0].width,
        name:res.assets[0].fileName,

        corpBoxMinWith:50,
        corpBoxLeftBorder:10,
        corpBoxRightBorder:width+10,
        corpBoxBottomBorder:10,
        corpBoxTopBorder:height+10,
      })
    }

    closeCropModal=()=>{
      console.log("===openCropModal===")
      this.setState({
        visible:false
      })
    }

    yellow = PanResponder.create({
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        console.log("点击")
        this.x1=Number(JSON.stringify(this.sx1, null, 6))
        this.y1=Number(JSON.stringify(this.sy1, null, 6))
        this.sx1.setOffset(this.sx1._value)
        this.sy1.setOffset(this.sy1._value)
      },
      onPanResponderMove: 
      (event,gestureState)=>{
        
        if(
                gestureState.dx+this.x1<this.x2-this.state.corpBoxMinWith
            && gestureState.dx+this.x1>this.state.corpBoxLeftBorder
            && gestureState.dx*0.64+this.y1>this.state.corpBoxBottomBorder
        )
        {
            this.sx1.setValue(gestureState.dx)
            this.sy1.setValue(gestureState.dx*0.64)
            this.yellow_flag=true
            // console.log("yellow移动",this.sx1,this.sy1,this.sx2,this.sy2)                      
        }
        if(gestureState.dx+this.x1>this.x2-this.state.corpBoxMinWith)
        {
          let d=this.x2-this.state.corpBoxMinWith-this.x1
          this.sx1.setValue(d)
          this.sy1.setValue(d*0.64)
          this.yellow_flag=true
        }
        if(gestureState.dx+this.x1<this.state.corpBoxLeftBorder && gestureState.dx*0.64+this.y1>this.state.corpBoxBottomBorder)
        {
          let d=this.state.corpBoxLeftBorder-this.x1
          this.sx1.setValue(d)
          this.sy1.setValue(d*0.64)
          this.yellow_flag=true
        }
        if(gestureState.dx+this.x1>this.state.corpBoxLeftBorder && gestureState.dx*0.64+this.y1<this.state.corpBoxBottomBorder)
        {
          let d=this.state.corpBoxBottomBorder-this.y1
          this.sx1.setValue(d/0.64)
          this.sy1.setValue(d)
          this.yellow_flag=true
        }
      },
      onPanResponderRelease: (event,gestureState) => {
        console.log("松开")
        if(this.yellow_flag==false)
        {
          this.sx1.setValue(0)
          this.sy1.setValue(0)            
        }else{
          this.yellow_flag=false
        }
        this.sx1.flattenOffset();
        this.sy1.flattenOffset();
        this.x1=Number(JSON.stringify(this.sx1, null, 6))
        this.y1=Number(JSON.stringify(this.sy1, null, 6))
        }
    });

    green = PanResponder.create({
        onMoveShouldSetPanResponder: () => true,
        onPanResponderGrant: () => {
          console.log("点击")
          this.x1=Number(JSON.stringify(this.sx1, null, 6))
          this.y2=Number(JSON.stringify(this.sy2, null, 6))
          this.sx1.setOffset(this.sx1._value)
          this.sy2.setOffset(this.sy2._value)
        },
        onPanResponderMove: 
            (event,gestureState)=>{
              if(gestureState.dx+this.x1<this.x2-this.state.corpBoxMinWith
                  && gestureState.dx+this.x1>this.state.corpBoxLeftBorder
                  && gestureState.dx*(-0.64)+this.y2<this.state.corpBoxTopBorder
                  ){
                  this.sx1.setValue(gestureState.dx)
                  this.sy2.setValue(gestureState.dx*(-0.64))  
                  this.green_flag=true
                  console.log("green移动",this.sx1,this.sy1,this.sx2,this.sy2)                  
              }
              if(gestureState.dx+this.x1>this.x2-this.state.corpBoxMinWith)
              {
                let d=this.x2-this.state.corpBoxMinWith-this.x1
                this.sx1.setValue(d)
                this.sy2.setValue(d*(-0.64))
                this.green_flag=true
              }
              if(gestureState.dx+this.x1<this.state.corpBoxLeftBorder && gestureState.dx*(-0.64)+this.y2<this.state.corpBoxTopBorder)
              {
                let d=this.state.corpBoxLeftBorder-this.x1
                this.sx1.setValue(d)
                this.sy2.setValue(d*(-0.64))
                this.green_flag=true
              }
              if(gestureState.dx+this.x1>this.state.corpBoxLeftBorder && gestureState.dx*(-0.64)+this.y2>this.state.corpBoxTopBorder)
              {
                let d=this.state.corpBoxTopBorder-this.y2
                this.sx1.setValue(d/(-0.64))
                this.sy2.setValue(d)
                this.green_flag=true
              }
            },
        onPanResponderRelease: (event,gestureState) => {
          console.log("松开")
          if(this.green_flag==false)
          {
          this.sx1.setValue(0)
          this.sy2.setValue(0)            
          }else{
            this.green_flag=false
          }
          this.sx1.flattenOffset();
          this.sy2.flattenOffset();
          this.x1=Number(JSON.stringify(this.sx1, null, 6))
          this.y2=Number(JSON.stringify(this.sy2, null, 6))
        }
      });

    blue = PanResponder.create({
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        console.log("点击")
        this.x2=Number(JSON.stringify(this.sx2, null, 6))
        this.y2=Number(JSON.stringify(this.sy2, null, 6))
        this.sx2.setOffset(this.sx2._value)
        this.sy2.setOffset(this.sy2._value)
      },
      onPanResponderMove: 
      (event,gestureState)=>{
        if(gestureState.dx+this.x2>this.x1+this.state.corpBoxMinWith
            && gestureState.dx+this.x2<this.state.corpBoxRightBorder
            && gestureState.dx*0.64+this.y2<this.state.corpBoxTopBorder
            ){
          this.sx2.setValue(gestureState.dx)
          this.sy2.setValue(gestureState.dx*0.64)
          this.blue_flag=true
          console.log("blue移动",this.sx1,this.sy1,this.sx2,this.sy2)
        }
        if(gestureState.dx+this.x2<this.x1+this.state.corpBoxMinWith)
        {
          let d=this.x1+this.state.corpBoxMinWith-this.x2
          this.sx2.setValue(d)
          this.sy2.setValue(d*(0.64))
          this.blue_flag=true
        }
        if(gestureState.dx+this.x2>this.state.corpBoxRightBorder && gestureState.dx*0.64+this.y2<this.state.corpBoxTopBorder)
        {
          let d=this.state.corpBoxRightBorder-this.x2
          this.sx2.setValue(d)
          this.sy2.setValue(d*(0.64))
          this.blue_flag=true
        }
        if(gestureState.dx+this.x2<this.state.corpBoxRightBorder && gestureState.dx*0.64+this.y2>this.state.corpBoxTopBorder)
        {
          let d=this.state.corpBoxTopBorder-this.y2
          this.sx2.setValue(d/(0.64))
          this.sy2.setValue(d)
          this.blue_flag=true
        }
      },
      onPanResponderRelease: (event,gestureState) => {
        console.log("松开")
        if(this.blue_flag==false)
        {
          this.sx2.setValue(0)
          this.sy2.setValue(0)            
        }else{
          this.blue_flag=false
        }
        this.sx2.flattenOffset();
        this.sy2.flattenOffset();
        this.x2=Number(JSON.stringify(this.sx2, null, 6))
        this.y2=Number(JSON.stringify(this.sy2, null, 6))
      }
    });

    red = PanResponder.create({
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        console.log("点击")
        this.x2=Number(JSON.stringify(this.sx2, null, 6))
        this.y1=Number(JSON.stringify(this.sy1, null, 6))
        this.sx2.setOffset(this.sx2._value)
        this.sy1.setOffset(this.sy1._value)
      },
      onPanResponderMove: 
        (event,gestureState)=>{
          if(gestureState.dx+this.x2>this.x1+this.state.corpBoxMinWith
            && gestureState.dx+this.x2<this.state.corpBoxRightBorder
            && gestureState.dx*(-0.64)+this.y1>this.state.corpBoxBottomBorder
            ){
            this.sx2.setValue(gestureState.dx)
            this.sy1.setValue(gestureState.dx*(-0.64))
            this.red_flag=true
          }
          if(gestureState.dx+this.x2<this.x1+this.state.corpBoxMinWith)
          {
            let d=this.x1+this.state.corpBoxMinWith-this.x2
            this.sx2.setValue(d)
            this.sy1.setValue(d*(-0.64))
            this.red_flag=true
          }
          if(gestureState.dx+this.x2>this.state.corpBoxRightBorder && gestureState.dx*(-0.64)+this.y1>this.state.corpBoxBottomBorder)
          {
            let d=this.state.corpBoxRightBorder-this.x2
            this.sx2.setValue(d)
            this.sy1.setValue(d*(-0.64))
            this.red_flag=true
          }
          if(gestureState.dx+this.x2<this.state.corpBoxRightBorder && gestureState.dx*(-0.64)+this.y1<this.state.corpBoxBottomBorder)
          {
            let d=this.state.corpBoxBottomBorder-this.y1
            this.sx2.setValue(d/(-0.64))
            this.sy1.setValue(d)
            this.red_flag=true
          }
          console.log("red移动",this.sx1,this.sy1,this.sx2,this.sy2)
        },
      onPanResponderRelease: (event,gestureState) => {
        console.log("松开")
        if(this.red_flag==false)
        {
          this.sx2.setValue(0)
          this.sy1.setValue(0)            
        }else{
          this.red_flag=false
        }
        this.sx2.flattenOffset();
        this.sy1.flattenOffset();
        this.x2=Number(JSON.stringify(this.sx2, null, 6))
        this.y1=Number(JSON.stringify(this.sy1, null, 6))
      }
    });

      white = PanResponder.create({
        onMoveShouldSetPanResponder: () => true,
        onPanResponderGrant: (evt, gestureState) => {
          console.log(JSON.stringify(gestureState, null, 6))
          this.x1=Number(JSON.stringify(this.sx1, null, 6))
          this.y1=Number(JSON.stringify(this.sy1, null, 6))
          this.x2=Number(JSON.stringify(this.sx2, null, 6))
          this.y2=Number(JSON.stringify(this.sy2, null, 6))          
          // console.log("点击",this.x1,this.y1,this.x2,this.y2)

          console.log(this.sx1._value,this.sy1._value,this.sx2._value,this.sy2._value)
          this.sx1.setOffset(this.sx1._value)
          this.sy1.setOffset(this.sy1._value)
          this.sx2.setOffset(this.sx2._value)
          this.sy2.setOffset(this.sy2._value)
          this.sy1.setValue(0)
          this.sy2.setValue(0)
          this.sx1.setValue(0)
          this.sx2.setValue(0)
        },
        onPanResponderStart:(event,gestureState)=>{
        },
        onPanResponderMove: 
          (event,gestureState)=>{
                // console.log("white移动",this.sx1,this.sx2,event.nativeEvent)
                // console.log()
                // console.log(JSON.stringify(gestureState, null, 6))
                if(gestureState.numberActiveTouches ==1){
                  //y轴合规但x轴不合规
                    if(
                    ( gestureState.dy+this.y1>this.state.corpBoxBottomBorder && gestureState.dy+this.y2<this.state.corpBoxTopBorder) && 
                    ( gestureState.dx+this.x1<this.state.corpBoxLeftBorder || gestureState.dx+this.x2>this.state.corpBoxRightBorder)
                    ){
                        this.sy1.setValue(gestureState.dy)
                        this.sy2.setValue(gestureState.dy)
                        if(gestureState.dx+this.x1<this.state.corpBoxLeftBorder)
                        {
                          this.sx1.setValue(this.state.corpBoxLeftBorder-this.x1)
                          this.sx2.setValue(this.state.corpBoxLeftBorder-this.x1)
                        }
                        if(gestureState.dx+this.x2>this.state.corpBoxRightBorder)
                        {
                          this.sx1.setValue(this.state.corpBoxRightBorder-this.x2)
                          this.sx2.setValue(this.state.corpBoxRightBorder-this.x2)
                        }
                        this.white_flag=true
                        console.log("触发")
                    }
                    //y轴不合规但x轴合规
                    if(
                      ( gestureState.dy+this.y1<this.state.corpBoxBottomBorder || gestureState.dy+this.y2>this.state.corpBoxTopBorder )&&
                      ( gestureState.dx+this.x1>this.state.corpBoxLeftBorder && gestureState.dx+this.x2<this.state.corpBoxRightBorder)
                    ){
                        this.sx1.setValue(gestureState.dx)
                        this.sx2.setValue(gestureState.dx)
                        if(gestureState.dy+this.y1<this.state.corpBoxBottomBorder)
                        {
                          this.sy1.setValue(this.state.corpBoxBottomBorder-this.y1)
                          this.sy2.setValue(this.state.corpBoxBottomBorder-this.y1)
                        }
                        if(gestureState.dy+this.y2>this.state.corpBoxTopBorder)
                        {
                          this.sy1.setValue(this.state.corpBoxTopBorder-this.y2)
                          this.sy2.setValue(this.state.corpBoxTopBorder-this.y2)
                        }
                        this.white_flag=true
                        console.log("触发")
                    }
                    //y轴合规且x轴合规
                    if(1
                      && gestureState.dy+this.y1>this.state.corpBoxBottomBorder
                      && gestureState.dy+this.y2<this.state.corpBoxTopBorder
                      && gestureState.dx+this.x1>this.state.corpBoxLeftBorder
                      && gestureState.dx+this.x2<this.state.corpBoxRightBorder
                      ){
                          this.sy1.setValue(gestureState.dy)
                          this.sy2.setValue(gestureState.dy)
                          this.sx1.setValue(gestureState.dx)
                          this.sx2.setValue(gestureState.dx)
                          this.white_flag=true
                          console.log("触发")
                      }   
                     //y轴不合规且x轴不合规  
                    if( 
                      ( gestureState.dy+this.y1<this.state.corpBoxBottomBorder || gestureState.dy+this.y2>this.state.corpBoxTopBorder )&&
                      ( gestureState.dx+this.x1<this.state.corpBoxLeftBorder || gestureState.dx+this.x2>this.state.corpBoxRightBorder)
                    ){
                      if(gestureState.dx+this.x1<this.state.corpBoxLeftBorder)
                      {
                        this.sx1.setValue(this.state.corpBoxLeftBorder-this.x1)
                        this.sx2.setValue(this.state.corpBoxLeftBorder-this.x1)
                      }
                      if(gestureState.dx+this.x2>this.state.corpBoxRightBorder)
                      {
                        this.sx1.setValue(this.state.corpBoxRightBorder-this.x2)
                        this.sx2.setValue(this.state.corpBoxRightBorder-this.x2)
                      }
                      if(gestureState.dy+this.y1<this.state.corpBoxBottomBorder)
                      {
                        this.sy1.setValue(this.state.corpBoxBottomBorder-this.y1)
                        this.sy2.setValue(this.state.corpBoxBottomBorder-this.y1)
                      }
                      if(gestureState.dy+this.y2>this.state.corpBoxTopBorder)
                      {
                        this.sy1.setValue(this.state.corpBoxTopBorder-this.y2)
                        this.sy2.setValue(this.state.corpBoxTopBorder-this.y2)
                      }
                    }
                }
            },
        onPanResponderRelease: (evt, gestureState) => {
          console.log("松开")
          if(this.white_flag==false)
          {
            this.sx1.setValue(0)    
            this.sx2.setValue(0) 
            this.sy1.setValue(0)      
            this.sy2.setValue(0)       
          }else{
            this.white_flag=false
          }
          this.sx1.flattenOffset();
          this.sy1.flattenOffset();
          this.sx2.flattenOffset();
          this.sy2.flattenOffset();
          this.x1=Number(JSON.stringify(this.sx1, null, 6))
          this.y1=Number(JSON.stringify(this.sy1, null, 6))
          this.x2=Number(JSON.stringify(this.sx2, null, 6))
          this.y2=Number(JSON.stringify(this.sy2, null, 6))  
        }
      });

      picture = PanResponder.create({
        onMoveShouldSetPanResponder: () => true,
        onPanResponderGrant: (evt, gestureState) => {
          console.log(JSON.stringify(gestureState, null, 6))
          this.x1=Number(JSON.stringify(this.sx1, null, 6))
          this.y1=Number(JSON.stringify(this.sy1, null, 6))
          this.x2=Number(JSON.stringify(this.sx2, null, 6))
          this.y2=Number(JSON.stringify(this.sy2, null, 6))          
          // console.log("点击",this.x1,this.y1,this.x2,this.y2)

          console.log(this.sx1._value,this.sy1._value,this.sx2._value,this.sy2._value)
          this.sx1.setOffset(this.sx1._value)
          this.sy1.setOffset(this.sy1._value)
          this.sx2.setOffset(this.sx2._value)
          this.sy2.setOffset(this.sy2._value)
          this.sy1.setValue(0)
          this.sy2.setValue(0)
          this.sx1.setValue(0)
          this.sx2.setValue(0)
        },
        onPanResponderStart:(event,gestureState)=>{
          if(gestureState.numberActiveTouches ==2){
            //这里计算初始的距离 √(x1-x2)^2+(y1-y2)^2
            console.log("start")
            let distend = Math.sqrt(
              Math.pow(event.nativeEvent.touches[0].pageX - event.nativeEvent.touches[1].pageX, 2) +
              Math.pow(event.nativeEvent.touches[0].pageY - event.nativeEvent.touches[1].pageY, 2)
            )
            this.initDistend = distend;
          }
        },
        onPanResponderMove: 
          (event,gestureState)=>{
              if(gestureState.numberActiveTouches ==2){
                  let distend = Math.sqrt(
                    Math.pow(event.nativeEvent.touches[0].pageX - event.nativeEvent.touches[1].pageX, 2) +
                    Math.pow(event.nativeEvent.touches[0].pageY - event.nativeEvent.touches[1].pageY, 2)
                  )
                  let scaleNumber = distend - this.initDistend
                  console.log(scaleNumber,gestureState.dx)
                  if( -scaleNumber/3+this.x1<this.x2+scaleNumber/3-this.state.corpBoxMinWith
                    && -scaleNumber*0.64/3+this.y1>this.state.corpBoxBottomBorder
                    && scaleNumber*0.64/3+this.y2<this.state.corpBoxTopBorder
                    && -scaleNumber/3+this.x1>this.state.corpBoxLeftBorder
                    && scaleNumber/3+this.x2<this.state.corpBoxRightBorder
                    ){
                        this.sy1.setValue(-scaleNumber*0.64/3)
                        this.sx1.setValue(-scaleNumber/3)     

                        this.sy2.setValue(scaleNumber*0.64/3)
                        this.sx2.setValue(scaleNumber/3)

                        this.picture_flag=true
                        console.log("触发")
                    }
                }
            },
        onPanResponderRelease: (evt, gestureState) => {
          console.log("松开")
          if(this.picture_flag==false)
          {
            this.sx1.setValue(0)    
            this.sx2.setValue(0) 
            this.sy1.setValue(0)      
            this.sy2.setValue(0)       
          }else{
            this.picture_flag=false
          }
          this.sx1.flattenOffset();
          this.sy1.flattenOffset();
          this.sx2.flattenOffset();
          this.sy2.flattenOffset();
          this.x1=Number(JSON.stringify(this.sx1, null, 6))
          this.y1=Number(JSON.stringify(this.sy1, null, 6))
          this.x2=Number(JSON.stringify(this.sx2, null, 6))
          this.y2=Number(JSON.stringify(this.sy2, null, 6))  
        }
      });

    render(){
        return(

          <View style={{position:"absolute"}}>
              {
                this.state.visible?
              <View style={{
                // position:"absolute",
                height: screenHeight,
                width: screenWidth,
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(0,0,0,0.64)', 
                zIndex:100
                }}>
                  <View style={{
                    backgroundColor:"white",
                    alignItems: 'center',
                    borderRadius:10,
                    width:screenWidth-10,
                    padding:10
                  }}>
                    <View style={{
                      height:50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                      <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑图片</Text>
                    </View>
                    <View style={styles.inputLineViewStyle}/>
                    {/* 裁剪区 */}
                    <View style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding:20,
                      width:this.state.width+40
                    }}
                    >
                      <Animated.View
                            style={{
                                position:"absolute",
                                left:0,top:0,
                                zIndex:999,
                                transform: [{ translateX: this.sx1 }, { translateY: this.sy1 }]
                            }}
                            {...this.yellow.panHandlers}
                        >
                            <View style={{
                              borderColor:"#1E6EFA",
                              borderRightWidth:0,
                              borderTopWidth:5,
                              borderLeftWidth:5,
                              borderBottomWidth:0,
                              height:20,width:20}}>
                            </View>
                        </Animated.View>
                        <Animated.View
                        style={{
                            position:"absolute",
                            left:0,top:0,
                            zIndex:999,
                            transform: [{ translateX: this.sx1 }, { translateY: this.sy2 }]
                        }}
                        {...this.green.panHandlers}
                        >
                        <View style={{
                          borderColor:"#1E6EFA",
                          borderRightWidth:0,
                          borderTopWidth:0,
                          borderLeftWidth:5,
                          borderBottomWidth:5,
                          height:20,width:20}}>
                        </View>
                        </Animated.View>

                        <Animated.View
                        style={{
                            position:"absolute",
                            left:0,top:0,
                            zIndex:999,
                            transform: [{ translateX: this.sx2 }, { translateY: this.sy2 }]
                        }}
                        {...this.blue.panHandlers}
                        >
                        <View style={{
                        borderColor:"#1E6EFA",
                        borderRightWidth:5,
                        borderTopWidth:0,
                        borderLeftWidth:0,
                        borderBottomWidth:5,
                          height:20,width:20}}>
                        </View>
                        </Animated.View>
                        <Animated.View
                        style={{
                            position:"absolute",
                            left:0,top:0,
                            zIndex:999,
                            transform: [{ translateX: this.sx2 }, { translateY: this.sy1 }]
                        }}
                        {...this.red.panHandlers}
                        >
                        <View style={{
                        borderColor:"#1E6EFA",
                        borderRightWidth:5,
                        borderTopWidth:5,
                        borderLeftWidth:0,
                        borderBottomWidth:0,
                        height:20,width:20}}>
                        </View>
                        </Animated.View>

                        <Animated.View
                        style={{
                            position:"absolute",zIndex:30,
                            left:10,top:10,
                            zIndex:999,
                            width:Animated.subtract(Animated.subtract(this.sx2,this.sx1),this.a),
                            height:Animated.subtract(Animated.subtract(this.sy2,this.sy1),this.a),
                            transform: [{ translateX: this.sx1 }, { translateY: this.sy1 },]
                        }}
                        {...this.white.panHandlers}
                        >
                        <View style={{borderColor:"#1E6EFA",borderWidth:2,flex:1,}}>
                        </View>
                        </Animated.View>

                    <ImageBackground 
                    source={{uri:this.state.coursePhoto}} 
                    style={{alignItems: "center",
                    justifyContent: "center",
                    height:this.state.height,
                    width:this.state.width
                    }}
                    {...this.picture.panHandlers}
                    >
                    </ImageBackground>   
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    {/* 保存与取消 */}
                    <View style={{ height: 50 ,flexDirection:"row",justifyContent:"space-between"}}>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                              visible: false
                            });
                        }}>
                            <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                              visible: false
                            });
                            console.log("保存图片",this.x1-10,this.y1-10,this.x2-10,this.y2-10,this.state.coursePhoto)
                            var formData = new FormData();
                            let file = { uri: this.state.coursePhoto, type: 'multipart/form-data',name: this.state.name};
                            formData.append('file', file);
                            formData.append('oldFile', this.state.oldFile);
                            formData.append('imageType', "corp_image");
                            formData.append('sx1', parseInt((this.x1-10)/this.state.width*this.state.photoWidth));
                            formData.append('sy1', parseInt((this.y1-10)/this.state.height*this.state.photoHeight));
                            formData.append('sx2', parseInt((this.x2-10)/this.state.width*this.state.photoWidth));
                            formData.append('sy2', parseInt((this.y2-10)/this.state.height*this.state.photoHeight));
                            console.log(this.state.width,this.state.height,this.state.photoWidth,this.state.photoHeight)
                            console.log("比例：",(parseInt((this.x2-10)/this.state.width*this.state.photoWidth)-parseInt((this.x1-10)/this.state.width*this.state.photoWidth))/(parseInt((this.y2-10)/this.state.height*this.state.photoHeight)-parseInt((this.y1-10)/this.state.height*this.state.photoHeight)))
                            console.log("比例：（无小数）",(((this.x2-10)/this.state.width*this.state.photoWidth)-((this.x1-10)/this.state.width*this.state.photoWidth))/(((this.y2-10)/this.state.height*this.state.photoHeight)-((this.y1-10)/this.state.height*this.state.photoHeight)))
                            console.log(
                              parseInt((this.x1-10)/this.state.width*this.state.photoWidth),
                              parseInt((this.y1-10)/this.state.height*this.state.photoHeight),
                              parseInt((this.x2-10)/this.state.width*this.state.photoWidth),
                              parseInt((this.y2-10)/this.state.height*this.state.photoHeight)
                            )
                            let params = {
                              formData,
                            }
                          //单个图片上传接口
                          singleImgUpload(params, (res)=>{
                            // console.log(res)
                            this.props.callBack(res)
                          });
                        }}>
                            <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>               
                  </View>     
              </View> 
              :
              <View></View>         
              }
          </View>
        )
    }
}

    // 图片上传
    const singleImgUpload = async (params, callback) => {
      let { formData } = params
      return await uploadRequest('/biz/upload/image/single_image_upLoad_crop', formData, callback)
    }

    const uploadRequest = async (url, datas, callback) => {
      const params = {
          method: 'POST',
          body: datas,
          headers: {
              'Content-Type': 'multipart/form-data'
          },
          timeout: 10000 // 10s超时
      };
  
      try {
          const response = await fetch(`${constants.service_addr}${url}`, params);
          const data = await response.json()
          callback(data);
      }
      catch (error) {
          let toastOpts;
          toastOpts = getFailToastOpts(error);
          WToast.show(toastOpts)
  
      }
  
  }

let styles = StyleSheet.create({
  inputLineViewStyle: {
      height:5,
      marginLeft: 13,
      marginRight: 13,
      borderBottomWidth: 0.5,
      borderColor:'#E8E9EC'
  },
})
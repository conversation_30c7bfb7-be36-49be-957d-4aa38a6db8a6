import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class RoleMenuAdd extends Component {
    constructor() {
        super()
        this.state = {
            roleId: "",
            roleName: "",
            menuParentId: "",
            dataSource: [],
            selMenuIdList: [],
            oldselMenuIdList: [],
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { roleId, roleName, menuParentId } = route.params;
            if (roleId) {
                console.log('componentWillMount==roleId:', roleId);
                this.setState({
                    roleId: roleId
                })
            }
            if (roleName) {
                this.setState({
                    roleName: roleName
                })
            }
            if (menuParentId != null) {
                console.log('componentWillMount==menuParentId:', menuParentId);
                this.setState({
                    menuParentId: menuParentId
                })
                this.loadMenuList(roleId, menuParentId)
            }
        }
    }

    copyArr = (varArray) => {
        let res = []
        for (let i = 0; i < varArray.length; i++) {
            res.push(varArray[i])
        }
        return res
    }


    loadMenuList = (roleId, menuParentId) => {

        console.log('===loaMenuList=22=menuParentId:', menuParentId);
        let loadTypeUrl;
        let loadRequest;
        loadTypeUrl = "/biz/menu/menu_list_by_top_2_menu_id";
        loadRequest = { 'menuParentId': menuParentId, "roleId": roleId, "menuClass": "C" };
        if (menuParentId == 0) {
            loadTypeUrl = "/biz/menu/list";
            loadRequest = { 'isRecursive': false, "roleId": roleId, "menuClass": "C" };
        }
        httpPost(loadTypeUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                let menuDTOList = response.data;
                this.setState({
                    dataSource: menuDTOList
                })
                var menuDTO;

                var selMenuIdList = [];
                for (var index = 0; index < menuDTOList.length; index++) {
                    menuDTO = menuDTOList[index];
                    console.log("=========init=menuDTO:", menuDTO);
                    if (menuDTO && menuDTO.selected === "Y") {
                        selMenuIdList = selMenuIdList.concat(menuDTO.menuId).concat(menuDTO.menuParentId);
                    }
                }
                this.setState({
                    selMenuIdList: selMenuIdList,
                    oldselMenuIdList: this.copyArr(selMenuIdList),
                })
                console.log("=========init=selMenuIdList:", selMenuIdList);
            }
        });
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={this.saveRoleMenu.bind(this)}>
                <Image style={{ width: 28, height: 28, marginRight: 5 }} source={require('../../assets/icon/iconfont/ok1.png')}></Image>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    saveRoleMenu = () => {
        console.log("=======saveRole");
        let toastOpts;
        if (!this.state.selMenuIdList) {
            toastOpts = getFailToastOpts("请选择菜单");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/role/role_add_menu";
        let requestParams = {
            "roleId": this.state.roleId,
            "menuIdList": this.state.selMenuIdList,
            "oldMenuIdList": this.state.oldselMenuIdList,
        };
        httpPost(url, requestParams, this.saveRoleCallBack);
    }

    // 保存回调函数
    saveRoleCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='菜单管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={[{ flexDirection: 'row', flexWrap: 'wrap', width: screenWidth * 0.95, justifyContent: 'flex-start' }]}>
                        {this.state.dataSource.map((item, key) => {
                            return (
                                <TouchableOpacity onPress={() => {
                                    var selMenuIdList = this.state.selMenuIdList;
                                    if (item.selected && item.selected == "Y") {
                                        item.selected = "N";
                                        arrayRemoveItem(selMenuIdList, item.menuId);
                                        arrayRemoveItem(selMenuIdList, item.menuParentId);
                                    }
                                    else {
                                        item.selected = "Y";
                                        selMenuIdList = selMenuIdList.concat(item.menuId).concat(item.menuParentId)
                                    }
                                    this.setState({
                                        selMenuIdList: selMenuIdList,
                                    })
                                    WToast.show({ data: '点击了' + item.menuName });
                                    console.log("======selMenuIdList:", selMenuIdList)
                                    console.log("======oldselMenuIdList:", this.state.oldselMenuIdList)
                                }}>
                                    <View style={[{ margin: 10, borderRadius: 4, padding: 10, height: 40, backgroundColor: '#F5F5F5' }, (item.selected && item.selected === 'Y') ? { backgroundColor: 'red' } : ""]}>
                                        <Text style={[styles.titleTextStyle, (item.selected && item.selected === 'Y') ? { color: '#FFFFFF' } : { color: '#000000' }]}>{item.menuName}</Text>
                                    </View>
                                </TouchableOpacity>

                            )
                        })}
                    </View>

                    {/* <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveRoleMenu.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>提交</Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
})
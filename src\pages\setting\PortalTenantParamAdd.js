import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Image, ScrollView, TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadImageLibrary } from '../../utils/UploadImageUtils';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
export default class PortalTenantParamAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate: "",
            paramCode: "",
            paramCodeName: "",
            selectedParamCode: [],
            paramCodeDataSource: [],
            paramValue: "",
            valueTypeList: [
                {
                    typeId: 0,
                    typeCode: 'text',
                    typeName: "文本"
                },
                {
                    typeId: 1,
                    typeCode: 'image',
                    typeName: "图片"
                },
                {
                    typeId: 2,
                    typeCode: 'menucheck',
                    typeName: "菜单勾选"
                },
            ],
            valueType: "text",
            imageConfigList: ["PAY_QR_CODE", "ALI_PAY_QR_CODE", "WECHAT_PAY_QR_CODE", "APPLY_CHARGE_PERSON_WECHAT_QR_CODE", "WECHAT_SHARE_PAGE_BOTTOM_IMAGE"
                , "RESUME_SHARE_LOGO", "RESUME_SHARE_WEBSITE_BANNER", "ENTERPRISE_SHARE_LOGO", "ENTERPRISE_SHARE_WEBSITE_BANNER","MEMBER_SHARE_WEBSITE_BANNER","MEMBER_SHARE_WEBSITE_NOTIFICATION_PICTURE","MEMBER_SHARE_CARD_LOGO"],
            bannerConfigList:["RESUME_SHARE_WEBSITE_BANNER", "ENTERPRISE_SHARE_WEBSITE_BANNER", "MEMBER_SHARE_WEBSITE_BANNER"]

        }
    }


    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let url = "/biz/tenant/config/paramCodeList";
        let request = {
            "currentPage": 1,
            "pageSize": 1000
        };
        httpPost(url, request, this.loadParamCodeListCallBack);
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { paramCode } = route.params;
            if (paramCode) {
                console.log("=============paramCode" + paramCode + "");
                this.setState({
                    paramCode: paramCode,
                    operate: "编辑"
                })
                let url = "/biz/tenant/config/get";
                let request = {
                    "paramCode": paramCode,
                };
                httpPost(url, request, this.loadPortalTenantParamCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
        }
    }

    loadPortalTenantParamCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                paramCodeName: response.data.paramCodeName,
                paramCode: response.data.paramCode,
                selectedParamCode: [response.data.paramCodeName],
                paramValue: response.data.paramValue,
                valueType: response.data.valueType
            })
        }
    }



    loadParamCodeListCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                paramCodeDataSource: response.data
            })
        }
    }

    openParamCode() {

        if (!this.state.paramCodeDataSource || this.state.paramCodeDataSource.length < 1) {
            WToast.show({ data: "暂无数据" });
            return
        }

        this.refs.SelectParamCode.showParamCode(this.state.selectedParamCode, this.state.paramCodeDataSource)
    }

    callBackSelectParamCodeValue(value) {
        console.log("==========系统配置选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedParamCode: value
        })
        var paramCodeName = value.toString();
        this.setState({
            paramCodeName: paramCodeName
        })
        let url = "/biz/tenant/config/paramCodeByName";
        let loadRequest = { "paramCodeName": paramCodeName };
        httpPost(url, loadRequest, this.loadParamCodeCallBack);
    }

    loadParamCodeCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var paramCode = response.data.paramCode;
            this.setState({
                paramCode: paramCode
            })
            if (this.state.imageConfigList.includes(paramCode)) {
                this.setState({
                    valueType: "image"
                })
            }
            else {
                this.setState({
                    valueType: "text"
                })
            }
            // if (paramCode == "RESUME_SHARE_LOGO" || paramCode == "ENTERPRISE_SHARE_LOGO") {
            //     console.log("推荐尺寸200x200px")
            //     toastOpts = getSuccessToastOpts("推荐尺寸200x200px");
            //     WToast.show(toastOpts)
            // }
            // if (paramCode == "RESUME_SHARE_WEBSITE_BANNER" || paramCode == "ENTERPRISE_SHARE_WEBSITE_BANNER") {
            //     console.log("推荐尺寸宽750px，高148px")
            //     toastOpts = getSuccessToastOpts("推荐尺寸宽750px，高148px");
            //     WToast.show(toastOpts)
            // }
        }
    }

    savePortalTenantParam = () => {
        console.log("=======savePortalTenantParam");
        let toastOpts;

        if (!this.state.paramCode) {
            toastOpts = getFailToastOpts("请选择配置类型");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.paramValue) {
            toastOpts = getFailToastOpts("请输入配置的值");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/tenant/config/add";
        if (this.state.operate === '编辑') {
            url = "/biz/tenant/config/modify";
        }
        let requestParams = {
            "paramCode": this.state.paramCode,
            "paramValue": this.state.paramValue,
            "valueType": this.state.valueType
        };
        console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.savePortalTenantParamCallBack);
    }

    savePortalTenantParamCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('更新完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PortalTenantParam",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Text style={CommonStyle.headRightText}>系统配置</Text>
            </TouchableOpacity>
        )
    }

    // 配置类型单项渲染
    renderValueTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    valueType: item.typeCode
                })
            }}>
                <View key={item.typeId} style={item.typeCode === this.state.valueType ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.typeCode === this.state.valueType ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.typeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '配置'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>配置名称</Text>
                        </View>
                        <TouchableOpacity onPress={() => {
                            if (this.state.operate == '编辑') {
                                toastOpts = getFailToastOpts("不可编辑配置名称");
                                WToast.show(toastOpts)
                                return;
                            }
                            this.openParamCode()
                        }}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5) }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.paramCodeName ? "请选择配置名称" : this.state.paramCodeName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>配置类型</Text>
                        </View>
                    </View>
                    <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.valueTypeList && this.state.valueTypeList.length > 0)
                                ?
                                this.state.valueTypeList.map((item, index) => {
                                    return this.renderValueTypeRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>

                    {
                        this.state.valueType === 'text' ?
                            <View>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>文本内容</Text>
                                </View>
                                <View style={[styles.inputRowStyle, { height: 200 }]}>
                                    <TextInput
                                        multiline={true}
                                        textAlignVertical="top"
                                        style={[CommonStyle.inputRowText, { height: 200 }]}
                                        placeholder={'请输入配置的值'}
                                        onChangeText={(text) => this.setState({ paramValue: text })}
                                    >
                                        {this.state.paramValue}
                                    </TextInput>
                                </View>
                            </View>

                            :
                            <View />
                    }

                    {
                        this.state.valueType === 'image' ?
                            <View>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>配置图片</Text>
                                    <Text style={[styles.leftLabNameTextStyle,{marginLeft:10,width:150,fontSize:14,color:"red"}]} >
                                    {
                                    (this.state.paramCode == "RESUME_SHARE_LOGO" || this.state.paramCode == "ENTERPRISE_SHARE_LOGO")  
                                    ?  "(推荐尺寸200x200px)" 
                                    :
                                    (
                                        [(this.state.bannerConfigList.includes(this.state.paramCode)) ? "(推荐尺寸750x160px)" : ""]
                                    )
                                    
                                    } 
                                    </Text>
                                </View>
                                {
                                    this.state.bannerConfigList.includes(this.state.paramCode) ? 
                                    <View style={[{
                                        width: screenWidth -20, height: 80, marginLeft: 10, marginBottom: 10, display: 'flex', justifyContent: 'center',
                                        alignItems: 'center'
                                    }, { borderColor: '#AAAAAA', borderWidth: 1, borderStyle: 'dashed', borderRadius: 5 }]}>
                                        
                                            <TouchableOpacity
                                            style={{ width: screenWidth -20, height: 80, alignItems: 'center', justifyContent: 'center' }}
                                            onPress={() => {
                                                console.log("1++" + this.state.paramCode);
                                                console.log("2++" + this.state.bannerConfigList.includes(this.state.paramCode));
                                                uploadImageLibrary(this.state.paramValueImageUrl,  "banner_image", (imageUploadResponse) => {
                                                    console.log("========imageUploadResponse", imageUploadResponse)
                                                    if (imageUploadResponse.code === 200) {
    
                                                        WToast.show({ data: "成功上传" });
                                                        let { compressFile } = imageUploadResponse.data
                                                        this.setState({
                                                            paramValue: compressFile,
                                                        })
                                                    }
                                                    else {
                                                        WToast.show({ data: imageUploadResponse.message });
                                                    }
                                                },  this.state.bannerConfigList.includes(this.state.paramCode));
                                            }}
                                        >
                                            {
                                                this.state.paramValue ?
                                                   
                                                    <Image source={{ uri: (constants.image_addr + '/' + this.state.paramValue) }} style={{ width: screenWidth -20, height: 80, resizeMode: 'contain', justifyContent: 'center', alignItems: 'center' }} />
                                                    :
                                                    <Image source={require('../../assets/icon/iconfont/addPhoto.png')} style={{ width: 24, height: 24 }}></Image>
                                            }
                                        </TouchableOpacity>
    
                                    </View>
                                    :
                                    <View style={[{
                                        width: 120, height: 150, marginLeft: 10, marginBottom: 10, display: 'flex', justifyContent: 'center',
                                        alignItems: 'center'
                                    }, { borderColor: '#AAAAAA', borderWidth: 1, borderStyle: 'dashed', borderRadius: 5 }]}>
                                        
                                            <TouchableOpacity
                                            style={{ width: 120, height: 150, alignItems: 'center', justifyContent: 'center' }}
                                            onPress={() => {
                                                console.log("1++" + this.state.paramCode);
                                                console.log("2++" + this.state.bannerConfigList.includes(this.state.paramCode));
                                                uploadImageLibrary(this.state.paramValueImageUrl,  "banner_image", (imageUploadResponse) => {
                                                    console.log("========imageUploadResponse", imageUploadResponse)
                                                    if (imageUploadResponse.code === 200) {
    
                                                        WToast.show({ data: "成功上传" });
                                                        let { compressFile } = imageUploadResponse.data
                                                        this.setState({
                                                            paramValue: compressFile,
                                                        })
                                                    }
                                                    else {
                                                        WToast.show({ data: imageUploadResponse.message });
                                                    }
                                                },  this.state.bannerConfigList.includes(this.state.paramCode));
                                            }}
                                        >
                                            {
                                                this.state.paramValue ?
                                                   
                                                    <Image source={{ uri: (constants.image_addr + '/' + this.state.paramValue) }} style={{ width: 120, height: 150, justifyContent: 'center', alignItems: 'center' }} />
                                                    :
                                                    <Image source={require('../../assets/icon/iconfont/addPhoto.png')} style={{ width: 24, height: 24 }}></Image>
                                            }
                                        </TouchableOpacity>
    
                                    </View>
                                }

                            </View>
                            :
                            <View />
                    }




                    <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0, marginTop: 6}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: (screenWidth - 56)/2, marginLeft: 20 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.savePortalTenantParam.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView, { width: (screenWidth - 56)/2, marginRight: 20 }]}>
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                <BottomScrollSelect
                    ref={'SelectParamCode'}
                    callBackParamCodeValue={this.callBackSelectParamCodeValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight: 30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }


});
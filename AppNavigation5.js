import React from 'react';
import {
  SafeAreaView,
  StyleSheet,
  ScrollView,
  View,
  Text,
  StatusBar,
  Button,
  Image,
  Linking,
  Dimensions,
} from 'react-native';

import {
  Header,
  LearnMoreLinks,
  Colors,
  DebugInstructions,
  ReloadInstructions,
} from 'react-native/Libraries/NewAppScreen';
import {
  NavigationContainer
} from '@react-navigation/native'
import {
  createStackNavigator
} from '@react-navigation/stack'
// import { from } from 'rxjs';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import {
  createDrawerNavigator,
  DrawerContentScrollView,
  DrawerItemList,
  DrawerItem
} from '@react-navigation/drawer'
import { WToast } from 'react-native-smart-tip';

import Home from './src/pages/home/<USER>'
import WelcomePage from './src/pages/home/<USER>'

import OrgMgrAdd from './src/pages/admin/OrgMgrAdd';
import OrgMgrList from './src/pages/admin/OrgMgrList';
import TenantList from './src/pages/admin/TenantList';
import TenantAdd from './src/pages/admin/TenantAdd';

import OutsourcingTenantList from './src/pages/admin/OutsourcingTenantList';
import OutsourcingTenantAdd from './src/pages/admin/OutsourcingTenantAdd';
import ProductionLineMgrList from './src/pages/admin/ProductionLineMgrList';
import ProductionLineMgrAdd from './src/pages/admin/ProductionLineMgrAdd';

import SettingHome from './src/pages/setting/SettingHome'
import ResetPwd from './src/pages/setting/ResetPwd';
import Profile from './src/pages/setting/Profile';
import BelongsProductionLineSetting from './src/pages/setting/BelongsProductionLineSetting';
import MessageRemind from './src/pages/setting/MessageRemind';
import HelpCenterList from './src/pages/setting/HelpCenterList';

import SuggestionFeedbackList from './src/pages/setting/SuggestionFeedbackList';
import SuggestionFeedbackAdd from './src/pages/setting/SuggestionFeedbackAdd';

import PaymentObjectList from './src/pages/payment_mgr/PaymentObjectList';
import PaymentObjectAdd from './src/pages/payment_mgr/PaymentObjectAdd';
import PaymentClassList from './src/pages/payment_mgr/PaymentClassList';
import PaymentApplyList from './src/pages/payment_mgr/PaymentApplyList';
import PaymentAuditList from './src/pages/payment_mgr/PaymentAuditList';
import PaymentApplyAdd from './src/pages/payment_mgr/PaymentApplyAdd';
import PaymentClassAdd from './src/pages/payment_mgr/PaymentClassAdd';
import PaymengApplyDetail from './src/pages/payment_mgr/PaymengApplyDetail';
import PaymentAudit from './src/pages/payment_mgr/PaymentAudit';

import VerifyInternalSettingList from './src/pages/verify/VerifyInternalSettingList';
import VerifyInternalSettingAdd from './src/pages/verify/VerifyInternalSettingAdd';
import VerifyInternalStandardList from './src/pages/verify/VerifyInternalStandardList';
import VerifyInternalStandardAdd from './src/pages/verify/VerifyInternalStandardAdd';
import VerifyInternalResultList from './src/pages/verify/VerifyInternalResultList';
import VerifyInternalResultAdd from './src/pages/verify/VerifyInternalResultAdd';
import VerifyExternalSettingList from './src/pages/verify/VerifyExternalSettingList';
import VerifyExternalSettingAdd from './src/pages/verify/VerifyExternalSettingAdd';
import VerifyExternalStandardList from './src/pages/verify/VerifyExternalStandardList';
import VerifyExternalStandardAdd from './src/pages/verify/VerifyExternalStandardAdd';
import VerifyExternalResultList from './src/pages/verify/VerifyExternalResultList';
import VerifyExternalResultAdd from './src/pages/verify/VerifyExternalResultAdd';


import RoleList from './src/pages/admin/RoleList';
import RoleAdd from './src/pages/admin/RoleAdd';
import RoleMenuList from './src/pages/admin/RoleMenuList';
import RoleMenuAdd from './src/pages/admin/RoleMenuAdd';
import QuickMenuList from './src/pages/admin/QuickMenuList';
import QuickMenuAdd from './src/pages/admin/QuickMenuAdd';
import RoleUserList from './src/pages/admin/RoleUserList';
import RoleUserAdd from './src/pages/admin/RoleUserAdd';
import UngradedCauseMgrList from './src/pages/admin/UngradedCauseMgrList';
import UngradedCauseMgrAdd from './src/pages/admin/UngradedCauseMgrAdd';
import OrderPositionMgrList from './src/pages/setting/OrderPositionMgrList';
import OrderPositionMgrAdd from './src/pages/setting/OrderPositionMgrAdd';
import WorkingShiftMgrList from './src/pages/setting/WorkingShiftMgrList';
import WorkingShiftMgrAdd from './src/pages/setting/WorkingShiftMgrAdd';
import WorkingShiftRelStaffMgr from './src/pages/setting/WorkingShiftRelStaffMgr';
import PortalStaffMgrAdd from './src/pages/setting/PortalStaffMgrAdd';
import PortalStaffMgrList from './src/pages/setting/PortalStaffMgrList';
import TenantEnterpriseList from './src/pages/setting/TenantEnterpriseList';
import TenantEnterpriseAdd from './src/pages/setting/TenantEnterpriseAdd';
import PortalTenantParam from './src/pages/setting/PortalTenantParam';
import PortalTenantParamAdd from './src/pages/setting/PortalTenantParamAdd';
import PortalTenantParamItem from './src/pages/setting/PortalTenantParamItem';
import PortalTenantParamData from './src/pages/setting/PortalTenantParamData';
import PortalTenantParamDataAdd from './src/pages/setting/PortalTenantParamDataAdd';
import HomeResourceDisplay from './src/pages/setting/HomeResourceDisplay';
import MemberContactConfig from './src/pages/setting/MemberContactConfig';
import MemberContactConfigAdd from './src/pages/setting/MemberContactConfigAdd';
import ConfigPreview from './src/pages/setting/ConfigPreview';
import DepartmentList from './src/pages/admin/DepartmentList';
import DepartmentAdd from './src/pages/admin/DepartmentAdd';
import JobMgrList from './src/pages/admin/JobMgrList';
import JobMgrAdd from './src/pages/admin/JobMgrAdd';
import DepartmentStaffMgrList from './src/pages/admin/DepartmentStaffMgrList';
import JobStaffMgrAdd from './src/pages/admin/JobStaffMgrAdd';
import JobStaffMgrList from './src/pages/admin/JobStaffMgrList';
import EquipmentMgrAdd from './src/pages/admin/EquipmentMgrAdd';
import EquipmentMgrList from './src/pages/admin/EquipmentMgrList';
import EquipmentStateMgrAdd from './src/pages/admin/EquipmentStateMgrAdd';
import EquipmentStateMgrList from './src/pages/admin/EquipmentStateMgrList';
import CheckDepartmentList from './src/pages/admin/CheckDepartmentList';
import CheckEquipmentList from './src/pages/admin/CheckEquipmentList';
import CheckEquipmentStateList from './src/pages/admin/CheckEquipmentStateList';
import InformationConfigAdd from './src/pages/admin/InformationConfigAdd';
import InformationConfigList from './src/pages/admin/InformationConfigList';

import DailyList from './src/pages/digital_employees/DailyList';
import DailyAdd from './src/pages/digital_employees/DailyAdd';
import DailyMessageList from './src/pages/digital_employees/DailyMessageList';
import DailyMessageAdd from './src/pages/digital_employees/DailyMessageAdd';
import DailyDetail from './src/pages/digital_employees/DailyDetail';
import HarvestMgrList from './src/pages/digital_employees/HarvestMgrList';
import HarvestMgrAdd from './src/pages/digital_employees/HarvestMgrAdd';
import HarvestDetail from './src/pages/digital_employees/HarvestDetail';
import CourseMgrAdd from './src/pages/digital_employees/CourseMgrAdd';
import CourseMgrList from './src/pages/digital_employees/CourseMgrList';
import GoodHarvestList from './src/pages/digital_employees/GoodHarvestList';
import HarvestCircleList from './src/pages/digital_employees/HarvestCircleList';
import HarvestGoodMgrList from './src/pages/digital_employees/HarvestGoodMgrList';
import MyBacklogDailyList from './src/pages/digital_employees/MyBacklogDailyList ';
import MyDoneDailyList from './src/pages/digital_employees/MyDoneDailyList';
import MyBacklogDailyAdd from './src/pages/digital_employees/MyBacklogDailyAdd';
import QueryDaily from './src/pages/digital_employees/QueryDaily';
import WorkDaily from './src/pages/digital_employees/WorkDaily';
import QueryHarvest from './src/pages/digital_employees/QueryHarvest';
import QueryMyScore from './src/pages/digital_employees/QueryMyScore';
import QueryPromotionPlan from './src/pages/digital_employees/QueryPromotionPlan';
import PortalTrackingList from './src/pages/digital_employees/PortalTrackingList';
import PortalTrackingAdd from './src/pages/digital_employees/PortalTrackingAdd';
import MyDocumentMgrList from './src/pages/digital_employees/MyDocumentMgrList';
import DocumentLibraryMgrList from './src/pages/digital_employees/DocumentLibraryMgrList';
import PointConfig from './src/pages/digital_employees/PointConfig';
import PointConfigAdd from './src/pages/digital_employees/PointConfigAdd';
import PointRecord from './src/pages/digital_employees/PointRecord';
import PointReward from './src/pages/digital_employees/PointReward';
import PointRewardAdd from './src/pages/digital_employees/PointRewardAdd';
import PointExchange from './src/pages/digital_employees/PointExchange';
import PointExchangeAdd from './src/pages/digital_employees/PointExchangeAdd';
import PointRanking from './src/pages/digital_employees/PointRanking';
import ExamConfig from './src/pages/digital_employees/ExamConfig';
import ExamConfigAdd from './src/pages/digital_employees/ExamConfigAdd';
import FormCollection from './src/pages/digital_employees/FormCollection';
import ExamApply from './src/pages/digital_employees/ExamApply';
import ExamApplyAdd from './src/pages/digital_employees/ExamApplyAdd';
import CourseScheduling from './src/pages/digital_employees/CourseScheduling';
import CourseSchedulingAdd from './src/pages/digital_employees/CourseSchedulingAdd';
import CourseSchedulingTable from './src/pages/digital_employees/CourseSchedulingTable';
import ExamApplyAudit from './src/pages/digital_employees/ExamApplyAudit';
import SYPointMall from './src/pages/digital_employees/SYPointMall';
import SYContactUs from './src/pages/digital_employees/SYContactUs';
import DigitalEmployeesHome from './src/pages/digital_employees/DigitalEmployeesHome';

import PromotionPlanAdd from './src/pages/digital_employees/PromotionPlanAdd';
import PromotionPlanList from './src/pages/digital_employees/PromotionPlanList';
import PromotionPlanDetail from './src/pages/digital_employees/PromotionPlanDetail';
import PromotionPlanSelUser from './src/pages/digital_employees/PromotionPlanSelUser';
import ScoreMgrAdd from './src/pages/digital_employees/ScoreMgrAdd';
import ScoreMgrList from './src/pages/digital_employees/ScoreMgrList';
import HarvestDiscussList from './src/pages/digital_employees/HarvestDiscussList';
import HarvestDiscussAdd from './src/pages/digital_employees/HarvestDiscussAdd';
import ScoreMgrCourseList from './src/pages/digital_employees/ScoreMgrCourseList';
import VideoLibraryMgrList from './src/pages/digital_employees/VideoLibraryMgrList';
import VideoLibraryView from './src/pages/digital_employees/VideoLibraryView';
import DocumentLibraryView from './src/pages/digital_employees/DocumentLibraryView';

import CollegClassGradesAdd from './src/pages/college_recruiting/CollegClassGradesAdd';
import CollegClassGradesList from './src/pages/college_recruiting/CollegClassGradesList';
import CollegClassStudentList from './src/pages/college_recruiting/CollegClassStudentList';
import CollegProfessionalList from './src/pages/college_recruiting/CollegProfessionalList';
import CollegProfessionalAdd from './src/pages/college_recruiting/CollegProfessionalAdd';
import CollegStudentAdd from './src/pages/college_recruiting/CollegStudentAdd';
import CollegStudentList from './src/pages/college_recruiting/CollegStudentList';
import CollegStudentResumeQuery from './src/pages/college_recruiting/CollegStudentResumeQuery';
import EnterpriseInvitedInterView from './src/pages/college_recruiting/EnterpriseInvitedInterView';
import EnterpriseResumeCollection from './src/pages/college_recruiting/EnterpriseResumeCollection';
import PortalEnterpriseAdd from './src/pages/college_recruiting/PortalEnterpriseAdd';
import PortalEnterpriseList from './src/pages/college_recruiting/PortalEnterpriseList';
import StudentInterViewInvited from './src/pages/college_recruiting/StudentInterViewInvited';
import StudentMyChance from './src/pages/college_recruiting/StudentMyChance';
import StudentMyInterView from './src/pages/college_recruiting/StudentMyInterView';
import StudentMyInterViewPreview from './src/pages/college_recruiting/StudentMyInterViewPreview';
import EnterpriseRecruiterList from './src/pages/college_recruiting/EnterpriseRecruiterList';
import EnterpriseRecruiterAdd from './src/pages/college_recruiting/EnterpriseRecruiterAdd';
import EnterprisecrHiringPositionAdd from './src/pages/college_recruiting/EnterprisecrHiringPositionAdd';
import EnterprisecrHiringPositionList from './src/pages/college_recruiting/EnterprisecrHiringPositionList';
import CollegPositionQuery from './src/pages/college_recruiting/CollegPositionQuery';
import EnterpriseInvitedApply from './src/pages/college_recruiting/EnterpriseInvitedApply';
import EnterprisecrHiringPositionDetail from './src/pages/college_recruiting/EnterprisecrHiringPositionDetail';
import PersonalInformation from './src/pages/college_recruiting/PersonalInformation'
import PersonalHonor from './src/pages/college_recruiting/PersonalHonor'
import CollegeEvaluation from './src/pages/college_recruiting/CollegeEvaluation'
import NewItem from './src/pages/college_recruiting/NewItem'
import EnterprisePositionDetail from './src/pages/college_recruiting/EnterprisePositionDetail';
import Feedback from './src/pages/college_recruiting/Feedback';
import ChangePassword from './src/pages/college_recruiting/ChangePassword';
import MemberManagementAdd from './src/pages/college_recruiting/MemberManagementAdd';
import MemberManagementList from './src/pages/college_recruiting/MemberManagementList';
import MemberManagementDetail from './src/pages/college_recruiting/MemberManagementDetail';
import MemberManagementExamine from './src/pages/college_recruiting/MemberManagementExamine';
import MemberManagementExamineDetail from './src/pages/college_recruiting/MemberManagementExamineDetail';
import MemberTypeAdd from './src/pages/college_recruiting/MemberTypeAdd';
import MemberTypeList from './src/pages/college_recruiting/MemberTypeList';
import ConfiguringTenants from './src/pages/college_recruiting/ConfiguringTenants';
import ConfiguringTenantsParam from './src/pages/college_recruiting/ConfiguringTenantsParam';
import ConfiguringTenantsParamItem from './src/pages/college_recruiting/ConfiguringTenantsParamItem';

import LoginView from './src/pages/LoginView';
import Register from './src/pages/Register';
import ModifyPwd from './src/pages/ModifyPwd';
import Privacy from './src/pages/Privacy';
import UserAgreement from './src/pages/UserAgreement';

import Myself from './src/pages/setting/Myself';

import HomeIconNormal from './src/assets/icon/bottom_navigate/workbench1.png';
import HomeIconActive from './src/assets/icon/bottom_navigate/workbench_lighted1.png';

import './src/utils/Global'

import ReactNativeDemo from './src/pages/demo/ReactNativeDemo'
import { ifIphoneX, ifIphoneXTabBarOptionHeight } from './src/utils/ScreenUtil';
import PointRecordList from './src/pages/digital_employees/PointRecordList';
import AssessClassList from './src/pages/digital_employees/AssessClassList';
import AssessClassAdd from './src/pages/digital_employees/AssessClassAdd';
import AssessApplyAdd from './src/pages/digital_employees/AssessApplyAdd';
import AssessApplyList from './src/pages/digital_employees/AssessApplyList';
import AssessAuditList from './src/pages/digital_employees/AssessAuditList';
import AssessQueryList from './src/pages/digital_employees/AssessQueryList';
import AssessAudit from './src/pages/digital_employees/AssessAudit';
import PermissionList from './src/pages/setting/PermissionList';
import PermissionUserList from './src/pages/setting/PermissionUserList';
import PermissionUserAddList from './src/pages/setting/PermissionUserAddList';
import MyReceiveAdd from './src/pages/digital_employees/ask_questions/MyReceiveAdd';
import MyReceiveList from './src/pages/digital_employees/ask_questions/MyReceiveList';
import AskQuestionsQuery from './src/pages/digital_employees/ask_questions/AskQuestionsQuery';
import MyAskQuestionsAdd from './src/pages/digital_employees/ask_questions/MyAskQuestionsAdd';
import MyAskQuestionsList from './src/pages/digital_employees/ask_questions/MyAskQuestionsList';
import AskQuestionsSolveTrackingAdd from './src/pages/digital_employees/ask_questions/AskQuestionsSolveTrackingAdd';
import AskQuestionsSolveTrackingList from './src/pages/digital_employees/ask_questions/AskQuestionsSolveTrackingList';
import CourseTypeMgrList from './src/pages/digital_employees/course_mgr/CourseTypeMgrList';
import CourseTypeMgrAdd from './src/pages/digital_employees/course_mgr/CourseTypeMgrAdd';
import CourseLevelMgrAdd from './src/pages/digital_employees/course_mgr/CourseLevelMgrAdd';
import CourseLevelMgrList from './src/pages/digital_employees/course_mgr/CourseLevelMgrList';
import MyCourseList from './src/pages/digital_employees/course_mgr/MyCourseList';
import CourseTaskMgrList from './src/pages/digital_employees/course_mgr/CourseTaskMgrList';
import CourseTaskMgrDetail from './src/pages/digital_employees/course_mgr/CourseTaskMgrDetail';
import CourseTrackDetail from './src/pages/digital_employees/course_mgr/CourseTrackDetail';
import CourseTrackList from './src/pages/digital_employees/course_mgr/CourseTrackList';
import CourseVidioList from './src/pages/digital_employees/course_mgr/CourseVidioList';
import StudentCourseTrackList from './src/pages/digital_employees/course_mgr/StudentCourseTrackList';
import StudentCourseTrackDetailList from './src/pages/digital_employees/course_mgr/StudentCourseTrackDetailList';
import StudentCourseTrackDetail from './src/pages/digital_employees/course_mgr/StudentCourseTrackDetail';
import ViewDocument from './src/pages/digital_employees/ViewDocument';
import MyCourseDetail from './src/pages/digital_employees/course_mgr/MyCourseDetail';


const RootStack = createStackNavigator();

const LoginStack = createStackNavigator();
const MainStack = createBottomTabNavigator();
// 首页
const HomeStack = createStackNavigator();
// 租户管理
const TenantStack = createStackNavigator();
// 半成品
const SemiFinishedStack = createStackNavigator();
// 装窑管理
const EncastageStack = createStackNavigator();
// 隧道窑高温区管理
const WarmManageStack = createStackNavigator();
// 出库管理
const CheckOutStack = createStackNavigator();
// 个人中心
const PersonalCenterStack = createStackNavigator();
// demo测试中心
const DEMOStack = createStackNavigator();
// 学社首页
const DigitalEmployeesHomeStack = createStackNavigator();


// 客户管理
const CustomerListStack = createStackNavigator();
// 订单管理
const OrderListStack = createStackNavigator();
// 排产管理
const OrderSchedulingListStack = createStackNavigator();
// 烧结管理
const SinteringListStack = createStackNavigator();
// 库存管理-入库管理
const StorageInListStack = createStackNavigator();
// 库存管理-出库管理
const StorageOutListStack = createStackNavigator();
// 窑车管理
const KilnCarMgrListStack = createStackNavigator();
// 机台管理
const MachineMgrListStack = createStackNavigator();
// 产品管理
const BrickClassifyMgrListStack = createStackNavigator();
// 角色管理
const RoleListStack = createStackNavigator();
// 合同管理
const ContractStack = createStackNavigator();
// 日报查询
const QueryDailyStack = createStackNavigator();
// 成果圈
const HarvestCircleStack = createStackNavigator();
// 优秀成果
const GoodHarvestStack = createStackNavigator();
// 文档库
const DocumentLibraryStack = createStackNavigator();
// 首页
const HomePageStack = createStackNavigator();
// 校招
const SchoolreCruitmentStack = createStackNavigator();
// 我的
const MyselfStack = createStackNavigator();
// 消息未读
const MessageRemindStack = createStackNavigator();

// 耐材销售购买页面-堆栈
const NaicaiIndexStack = new createBottomTabNavigator();
// 耐材销售购买页面 - 首页
const NaicaiIndexStackDefautStack = createStackNavigator();
// 耐材销售购买页面 - 产品
const NaicaiIndexStackSellGoodsStack = createStackNavigator();
// 耐材销售购买页面 - 求购
const NaicaiIndexStackAskBugGoodsStack = createStackNavigator();
// 耐材销售购买页面 - 我的
const NaicaiIndexStackMyCenterOrLoginStack = createStackNavigator();

//在图标上添加徽章
function IconWithBadge({ icon, badgeCount, size }) {
  return (
    <View></View>
    // <View style={{ width: 24, height: 24, margin: 5 }}>
    //   <Image source={icon} style={{
    //     width: size,
    //     height: size
    //   }} />
    //   {badgeCount > 0 && (
    //     <View
    //       style={{
    //         // On React Native < 0.57 overflow outside of parent will not work on Android, see https://git.io/fhLJ8
    //         position: 'absolute',
    //         right: -6,
    //         top: -3,
    //         backgroundColor: 'red',
    //         borderRadius: 6,
    //         width: 12,
    //         height: 12,
    //         justifyContent: 'center',
    //         alignItems: 'center',
    //       }}
    //     >
    //       {/* <Text style={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>
    //         {badgeCount}
    //       </Text> */}
    //     </View>
    //   )}
    // </View>
  );
}

function HomeIconWithBadge(props) {
  // You should pass down the badgeCount in some other ways like React Context API, Redux, MobX or event emitters.
  return <IconWithBadge {...props} badgeCount={0} />;
}

// 登录的路由栈
const LoginStackScreen = () => {
  return (
    <LoginStack.Navigator headerMode={"none"}
    // screenOptions={{                 //用来定制头部信息、根据自己需要更改
    //   title: '测试标题',
    //   headerStyle: {
    //     backgroundColor: '#ee7530'
    //   },
    //   headerTintColor: '#fff',
    //   headerTitleStyle: {
    //     fontWeight: 'bold',
    //     fontSize: 20
    //   }
    // }}
    >
      <LoginStack.Screen name="LoginView" component={LoginView} />
      <LoginStack.Screen name="Privacy" component={Privacy} />
      <LoginStack.Screen name="UserAgreement" component={UserAgreement} />
      <LoginStack.Screen name="Register" component={Register} />
      <LoginStack.Screen name='ModifyPwd' component={ModifyPwd} />
    </LoginStack.Navigator>
  )
}


// 首页的路由栈
const HomeStackScreen = () => {
  return (
    <HomeStack.Navigator initialRouteName="Home" headerMode={"none"}>
      <HomeStack.Screen name="Home" component={Home}
      // options={{
      //   title: '首页'
      // }}
      />

      <HomeStack.Screen name="WelcomePage" component={WelcomePage} />

      <HomeStack.Screen name="OrgMgrAdd" component={OrgMgrAdd} />
      <HomeStack.Screen name="OrgMgrList" component={OrgMgrList} />

      <HomeStack.Screen name="PaymentObjectList" component={PaymentObjectList} />
      <HomeStack.Screen name="PaymentObjectAdd" component={PaymentObjectAdd} />
      <HomeStack.Screen name="PaymentClassList" component={PaymentClassList} />
      <HomeStack.Screen name="PaymentApplyList" component={PaymentApplyList} />
      <HomeStack.Screen name="PaymentAuditList" component={PaymentAuditList} />
      <HomeStack.Screen name="PaymentApplyAdd" component={PaymentApplyAdd} />
      <HomeStack.Screen name="PaymentClassAdd" component={PaymentClassAdd} />
      <HomeStack.Screen name="PaymengApplyDetail" component={PaymengApplyDetail} />
      <HomeStack.Screen name="PaymentAudit" component={PaymentAudit} />


      <HomeStack.Screen name="VerifyInternalSettingList" component={VerifyInternalSettingList} />
      <HomeStack.Screen name="VerifyInternalSettingAdd" component={VerifyInternalSettingAdd} />
      <HomeStack.Screen name="VerifyInternalStandardList" component={VerifyInternalStandardList} />
      <HomeStack.Screen name="VerifyInternalStandardAdd" component={VerifyInternalStandardAdd} />
      <HomeStack.Screen name="VerifyInternalResultList" component={VerifyInternalResultList} />
      <HomeStack.Screen name="VerifyInternalResultAdd" component={VerifyInternalResultAdd} />
      <HomeStack.Screen name="VerifyExternalSettingList" component={VerifyExternalSettingList} />
      <HomeStack.Screen name="VerifyExternalSettingAdd" component={VerifyExternalSettingAdd} />
      <HomeStack.Screen name="VerifyExternalStandardList" component={VerifyExternalStandardList} />
      <HomeStack.Screen name="VerifyExternalStandardAdd" component={VerifyExternalStandardAdd} />
      <HomeStack.Screen name="VerifyExternalResultList" component={VerifyExternalResultList} />
      <HomeStack.Screen name="VerifyExternalResultAdd" component={VerifyExternalResultAdd} />

      <HomeStack.Screen name="DepartmentList" component={DepartmentList} />
      <HomeStack.Screen name="DepartmentAdd" component={DepartmentAdd} />
      <HomeStack.Screen name="JobMgrList" component={JobMgrList} />
      <HomeStack.Screen name="JobMgrAdd" component={JobMgrAdd} />
      <HomeStack.Screen name="DepartmentStaffMgrList" component={DepartmentStaffMgrList} />
      <HomeStack.Screen name="JobStaffMgrList" component={JobStaffMgrList} />
      <HomeStack.Screen name="JobStaffMgrAdd" component={JobStaffMgrAdd} />
      <HomeStack.Screen name="EquipmentMgrList" component={EquipmentMgrList} />
      <HomeStack.Screen name="EquipmentMgrAdd" component={EquipmentMgrAdd} />
      <HomeStack.Screen name="EquipmentStateMgrAdd" component={EquipmentStateMgrAdd} />
      <HomeStack.Screen name="EquipmentStateMgrList" component={EquipmentStateMgrList} />
      <HomeStack.Screen name="CheckEquipmentList" component={CheckEquipmentList} />
      <HomeStack.Screen name="CheckEquipmentStateList" component={CheckEquipmentStateList} />
      <HomeStack.Screen name="CheckDepartmentList" component={CheckDepartmentList} />
      <HomeStack.Screen name="WorkingShiftRelStaffMgr" component={WorkingShiftRelStaffMgr} />
      <HomeStack.Screen name="PortalStaffMgrAdd" component={PortalStaffMgrAdd} />
      <HomeStack.Screen name="PortalStaffMgrList" component={PortalStaffMgrList} />
      <HomeStack.Screen name="EnterpriseInvitedApply" component={EnterpriseInvitedApply} />
      <HomeStack.Screen name="InformationConfigAdd" component={InformationConfigAdd} />
      <HomeStack.Screen name="InformationConfigList" component={InformationConfigList} />


      <HomeStack.Screen name="DailyList" component={DailyList} />
      <HomeStack.Screen name="DailyAdd" component={DailyAdd} />
      <HomeStack.Screen name="DailyMessageList" component={DailyMessageList} />
      <HomeStack.Screen name="DailyMessageAdd" component={DailyMessageAdd} />
      <HomeStack.Screen name="HarvestMgrList" component={HarvestMgrList} />
      <HomeStack.Screen name="HarvestMgrAdd" component={HarvestMgrAdd} />
      <HomeStack.Screen name="HarvestDetail" component={HarvestDetail} />
      <HomeStack.Screen name="DailyDetail" component={DailyDetail} />

      <HomeStack.Screen name="CourseMgrAdd" component={CourseMgrAdd} />
      <HomeStack.Screen name="CourseMgrList" component={CourseMgrList} />
      <HomeStack.Screen name="GoodHarvestList" component={GoodHarvestList} />
      <HomeStack.Screen name="HarvestCircleList" component={HarvestCircleList} />
      <HomeStack.Screen name="HarvestGoodMgrList" component={HarvestGoodMgrList} />
      <HomeStack.Screen name="MyBacklogDailyList" component={MyBacklogDailyList} />
      <HomeStack.Screen name="MyBacklogDailyAdd" component={MyBacklogDailyAdd} />
      <HomeStack.Screen name="MyDoneDailyList" component={MyDoneDailyList} />
      <HomeStack.Screen name="PromotionPlanAdd" component={PromotionPlanAdd} />
      <HomeStack.Screen name="PromotionPlanList" component={PromotionPlanList} />
      <HomeStack.Screen name="PromotionPlanDetail" component={PromotionPlanDetail} />
      <HomeStack.Screen name="PromotionPlanSelUser" component={PromotionPlanSelUser} />
      <HomeStack.Screen name="ScoreMgrAdd" component={ScoreMgrAdd} />
      <HomeStack.Screen name="ScoreMgrList" component={ScoreMgrList} />
      <HomeStack.Screen name="HarvestDiscussAdd" component={HarvestDiscussAdd} />
      <HomeStack.Screen name="HarvestDiscussList" component={HarvestDiscussList} />
      <HomeStack.Screen name="ScoreMgrCourseList" component={ScoreMgrCourseList} />
      <HomeStack.Screen name="QueryDaily" component={QueryDaily} />
      <HomeStack.Screen name="WorkDaily" component={WorkDaily} />
      <HomeStack.Screen name="QueryHarvest" component={QueryHarvest} />
      <HomeStack.Screen name="QueryMyScore" component={QueryMyScore} />
      <HomeStack.Screen name="QueryPromotionPlan" component={QueryPromotionPlan} />
      <HomeStack.Screen name="PortalTrackingList" component={PortalTrackingList} />
      <HomeStack.Screen name="PortalTrackingAdd" component={PortalTrackingAdd} />
      <HomeStack.Screen name="MyDocumentMgrList" component={MyDocumentMgrList} />
      <HomeStack.Screen name="DocumentLibraryMgrList" component={DocumentLibraryMgrList} />
      <HomeStack.Screen name="ViewDocument" component={ViewDocument} />
      <HomeStack.Screen name="VideoLibraryMgrList" component={VideoLibraryMgrList} />

      <HomeStack.Screen name="PointConfig" component={PointConfig} />
      <HomeStack.Screen name="PointConfigAdd" component={PointConfigAdd} />
      <HomeStack.Screen name="PointRecord" component={PointRecord} />
      <HomeStack.Screen name="PointReward" component={PointReward} />
      <HomeStack.Screen name="PointRewardAdd" component={PointRewardAdd} />
      <HomeStack.Screen name="PointExchange" component={PointExchange} />
      <HomeStack.Screen name="PointExchangeAdd" component={PointExchangeAdd} />
      <HomeStack.Screen name="PointRanking" component={PointRanking} />
      <HomeStack.Screen name="ExamConfig" component={ExamConfig} />
      <HomeStack.Screen name="ExamConfigAdd" component={ExamConfigAdd} />
      <HomeStack.Screen name="FormCollection" component={FormCollection} />
      <HomeStack.Screen name="ExamApply" component={ExamApply} />
      <HomeStack.Screen name="ExamApplyAdd" component={ExamApplyAdd} />
      <HomeStack.Screen name="CourseScheduling" component={CourseScheduling} />
      <HomeStack.Screen name="CourseSchedulingAdd" component={CourseSchedulingAdd} />
      <HomeStack.Screen name="CourseSchedulingTable" component={CourseSchedulingTable} />
      <HomeStack.Screen name="ExamApplyAudit" component={ExamApplyAudit} />
      <HomeStack.Screen name="SYPointMall" component={SYPointMall} />
      <HomeStack.Screen name="SYContactUs" component={SYContactUs} />
      <HomeStack.Screen name="AssessClassList" component={AssessClassList} />
      <HomeStack.Screen name="AssessClassAdd" component={AssessClassAdd} />
      <HomeStack.Screen name="AssessApplyAdd" component={AssessApplyAdd} />
      <HomeStack.Screen name="AssessApplyList" component={AssessApplyList} />
      <HomeStack.Screen name="AssessAuditList" component={AssessAuditList} />
      <HomeStack.Screen name="AssessQueryList" component={AssessQueryList} />
      <HomeStack.Screen name="AssessAudit" component={AssessAudit} />
      <HomeStack.Screen name="MyReceiveAdd" component={MyReceiveAdd} />
      <HomeStack.Screen name="MyReceiveList" component={MyReceiveList} />
      <HomeStack.Screen name="AskQuestionsQuery" component={AskQuestionsQuery} />
      <HomeStack.Screen name="MyAskQuestionsAdd" component={MyAskQuestionsAdd} />
      <HomeStack.Screen name="MyAskQuestionsList" component={MyAskQuestionsList} />
      <HomeStack.Screen name="AskQuestionsSolveTrackingAdd" component={AskQuestionsSolveTrackingAdd} />
      <HomeStack.Screen name="AskQuestionsSolveTrackingList" component={AskQuestionsSolveTrackingList} />

      <HomeStack.Screen name="CollegClassGradesAdd" component={CollegClassGradesAdd} />
      <HomeStack.Screen name="CollegClassGradesList" component={CollegClassGradesList} />
      <HomeStack.Screen name="CollegClassStudentList" component={CollegClassStudentList} />
      <HomeStack.Screen name="CollegProfessionalList" component={CollegProfessionalList} />
      <HomeStack.Screen name="CollegProfessionalAdd" component={CollegProfessionalAdd} />
      <HomeStack.Screen name="CollegStudentAdd" component={CollegStudentAdd} />
      <HomeStack.Screen name="CollegStudentList" component={CollegStudentList} />
      <HomeStack.Screen name="CollegStudentResumeQuery" component={CollegStudentResumeQuery} />
      <HomeStack.Screen name="EnterpriseInvitedInterView" component={EnterpriseInvitedInterView} />
      <HomeStack.Screen name="EnterpriseResumeCollection" component={EnterpriseResumeCollection} />
      <HomeStack.Screen name="PortalEnterpriseAdd" component={PortalEnterpriseAdd} />
      <HomeStack.Screen name="PortalEnterpriseList" component={PortalEnterpriseList} />
      <HomeStack.Screen name="StudentInterViewInvited" component={StudentInterViewInvited} />
      <HomeStack.Screen name="StudentMyChance" component={StudentMyChance} />
      <HomeStack.Screen name="StudentMyInterView" component={StudentMyInterView} />
      <HomeStack.Screen name="StudentMyInterViewPreview" component={StudentMyInterViewPreview} />
      <HomeStack.Screen name="EnterpriseRecruiterList" component={EnterpriseRecruiterList} />
      <HomeStack.Screen name="EnterpriseRecruiterAdd" component={EnterpriseRecruiterAdd} />
      <HomeStack.Screen name="EnterprisecrHiringPositionAdd" component={EnterprisecrHiringPositionAdd} />
      <HomeStack.Screen name="EnterprisecrHiringPositionList" component={EnterprisecrHiringPositionList} />
      <HomeStack.Screen name="EnterprisecrHiringPositionDetail" component={EnterprisecrHiringPositionDetail} />
      <HomeStack.Screen name="CollegPositionQuery" component={CollegPositionQuery} />
      <HomeStack.Screen name="PersonalInformation" component={PersonalInformation} />
      <HomeStack.Screen name="PersonalHonor" component={PersonalHonor} />
      <HomeStack.Screen name="CollegeEvaluation" component={CollegeEvaluation} />
      <HomeStack.Screen name="NewItem" component={NewItem} />
      <HomeStack.Screen name="EnterprisePositionDetail" component={EnterprisePositionDetail} />
      <HomeStack.Screen name="MemberManagementAdd" component={MemberManagementAdd} />
      <HomeStack.Screen name="MemberManagementList" component={MemberManagementList} />
      <HomeStack.Screen name="MemberManagementExamine" component={MemberManagementExamine} />
      <HomeStack.Screen name="MemberTypeAdd" component={MemberTypeAdd} />
      <HomeStack.Screen name="MemberTypeList" component={MemberTypeList} />
      <HomeStack.Screen name="ConfiguringTenants" component={ConfiguringTenants} />
      <HomeStack.Screen name="ConfiguringTenantsParam" component={ConfiguringTenantsParam} />
      <HomeStack.Screen name="ConfiguringTenantsParamItem" component={ConfiguringTenantsParamItem} />
      <HomeStack.Screen name="TenantEnterpriseAdd" component={TenantEnterpriseAdd} />
      <HomeStack.Screen name="TenantEnterpriseList" component={TenantEnterpriseList} />
      <HomeStack.Screen name="PortalTenantParam" component={PortalTenantParam} />
      <HomeStack.Screen name="PortalTenantParamAdd" component={PortalTenantParamAdd} />
      <HomeStack.Screen name="PortalTenantParamItem" component={PortalTenantParamItem} />
      <HomeStack.Screen name="PortalTenantParamData" component={PortalTenantParamData} />
      <HomeStack.Screen name="PortalTenantParamDataAdd" component={PortalTenantParamDataAdd} />  
      <HomeStack.Screen name="HomeResourceDisplay" component={HomeResourceDisplay} />
      <HomeStack.Screen name="MemberContactConfig" component={MemberContactConfig} />
      <HomeStack.Screen name="MemberContactConfigAdd" component={MemberContactConfigAdd} />
      <HomeStack.Screen name="ConfigPreview" component={ConfigPreview} />
      <HomeStack.Screen name="PermissionList" component={PermissionList} />
      <HomeStack.Screen name="PermissionUserList" component={PermissionUserList} />
      <HomeStack.Screen name="PermissionUserAddList" component={PermissionUserAddList} />

      <HomeStack.Screen name="CourseLevelMgrAdd" component={CourseLevelMgrAdd} />
      <HomeStack.Screen name="CourseLevelMgrList" component={CourseLevelMgrList} />
      <HomeStack.Screen name="CourseTaskMgrList" component={CourseTaskMgrList} />
      <HomeStack.Screen name="CourseTaskMgrDetail" component={CourseTaskMgrDetail} />
      <HomeStack.Screen name="CourseTrackDetail" component={CourseTrackDetail} />
      <HomeStack.Screen name="CourseTrackList" component={CourseTrackList} />
      <HomeStack.Screen name="CourseTypeMgrAdd" component={CourseTypeMgrAdd} />
      <HomeStack.Screen name="CourseTypeMgrList" component={CourseTypeMgrList} />
      <HomeStack.Screen name="MyCourseList" component={MyCourseList} />
      <HomeStack.Screen name="StudentCourseTrackList" component={StudentCourseTrackList} />
      <HomeStack.Screen name="CourseVidioList" component={CourseVidioList} />
      <HomeStack.Screen name="StudentCourseTrackDetailList" component={StudentCourseTrackDetailList} />
      <HomeStack.Screen name="StudentCourseTrackDetail" component={StudentCourseTrackDetail} />
      <HomeStack.Screen name="MyCourseDetail" component={MyCourseDetail} />
      <HomeStack.Screen name="PointRecordList" component={PointRecordList} />

      <RoleListStack.Screen name="RoleList" component={RoleList} />
      <RoleListStack.Screen name="RoleAdd" component={RoleAdd} />
      <RoleListStack.Screen name="RoleMenuList" component={RoleMenuList} />
      <RoleListStack.Screen name="RoleMenuAdd" component={RoleMenuAdd} />
      <RoleListStack.Screen name="QuickMenuList" component={QuickMenuList} />
      <RoleListStack.Screen name="QuickMenuAdd" component={QuickMenuAdd} />
      <RoleListStack.Screen name="RoleUserList" component={RoleUserList} />
      <RoleListStack.Screen name="RoleUserAdd" component={RoleUserAdd} />
      <RoleListStack.Screen name="UngradedCauseMgrList" component={UngradedCauseMgrList} />
      <RoleListStack.Screen name="UngradedCauseMgrAdd" component={UngradedCauseMgrAdd} />
      <RoleListStack.Screen name="OrderPositionMgrList" component={OrderPositionMgrList} />
      <RoleListStack.Screen name="OrderPositionMgrAdd" component={OrderPositionMgrAdd} />
      <RoleListStack.Screen name="WorkingShiftMgrList" component={WorkingShiftMgrList} />
      <RoleListStack.Screen name="WorkingShiftMgrAdd" component={WorkingShiftMgrAdd} />


      <PersonalCenterStack.Screen name="SuggestionFeedbackAdd" component={SuggestionFeedbackAdd} />
      <PersonalCenterStack.Screen name="SuggestionFeedbackList" component={SuggestionFeedbackList} />
      <PersonalCenterStack.Screen name="HelpCenterList" component={HelpCenterList} />

      <TenantStack.Screen name="TenantList" component={TenantList} />
      <TenantStack.Screen name="TenantAdd" component={TenantAdd} />
      <TenantStack.Screen name="OutsourcingTenantList" component={OutsourcingTenantList} />
      <TenantStack.Screen name="OutsourcingTenantAdd" component={OutsourcingTenantAdd} />
      <TenantStack.Screen name="ProductionLineMgrAdd" component={ProductionLineMgrAdd} />
      <TenantStack.Screen name="ProductionLineMgrList" component={ProductionLineMgrList} />
    </HomeStack.Navigator>
  )
}

const QueryDailyStackScreen = () => {
  return (
    <QueryDailyStack.Navigator headerMode={"none"}>
      <QueryDailyStack.Screen name="QueryDaily" component={QueryDaily} />
      <QueryDailyStack.Screen name="DailyMessageList" component={DailyMessageList} />
      <QueryDailyStack.Screen name="DailyMessageAdd" component={DailyMessageAdd} />
      <QueryDailyStack.Screen name="DailyDetail" component={DailyDetail} />
    </QueryDailyStack.Navigator>
  )
}
const HarvestCircleStackScreen = () => {
  return (
    <HarvestCircleStack.Navigator headerMode={"none"}>
      <HarvestCircleStack.Screen name="HarvestCircleList" component={HarvestCircleList} />
    </HarvestCircleStack.Navigator>
  )
}
const GoodHarvestStackScreen = () => {
  return (
    <GoodHarvestStack.Navigator headerMode={"none"}>
      <GoodHarvestStack.Screen name="GoodHarvestList" component={GoodHarvestList} />
    </GoodHarvestStack.Navigator>
  )
}
const DocumentLibraryStackScreen = () => {
  return (
    <DocumentLibraryStack.Navigator headerMode={"none"}>
      <DocumentLibraryStack.Screen name="DocumentLibraryMgrList" component={DocumentLibraryMgrList} />
    </DocumentLibraryStack.Navigator>
  )
}


const TenantStackScreen = () => {
  return (
    <TenantStack.Navigator headerMode={"none"}>
      <TenantStack.Screen name="TenantList" component={TenantList} />
      <TenantStack.Screen name="TenantAdd" component={TenantAdd} />
      <TenantStack.Screen name="OutsourcingTenantList" component={OutsourcingTenantList} />
      <TenantStack.Screen name="OutsourcingTenantAdd" component={OutsourcingTenantAdd} />
    </TenantStack.Navigator>
  )
}

const PersonalCenterStackScreen = () => {
  return (
    <PersonalCenterStack.Navigator headerMode={'none'} initialRouteName="SettingHome" >
      <PersonalCenterStack.Screen name="Profile" component={Profile} />
      <PersonalCenterStack.Screen name="SuggestionFeedbackAdd" component={SuggestionFeedbackAdd} />
      <PersonalCenterStack.Screen name="SuggestionFeedbackList" component={SuggestionFeedbackList} />
      <PersonalCenterStack.Screen name="MessageRemind" component={MessageRemind} />
      <PersonalCenterStack.Screen name="HelpCenterList" component={HelpCenterList} />
      <PersonalCenterStack.Screen name="DailyList" component={DailyList} />
      <PersonalCenterStack.Screen name="MyBacklogDailyList" component={MyBacklogDailyList} />
      <PersonalCenterStack.Screen name="PromotionPlanList" component={PromotionPlanList} />
      <PersonalCenterStack.Screen name="HarvestMgrList" component={HarvestMgrList} />
      <PersonalCenterStack.Screen name="DailyDetail" component={DailyDetail} />
      <PersonalCenterStack.Screen name="DailyAdd" component={DailyAdd} />
      <PersonalCenterStack.Screen name="PromotionPlanAdd" component={PromotionPlanAdd} />
      <PersonalCenterStack.Screen name="PromotionPlanDetail" component={PromotionPlanDetail} />
      <PersonalCenterStack.Screen name="PromotionPlanSelUser" component={PromotionPlanSelUser} />
      <PersonalCenterStack.Screen name="HarvestMgrAdd" component={HarvestMgrAdd} />
      <PersonalCenterStack.Screen name="HarvestDetail" component={HarvestDetail} />
    </PersonalCenterStack.Navigator>
  )
}

const RoleListStackScreen = () => {
  return (
    <RoleListStack.Navigator headerMode={"none"}>
      <RoleListStack.Screen name="RoleList" component={RoleList} />
      <RoleListStack.Screen name="RoleAdd" component={RoleAdd} />
      <RoleListStack.Screen name="RoleMenuList" component={RoleMenuList} />
      <RoleListStack.Screen name="RoleMenuAdd" component={RoleMenuAdd} />
      <RoleListStack.Screen name="RoleUserList" component={RoleUserList} />
      <RoleListStack.Screen name="RoleUserAdd" component={RoleUserAdd} />
      <RoleListStack.Screen name="UngradedCauseMgrAdd" component={UngradedCauseMgrAdd} />
      <RoleListStack.Screen name="UngradedCauseMgrList" component={UngradedCauseMgrList} />
    </RoleListStack.Navigator>
  )
}

const DEMOStackScreen = () => {
  return (
    <DEMOStack.Navigator headerMode={'none'}>
      <DEMOStack.Screen name="ReactNativeDemo" component={ReactNativeDemo} />
    </DEMOStack.Navigator>
  )
}

//实习平台首页
const HomePageStackScreen = () => {
  return (
    <HomePageStack.Navigator headerMode={'none'}>
      {/* <HomePageStack.Screen name="WelcomePage" component={WelcomePage} /> */}
      <HomePageStack.Screen name="StudentMyInterView" component={StudentMyInterView} />
      <HomePageStack.Screen name="StudentInterViewInvited" component={StudentInterViewInvited} />
      <HomePageStack.Screen name="StudentMyChance" component={StudentMyChance} />
    </HomePageStack.Navigator>
  )
}

//校招
const SchoolreCruitmentStackScreen = () => {
  return (
    <SchoolreCruitmentStack.Navigator headerMode={'none'}>
      <SchoolreCruitmentStack.Screen name="CollegPositionQuery" component={CollegPositionQuery} />
      <SchoolreCruitmentStack.Screen name="EnterprisecrHiringPositionDetail" component={EnterprisecrHiringPositionDetail} />
    </SchoolreCruitmentStack.Navigator>
  )
}

//我的
const MyselfStackScreen = () => {
  return (
    <MyselfStack.Navigator headerMode={'none'}>
      <MyselfStack.Screen name="Myself" component={Myself} />
      {/* <MyselfStack.Screen name="ChangePassword" component={ChangePassword} /> */}
      <MyselfStack.Screen name="Feedback" component={Feedback} />
      <MyselfStack.Screen name="StudentMyInterViewPreview" component={StudentMyInterViewPreview} />

    </MyselfStack.Navigator>
  )
}


//消息提醒
const MessageRemindStackScreen = () => {
  return (
    <MessageRemindStack.Navigator headerMode={'none'}>
      <MessageRemindStack.Screen name="MessageRemind" component={MessageRemind} />
    </MessageRemindStack.Navigator>
  )
}

//学社首页
const DigitalEmployeesHomeStackScreen = () => {
  return (
    <DigitalEmployeesHomeStack.Navigator initialRouteName="DigitalEmployeesHome" headerMode={'none'}>

      <HomeStack.Screen name="OrgMgrAdd" component={OrgMgrAdd} />
      <HomeStack.Screen name="OrgMgrList" component={OrgMgrList} />
      <HomeStack.Screen name="Profile" component={Profile} />
      <HomeStack.Screen name="PaymentObjectList" component={PaymentObjectList} />
      <HomeStack.Screen name="PaymentObjectAdd" component={PaymentObjectAdd} />
      <HomeStack.Screen name="PaymentClassList" component={PaymentClassList} />
      <HomeStack.Screen name="PaymentApplyList" component={PaymentApplyList} />
      <HomeStack.Screen name="PaymentAuditList" component={PaymentAuditList} />
      <HomeStack.Screen name="PaymentApplyAdd" component={PaymentApplyAdd} />
      <HomeStack.Screen name="PaymentClassAdd" component={PaymentClassAdd} />
      <HomeStack.Screen name="PaymengApplyDetail" component={PaymengApplyDetail} />
      <HomeStack.Screen name="PaymentAudit" component={PaymentAudit} />


      <HomeStack.Screen name="VerifyInternalSettingList" component={VerifyInternalSettingList} />
      <HomeStack.Screen name="VerifyInternalSettingAdd" component={VerifyInternalSettingAdd} />
      <HomeStack.Screen name="VerifyInternalStandardList" component={VerifyInternalStandardList} />
      <HomeStack.Screen name="VerifyInternalStandardAdd" component={VerifyInternalStandardAdd} />
      <HomeStack.Screen name="VerifyInternalResultList" component={VerifyInternalResultList} />
      <HomeStack.Screen name="VerifyInternalResultAdd" component={VerifyInternalResultAdd} />
      <HomeStack.Screen name="VerifyExternalSettingList" component={VerifyExternalSettingList} />
      <HomeStack.Screen name="VerifyExternalSettingAdd" component={VerifyExternalSettingAdd} />
      <HomeStack.Screen name="VerifyExternalStandardList" component={VerifyExternalStandardList} />
      <HomeStack.Screen name="VerifyExternalStandardAdd" component={VerifyExternalStandardAdd} />
      <HomeStack.Screen name="VerifyExternalResultList" component={VerifyExternalResultList} />
      <HomeStack.Screen name="VerifyExternalResultAdd" component={VerifyExternalResultAdd} />

      <HomeStack.Screen name="DepartmentList" component={DepartmentList} />
      <HomeStack.Screen name="DepartmentAdd" component={DepartmentAdd} />
      <HomeStack.Screen name="JobMgrList" component={JobMgrList} />
      <HomeStack.Screen name="JobMgrAdd" component={JobMgrAdd} />
      <HomeStack.Screen name="DepartmentStaffMgrList" component={DepartmentStaffMgrList} />
      <HomeStack.Screen name="JobStaffMgrList" component={JobStaffMgrList} />
      <HomeStack.Screen name="JobStaffMgrAdd" component={JobStaffMgrAdd} />
      <HomeStack.Screen name="EquipmentMgrList" component={EquipmentMgrList} />
      <HomeStack.Screen name="EquipmentMgrAdd" component={EquipmentMgrAdd} />
      <HomeStack.Screen name="EquipmentStateMgrAdd" component={EquipmentStateMgrAdd} />
      <HomeStack.Screen name="EquipmentStateMgrList" component={EquipmentStateMgrList} />
      <HomeStack.Screen name="CheckEquipmentList" component={CheckEquipmentList} />
      <HomeStack.Screen name="CheckEquipmentStateList" component={CheckEquipmentStateList} />
      <HomeStack.Screen name="CheckDepartmentList" component={CheckDepartmentList} />
      <HomeStack.Screen name="WorkingShiftRelStaffMgr" component={WorkingShiftRelStaffMgr} />
      <HomeStack.Screen name="PortalStaffMgrAdd" component={PortalStaffMgrAdd} />
      <HomeStack.Screen name="PortalStaffMgrList" component={PortalStaffMgrList} />
      <HomeStack.Screen name="EnterpriseInvitedApply" component={EnterpriseInvitedApply} />
      <HomeStack.Screen name="InformationConfigAdd" component={InformationConfigAdd} />
      <HomeStack.Screen name="InformationConfigList" component={InformationConfigList} />


      <HomeStack.Screen name="DailyList" component={DailyList} />
      <HomeStack.Screen name="DailyAdd" component={DailyAdd} />
      <HomeStack.Screen name="DailyMessageList" component={DailyMessageList} />
      <HomeStack.Screen name="DailyMessageAdd" component={DailyMessageAdd} />
      <HomeStack.Screen name="HarvestMgrList" component={HarvestMgrList} />
      <HomeStack.Screen name="HarvestMgrAdd" component={HarvestMgrAdd} />
      <HomeStack.Screen name="DailyDetail" component={DailyDetail} />

      <HomeStack.Screen name="CourseMgrAdd" component={CourseMgrAdd} />
      <HomeStack.Screen name="CourseMgrList" component={CourseMgrList} />
      <HomeStack.Screen name="GoodHarvestList" component={GoodHarvestList} />
      <HomeStack.Screen name="HarvestCircleList" component={HarvestCircleList} />
      <HomeStack.Screen name="HarvestGoodMgrList" component={HarvestGoodMgrList} />
      <HomeStack.Screen name="MyBacklogDailyList" component={MyBacklogDailyList} />
      <HomeStack.Screen name="MyBacklogDailyAdd" component={MyBacklogDailyAdd} />
      <HomeStack.Screen name="MyDoneDailyList" component={MyDoneDailyList} />
      <HomeStack.Screen name="PromotionPlanAdd" component={PromotionPlanAdd} />
      <HomeStack.Screen name="PromotionPlanList" component={PromotionPlanList} />
      <HomeStack.Screen name="PromotionPlanDetail" component={PromotionPlanDetail} />
      <HomeStack.Screen name="PromotionPlanSelUser" component={PromotionPlanSelUser} />
      <HomeStack.Screen name="ScoreMgrAdd" component={ScoreMgrAdd} />
      <HomeStack.Screen name="ScoreMgrList" component={ScoreMgrList} />
      <HomeStack.Screen name="HarvestDiscussAdd" component={HarvestDiscussAdd} />
      <HomeStack.Screen name="HarvestDiscussList" component={HarvestDiscussList} />
      <HomeStack.Screen name="ScoreMgrCourseList" component={ScoreMgrCourseList} />
      <HomeStack.Screen name="QueryDaily" component={QueryDaily} />
      <HomeStack.Screen name="WorkDaily" component={WorkDaily} />
      <HomeStack.Screen name="QueryHarvest" component={QueryHarvest} />
      <HomeStack.Screen name="QueryMyScore" component={QueryMyScore} />
      <HomeStack.Screen name="QueryPromotionPlan" component={QueryPromotionPlan} />
      <HomeStack.Screen name="PortalTrackingList" component={PortalTrackingList} />
      <HomeStack.Screen name="PortalTrackingAdd" component={PortalTrackingAdd} />
      <HomeStack.Screen name="MyDocumentMgrList" component={MyDocumentMgrList} />
      <HomeStack.Screen name="DocumentLibraryMgrList" component={DocumentLibraryMgrList} />
      <HomeStack.Screen name="ViewDocument" component={ViewDocument} />
      <HomeStack.Screen name="VideoLibraryMgrList" component={VideoLibraryMgrList} />

      <HomeStack.Screen name="PointConfig" component={PointConfig} />
      <HomeStack.Screen name="PointConfigAdd" component={PointConfigAdd} />
      <HomeStack.Screen name="PointRecord" component={PointRecord} />
      <HomeStack.Screen name="PointReward" component={PointReward} />
      <HomeStack.Screen name="PointRewardAdd" component={PointRewardAdd} />
      <HomeStack.Screen name="PointExchange" component={PointExchange} />
      <HomeStack.Screen name="PointExchangeAdd" component={PointExchangeAdd} />
      <HomeStack.Screen name="PointRanking" component={PointRanking} />
      <HomeStack.Screen name="ExamConfig" component={ExamConfig} />
      <HomeStack.Screen name="ExamConfigAdd" component={ExamConfigAdd} />
      <HomeStack.Screen name="FormCollection" component={FormCollection} />
      <HomeStack.Screen name="ExamApply" component={ExamApply} />
      <HomeStack.Screen name="ExamApplyAdd" component={ExamApplyAdd} />
      <HomeStack.Screen name="CourseScheduling" component={CourseScheduling} />
      <HomeStack.Screen name="CourseSchedulingAdd" component={CourseSchedulingAdd} />
      <HomeStack.Screen name="CourseSchedulingTable" component={CourseSchedulingTable} />
      <HomeStack.Screen name="ExamApplyAudit" component={ExamApplyAudit} />
      <HomeStack.Screen name="SYPointMall" component={SYPointMall} />
      <HomeStack.Screen name="SYContactUs" component={SYContactUs} />
      <HomeStack.Screen name="AssessClassList" component={AssessClassList} />
      <HomeStack.Screen name="AssessClassAdd" component={AssessClassAdd} />
      <HomeStack.Screen name="AssessApplyAdd" component={AssessApplyAdd} />
      <HomeStack.Screen name="AssessApplyList" component={AssessApplyList} />
      <HomeStack.Screen name="AssessAuditList" component={AssessAuditList} />
      <HomeStack.Screen name="AssessQueryList" component={AssessQueryList} />
      <HomeStack.Screen name="AssessAudit" component={AssessAudit} />
      <HomeStack.Screen name="MyReceiveAdd" component={MyReceiveAdd} />
      <HomeStack.Screen name="MyReceiveList" component={MyReceiveList} />
      <HomeStack.Screen name="AskQuestionsQuery" component={AskQuestionsQuery} />
      <HomeStack.Screen name="MyAskQuestionsAdd" component={MyAskQuestionsAdd} />
      <HomeStack.Screen name="MyAskQuestionsList" component={MyAskQuestionsList} />
      <HomeStack.Screen name="AskQuestionsSolveTrackingAdd" component={AskQuestionsSolveTrackingAdd} />
      <HomeStack.Screen name="AskQuestionsSolveTrackingList" component={AskQuestionsSolveTrackingList} />

      <HomeStack.Screen name="CollegClassGradesAdd" component={CollegClassGradesAdd} />
      <HomeStack.Screen name="CollegClassGradesList" component={CollegClassGradesList} />
      <HomeStack.Screen name="CollegClassStudentList" component={CollegClassStudentList} />
      <HomeStack.Screen name="CollegProfessionalList" component={CollegProfessionalList} />
      <HomeStack.Screen name="CollegProfessionalAdd" component={CollegProfessionalAdd} />
      <HomeStack.Screen name="CollegStudentAdd" component={CollegStudentAdd} />
      <HomeStack.Screen name="CollegStudentList" component={CollegStudentList} />
      <HomeStack.Screen name="CollegStudentResumeQuery" component={CollegStudentResumeQuery} />
      <HomeStack.Screen name="EnterpriseInvitedInterView" component={EnterpriseInvitedInterView} />
      <HomeStack.Screen name="EnterpriseResumeCollection" component={EnterpriseResumeCollection} />
      <HomeStack.Screen name="PortalEnterpriseAdd" component={PortalEnterpriseAdd} />
      <HomeStack.Screen name="PortalEnterpriseList" component={PortalEnterpriseList} />
      <HomeStack.Screen name="StudentInterViewInvited" component={StudentInterViewInvited} />
      <HomeStack.Screen name="StudentMyChance" component={StudentMyChance} />
      <HomeStack.Screen name="StudentMyInterView" component={StudentMyInterView} />
      <HomeStack.Screen name="StudentMyInterViewPreview" component={StudentMyInterViewPreview} />
      <HomeStack.Screen name="EnterpriseRecruiterList" component={EnterpriseRecruiterList} />
      <HomeStack.Screen name="EnterpriseRecruiterAdd" component={EnterpriseRecruiterAdd} />
      <HomeStack.Screen name="EnterprisecrHiringPositionAdd" component={EnterprisecrHiringPositionAdd} />
      <HomeStack.Screen name="EnterprisecrHiringPositionList" component={EnterprisecrHiringPositionList} />
      <HomeStack.Screen name="EnterprisecrHiringPositionDetail" component={EnterprisecrHiringPositionDetail} />
      <HomeStack.Screen name="CollegPositionQuery" component={CollegPositionQuery} />
      <HomeStack.Screen name="PersonalInformation" component={PersonalInformation} />
      <HomeStack.Screen name="PersonalHonor" component={PersonalHonor} />
      <HomeStack.Screen name="CollegeEvaluation" component={CollegeEvaluation} />
      <HomeStack.Screen name="NewItem" component={NewItem} />
      <HomeStack.Screen name="EnterprisePositionDetail" component={EnterprisePositionDetail} />
      <HomeStack.Screen name="MemberManagementAdd" component={MemberManagementAdd} />
      <HomeStack.Screen name="MemberManagementList" component={MemberManagementList} />
      <HomeStack.Screen name="MemberManagementDetail" component={MemberManagementDetail} />
      <HomeStack.Screen name="MemberManagementExamine" component={MemberManagementExamine} />
      <HomeStack.Screen name="MemberManagementExamineDetail" component={MemberManagementExamineDetail} />
      <HomeStack.Screen name="MemberTypeAdd" component={MemberTypeAdd} />
      <HomeStack.Screen name="MemberTypeList" component={MemberTypeList} />
      <HomeStack.Screen name="ConfiguringTenants" component={ConfiguringTenants} />
      <HomeStack.Screen name="ConfiguringTenantsParam" component={ConfiguringTenantsParam} />
      <HomeStack.Screen name="ConfiguringTenantsParamItem" component={ConfiguringTenantsParamItem} />
      <HomeStack.Screen name="TenantEnterpriseAdd" component={TenantEnterpriseAdd} />
      <HomeStack.Screen name="TenantEnterpriseList" component={TenantEnterpriseList} />
      <HomeStack.Screen name="PortalTenantParam" component={PortalTenantParam} />
      <HomeStack.Screen name="PortalTenantParamAdd" component={PortalTenantParamAdd} />
      <HomeStack.Screen name="PortalTenantParamItem" component={PortalTenantParamItem} />
      <HomeStack.Screen name="PortalTenantParamData" component={PortalTenantParamData} />
      <HomeStack.Screen name="PortalTenantParamDataAdd" component={PortalTenantParamDataAdd} />
      <HomeStack.Screen name="HomeResourceDisplay" component={HomeResourceDisplay} />
      <HomeStack.Screen name="MemberContactConfig" component={MemberContactConfig} />
      <HomeStack.Screen name="MemberContactConfigAdd" component={MemberContactConfigAdd} />
      <HomeStack.Screen name="ConfigPreview" component={ConfigPreview} />
      <HomeStack.Screen name="PermissionList" component={PermissionList} />
      <HomeStack.Screen name="PermissionUserList" component={PermissionUserList} />
      <HomeStack.Screen name="PermissionUserAddList" component={PermissionUserAddList} />

      <HomeStack.Screen name="CourseLevelMgrAdd" component={CourseLevelMgrAdd} />
      <HomeStack.Screen name="CourseLevelMgrList" component={CourseLevelMgrList} />
      <HomeStack.Screen name="CourseTaskMgrList" component={CourseTaskMgrList} />
      <HomeStack.Screen name="CourseTaskMgrDetail" component={CourseTaskMgrDetail} />
      <HomeStack.Screen name="CourseTrackDetail" component={CourseTrackDetail} />
      <HomeStack.Screen name="CourseTrackList" component={CourseTrackList} />
      <HomeStack.Screen name="CourseTypeMgrAdd" component={CourseTypeMgrAdd} />
      <HomeStack.Screen name="CourseTypeMgrList" component={CourseTypeMgrList} />
      <HomeStack.Screen name="CourseVidioList" component={CourseVidioList} />
      <HomeStack.Screen name="MyCourseList" component={MyCourseList} />
      <HomeStack.Screen name="StudentCourseTrackList" component={StudentCourseTrackList} />
      <HomeStack.Screen name="StudentCourseTrackDetailList" component={StudentCourseTrackDetailList} />
      <HomeStack.Screen name="StudentCourseTrackDetail" component={StudentCourseTrackDetail} />
      <HomeStack.Screen name="PointRecordList" component={PointRecordList} />

      <RoleListStack.Screen name="RoleList" component={RoleList} />
      <RoleListStack.Screen name="RoleAdd" component={RoleAdd} />
      <RoleListStack.Screen name="RoleMenuList" component={RoleMenuList} />
      <RoleListStack.Screen name="RoleMenuAdd" component={RoleMenuAdd} />
      <RoleListStack.Screen name="QuickMenuList" component={QuickMenuList} />
      <RoleListStack.Screen name="QuickMenuAdd" component={QuickMenuAdd} />
      <RoleListStack.Screen name="RoleUserList" component={RoleUserList} />
      <RoleListStack.Screen name="RoleUserAdd" component={RoleUserAdd} />
      <RoleListStack.Screen name="UngradedCauseMgrList" component={UngradedCauseMgrList} />
      <RoleListStack.Screen name="UngradedCauseMgrAdd" component={UngradedCauseMgrAdd} />
      <RoleListStack.Screen name="OrderPositionMgrList" component={OrderPositionMgrList} />
      <RoleListStack.Screen name="OrderPositionMgrAdd" component={OrderPositionMgrAdd} />
      <RoleListStack.Screen name="WorkingShiftMgrList" component={WorkingShiftMgrList} />
      <RoleListStack.Screen name="WorkingShiftMgrAdd" component={WorkingShiftMgrAdd} />


      <PersonalCenterStack.Screen name="SuggestionFeedbackAdd" component={SuggestionFeedbackAdd} />
      <PersonalCenterStack.Screen name="SuggestionFeedbackList" component={SuggestionFeedbackList} />
      <PersonalCenterStack.Screen name="HelpCenterList" component={HelpCenterList} />

      <TenantStack.Screen name="TenantList" component={TenantList} />
      <TenantStack.Screen name="TenantAdd" component={TenantAdd} />
      <TenantStack.Screen name="OutsourcingTenantList" component={OutsourcingTenantList} />
      <TenantStack.Screen name="OutsourcingTenantAdd" component={OutsourcingTenantAdd} />
      <TenantStack.Screen name="ProductionLineMgrAdd" component={ProductionLineMgrAdd} />
      <TenantStack.Screen name="ProductionLineMgrList" component={ProductionLineMgrList} />
    </DigitalEmployeesHomeStack.Navigator>
  )
}


const NaicaiIndexStackMyCenterOrLoginStackScreen = () => {
  return (
    <NaicaiIndexStackMyCenterOrLoginStack.Navigator headerMode={'none'}>
      <NaicaiIndexStackMyCenterOrLoginStack.Screen name="HomeStackScreen2" component={HomeStackScreen} />
      <NaicaiIndexStackMyCenterOrLoginStack.Screen name="RegisterScreen2" component={Register} />
      <NaicaiIndexStackMyCenterOrLoginStack.Screen name="PrivacyScreen2" component={Privacy} />
      <NaicaiIndexStackMyCenterOrLoginStack.Screen name="UserAgreementScreen2" component={UserAgreement} />
      <NaicaiIndexStackMyCenterOrLoginStack.Screen name='ModifyPwd' component={ModifyPwd} />
    </NaicaiIndexStackMyCenterOrLoginStack.Navigator>
  )
}

const NaicaiIndexStackScreen = () => {
  var menuDTOList = [{
    menuName: "首页",
    menuCode: "NaicaiIndexStackDefautScreen"
  }, {
    menuName: "产品",
    menuCode: "NaicaiIndexStackSellGoodStackScreen"
  }, {
    menuName: "求购",
    menuCode: "NaicaiIndexStackAskBugGoodsStackScreen"
  }, {
    menuName: "我的",
    menuCode: "NaicaiIndexStackMyCenterOrLoginStackScreen"
  }]

  return (
    <NaicaiIndexStack.Navigator
      headerMode='none'
      initialRouteName="我的"
      style={{ height: platformos === 'ios' ? 62 : 100 }}
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          if (route.name === '首页') {
            return (
              <Image
                source={focused ?
                  require('./src/assets/icon/bottom_navigate/naicai_index_lighted.png') :
                  require('./src/assets/icon/bottom_navigate/naicai_index.png')}
                style={{ width: size, height: size }}
              />
            );
          } else if (route.name === '产品') {
            return (
              <Image
                source={focused ?
                  require('./src/assets/icon/bottom_navigate/naicai_product_lighted.png') :
                  require('./src/assets/icon/bottom_navigate/naicai_product.png')}
                style={{ width: size, height: size }}
              />
            );
          } else if (route.name === '求购') {
            return (
              <Image
                source={focused ?
                  require('./src/assets/icon/bottom_navigate/naicai_ask_bug_lighted.png') :
                  require('./src/assets/icon/bottom_navigate/naicai_ask_bug.png')}
                style={{ width: size, height: size }}
              />
            );
          } else if (route.name === '我的') {
            return (
              <Image
                source={focused ?
                  require('./src/assets/icon/bottom_navigate/naicai_my_lighted.png') :
                  require('./src/assets/icon/bottom_navigate/naicai_my.png')}
                style={{ width: size, height: size }}
              />
            );
          } else if (route.name === '工作台') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={
                        require('./src/assets/icon/bottom_navigate/workbench_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={
                        require('./src/assets/icon/bottom_navigate/workbench1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>工作台</Text>
                }
              </View>
            );
          }
        },
      })}
      tabBarOptions={{
        activeTintColor: '#333333',
        inactiveTintColor: '#b6b6b6',
        pressColor: '#823453',
        pressOpacity: 0.8,
        style: [{
          borderTopColor: '#ebebeb',
          borderTopWidth: 1,
          backgroundColor: '#FFFFFF',
          height: ifIphoneXTabBarOptionHeight()
        }],
        labelStyle: {
          fontSize: 15,
          margin: 1,
          marginBottom: 2,
        },
      }}
    >
      {
        menuDTOList.map((elem, index) => {
          if ('NaicaiIndexStackDefautScreen' == elem.menuCode) {
            return (
              <NaicaiIndexStack.Screen key={elem.menuCode} name="首页" component={NaicaiIndexStackDefautScreen} />
            )
          }
          else if ('NaicaiIndexStackSellGoodStackScreen' == elem.menuCode) {
            return (
              <NaicaiIndexStack.Screen key={elem.menuCode} name="产品" component={NaicaiIndexStackSellGoodStackScreen} />
            )
          }
          else if ('NaicaiIndexStackAskBugGoodsStackScreen' == elem.menuCode) {
            return (
              <NaicaiIndexStack.Screen key={elem.menuCode} name="求购" component={NaicaiIndexStackAskBugGoodsStackScreen} />
            )
          }
          else if ('NaicaiIndexStackMyCenterOrLoginStackScreen' == elem.menuCode) {
            return (
              <NaicaiIndexStack.Screen key={elem.menuCode} name="我的" component={NaicaiIndexStackMyCenterOrLoginStackScreen} />
            )
          }
        })
      }
    </NaicaiIndexStack.Navigator>
  )
}

const judgeIsDigitalEmployee = () => {
  if (null != constants.tenantExtAttrJSON) {
    if (null != constants.tenantExtAttrJSON.menuTypes && (constants.tenantExtAttrJSON.menuTypes.indexOf('D') > 0 || constants.tenantExtAttrJSON.menuTypes.indexOf('R') > 0)) {
      return true
    }
  }
  return false
}

const MainStackScreen = () => {
  var menuDTOList = [{
    menuName: "个人中心",
    menuCode: "PersonalCenterStackScreen"
  }]

  if (constants && constants.roleInfo && constants.roleInfo.menuDTOList && constants.roleInfo.menuDTOList.length > 0) {
    menuDTOList = constants.roleInfo.menuDTOList;
  }

  return (
    <MainStack.Navigator
      headerMode='none'
      initialRouteName={judgeIsDigitalEmployee() ? '学社首页' : '工作台'}
      style={{ height: platformos === 'ios' ? 62 : 100 }}
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          if (route.name === '工作台') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={
                        require('./src/assets/icon/bottom_navigate/workbench_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={
                        require('./src/assets/icon/bottom_navigate/workbench1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 65, }}>

                  {focused ? null :
                    <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>工作台</Text>
                  }
                </View>

              </View>
            );
          } else if (route.name === '租户管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/customer_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/customer1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>租户管理</Text>
                }
              </View>
            );
          } else if (route.name === '半成品管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/sime_finished_list_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/sime_finished_list1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>半成品</Text>
                }
              </View>
            );
          } else if (route.name === '装窑管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/foot_encastage_icon_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/foot_encastage_icon.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>装窑管理</Text>
                }
              </View>
            );
          } else if (route.name === '温度管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/foot_temperature_icon_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/foot_temperature_icon1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>温度管理</Text>
                }
              </View>
            );
          } else if (route.name === '出库管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/check_out_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/check_out1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>出库管理</Text>
                }
              </View>
            );
          } else if (route.name === '个人中心') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/user_center_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/user_center1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 65, }}>
                  {focused ? null :
                    <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>个人中心</Text>
                  }
                </View>
              </View>
            );
          } else if (route.name === '测试') {
            return (
              <Image
                source={focused ?
                  require('./src/assets/icon/testIcon_lighted.png') :
                  require('./src/assets/icon/testIcon.png')}
                style={{ width: size, height: size }}
              />
            );
          } else if (route.name === '客户管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/custer_list_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/custer_list1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>客户管理</Text>
                }
              </View>
            );
          } else if (route.name === '订单管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/order_list_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/order_list1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>订单管理</Text>
                }
              </View>
            );
          } else if (route.name === '排产管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/scheduling_list_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/scheduling_list1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>排产管理</Text>
                }
              </View>
            );
          } else if (route.name === '烧结管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/sintering_list_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/sintering_list1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>烧结管理</Text>
                }
              </View>
            );
          } else if (route.name === '入库管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/package_store_in_list_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/package_store_in_list1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>入库管理</Text>
                }
              </View>
            );
          } else if (route.name === '出库管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/check_out_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/check_out1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>出库管理</Text>
                }
              </View>
            );
          } else if (route.name === '窑车管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/foot_encastage_icon_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/foot_encastage_icon.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>窑车管理</Text>
                }
              </View>
            );
          } else if (route.name === '机台管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/machine_list_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/machine_list1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>机台管理</Text>
                }
              </View>
            );
          } else if (route.name === '产品管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/brick_classify_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/brick_classify1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>产品管理</Text>
                }
              </View>
            );
          } else if (route.name === '角色管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/customer_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/customer1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>角色管理</Text>
                }
              </View>
            );
          } else if (route.name === '合同管理') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/contract_list_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/contract_list1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>合同管理</Text>
                }
              </View>
            );
          } else if (route.name === '日报查询') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/query_daily_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/query_daily1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 65, }}>

                  {focused ? null :
                    <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>日报查询</Text>
                  }
                </View>

              </View>
            );
          } else if (route.name === '成果圈') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/query_harvest_circle_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/query_harvest_circle1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>成果圈</Text>
                }
              </View>
            );
          } else if (route.name === '优秀成果') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/query_good_harvest_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/query_good_harvest1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>优秀成果</Text>
                }
              </View>
            );
          } else if (route.name === '资源库') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/document_library_lighted1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/bottom_navigate/document_library1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>资源库</Text>
                }
              </View>
            );
          } else if (route.name === '首页') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/home_page_lighted_icon1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/home_page_icon1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>首页</Text>
                }
              </View>
            );
          } else if (route.name === '校招') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/school_recruitment_lighted_icon1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/school_recruitment_icon1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>校招</Text>
                }
              </View>
            );
          } else if (route.name === '我的') {
            return (
              <View style={{ position: 'relative', alignItems: 'center', justifyContent: 'center', width: 60, height: size }}>
                {
                  focused ?
                    <Image
                      source={require('./src/assets/icon/myself_lighted_icon1.png')}
                      style={{ width: 50, height: 50 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/myself_icon1.png')}
                      style={{ width: 30, height: 30 }}
                    />
                }
                {focused ? null :
                  <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>我的</Text>
                }
              </View>
            );
          } else if (route.name === '我的消息') {
            return (
              <View style={{
                position: 'relative', alignItems: 'center',
                justifyContent: 'center', width: 60, height: size
              }}>
                {
                  focused ?
                    <Image
                      source={
                        require('./src/assets/icon/message_reminder_light_icon1.png')}
                      style={{ width: 50, height: 50, position: 'relative', left: 0, top: 0 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/message_reminder_icon1.png')}
                      style={{ width: 30, height: 30, position: 'relative', left: 0, top: 0 }}
                    />
                }
                <View style={{
                  position: 'relative', alignItems: 'center',
                  justifyContent: 'center', width: 65,
                }}>
                  {focused ? null :
                    <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>我的消息</Text>
                  }
                </View>
              </View>
            );
          } else if (route.name === '学社首页') {
            return (
              <View style={{
                position: 'relative', alignItems: 'center',
                justifyContent: 'center', width: 60, height: size
              }}>
                {
                  focused ?
                    <Image
                      source={
                        require('./src/assets/icon/home_page_lighted_icon1.png')}
                      style={{ width: 50, height: 50, position: 'relative', left: 0, top: 0 }}
                    />
                    :
                    <Image
                      source={require('./src/assets/icon/home_page_icon1.png')}
                      style={{ width: 30, height: 30, position: 'relative', left: 0, top: 0 }}
                    />
                }
                <View style={{
                  position: 'relative', alignItems: 'center',
                  justifyContent: 'center', width: 65,
                }}>
                  {focused ? null :
                    <Text style={{ color: '#33333375', fontSize: 14, flexWrap: 'wrap', alignItems: 'center' }}>首页</Text>
                  }
                </View>
              </View>
            );
          }
        },
      })}
      tabBarOptions={{
        showLabel: false,
        activeTintColor: '#333333',
        inactiveTintColor: '#b6b6b6',
        pressColor: '#823453',
        pressOpacity: 0.8,
        style: [{
          borderTopColor: '#ebebeb',
          borderTopWidth: 1,
          backgroundColor: '#FFFFFF',
          height: ifIphoneXTabBarOptionHeight()
        }],
        labelStyle: {
          fontSize: 15,
          margin: 1,
          marginBottom: 2,
        },
      }}
    >
      {
        menuDTOList.map((elem, index) => {
          if ('HomeStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="工作台" component={HomeStackScreen} />
            )
          }
          else if ('QueryDailyStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="日报查询" component={QueryDailyStackScreen} />
            )
          }
          else if ('HarvestCircleStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="成果圈" component={HarvestCircleStackScreen} />
            )
          }
          else if ('DocumentLibraryStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="资源库" component={DocumentLibraryStackScreen} />
            )
          }
          else if ('GoodHarvestStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="优秀成果" component={GoodHarvestStackScreen} />
            )
          }
          else if ('TenantStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="租户管理" component={TenantStackScreen} />
            )
          }
          else if ('SemiFinishedStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="半成品管理" component={SemiFinishedStackScreen} />
            )
          }
          else if ('EncastageStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="装窑管理" component={EncastageStackScreen} />
            )
          }
          else if ('WarmManageStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="温度管理" component={WarmManageStackScreen} />
            )
          }
          else if ('CheckOutStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="出库管理" component={CheckOutStackScreen} />
            )
          }
          else if ('PersonalCenterStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="个人中心" component={PersonalCenterStackScreen} />
            )
          }
          else if ('CustomerListStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="客户管理" component={CustomerListStackScreen} />
            )
          }
          else if ('OrderListStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="订单管理" component={OrderListStackScreen} />
            )
          }
          else if ('OrderSchedulingListStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="排产管理" component={OrderSchedulingListStackScreen} />
            )
          }
          else if ('SinteringListStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="烧结管理" component={SinteringListStackScreen} />
            )
          }
          else if ('StorageInListStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="入库管理" component={StorageInListStackScreen} />
            )
          }
          else if ('StorageOutListStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="出库管理" component={StorageOutListStackScreen} />
            )
          }
          else if ('KilnCarMgrListStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="窑车管理" component={KilnCarMgrListStackScreen} />
            )
          }
          else if ('MachineMgrListStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="机台管理" component={MachineMgrListStackScreen} />
            )
          }
          else if ('BrickClassifyMgrListStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="产品管理" component={BrickClassifyMgrListStackScreen} />
            )
          }
          else if ('RoleListStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="角色管理" component={RoleListStackScreen} />
            )
          }
          else if ('ContractStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="合同管理" component={ContractStackScreen} />
            )
          }
          else if ('HomePageStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="首页" component={HomePageStackScreen} />
            )
          }
          else if ('SchoolreCruitmentStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="校招" component={SchoolreCruitmentStackScreen} />
            )
          }
          else if ('MyselfStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="我的" component={MyselfStackScreen} />
            )
          }
          else if ('MessageRemindStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="我的消息" component={MessageRemindStackScreen} />
            )
          }
          else if ('DigitalEmployeesHomeStackScreen' == elem.menuCode) {
            return (
              <MainStack.Screen key={elem.menuCode} name="学社首页" component={DigitalEmployeesHomeStackScreen} />
            )
          }
        })
      }
    </MainStack.Navigator>
  )
}

const AppNavigation5 = () => {
  return (
    <NavigationContainer>
      <RootStack.Navigator initialRouteName={'LoginStack'} screenOptions={{ headerShown: false }}>
        <RootStack.Screen name="LoginStack" component={LoginStackScreen} />
        <RootStack.Screen name='MainStack' component={MainStackScreen} />
        <RootStack.Screen name='NaicaiIndexStackScreen' component={NaicaiIndexStackScreen} />
        <RootStack.Screen name="VideoLibraryView" component={VideoLibraryView} />
        <RootStack.Screen name="DocumentLibraryView" component={DocumentLibraryView} />
        {/* 页面不展示底部导航栏 */}
        <RootStack.Screen name='MemberManagementDetail' component={MemberManagementDetail} />
        <RootStack.Screen name='MemberManagementExamineDetail' component={MemberManagementExamineDetail} />
      </RootStack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    backgroundColor: Colors.lighter,
  },
  engine: {
    position: 'absolute',
    right: 0,
  },
  body: {
    backgroundColor: Colors.white,
  },
  sectionContainer: {
    marginTop: 32,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.black,
  },
  sectionDescription: {
    marginTop: 8,
    fontSize: 18,
    fontWeight: '400',
    color: Colors.dark,
  },
  highlight: {
    fontWeight: '700',
  },
  footer: {
    color: Colors.dark,
    fontSize: 12,
    fontWeight: '600',
    padding: 4,
    paddingRight: 12,
    textAlign: 'right',
  },
});

export default AppNavigation5;

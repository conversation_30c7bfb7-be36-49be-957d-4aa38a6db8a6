import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,
    FlatList,RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class WorkingShiftMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { shiftId } = route.params;
            if (shiftId) {
                console.log("=============shiftId" + shiftId + "");
            }
        }
        this.loadWorkingShiftList();
    }



        // 回调函数
        callBackFunction=()=>{
            let url= "/biz/working/shift/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    
        // 下拉触顶刷新到第一页
        _loadFreshData=()=>{
            if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
                console.log("==========不刷新=====");
                return;
            }
            this.setState({
                currentPage:1
            })
            let url= "/biz/working/shift/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    
        _loadFreshDataCallBack=(response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                var dataNew = response.data.dataList;
                // dataOld.unshift(dataNew);
                var dataAll = [...dataNew];
                this.setState({
                    dataSource:dataAll,
                    currentPage:response.data.currentPage + 1,
                    totalPage:response.data.totalPage,
                    totalRecord:response.data.totalRecord,
                    refreshing:false
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        }
    
        flatListFooterComponent=()=>{
            return(
                <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
            )
        }
    
        // 上拉触底加载下一页
        _loadNextData=()=>{
            if ((this.state.currentPage-1) >= this.state.totalPage) {
                WToast.show({data:"已经是最后一页了，我们也是有底线的"});
                return;
            }
            this.setState({
                refreshing:true
            })
            this.loadWorkingShiftList();
        }
    
        loadWorkingShiftList=()=>{
            let url= "/biz/working/shift/list";
            let loadRequest={
                "currentPage": this.state.currentPage,
                "pageSize": this.state.pageSize,
            };
            httpPost(url, loadRequest, this.loadWorkingShiftListCallBack);
        }
    
    
        loadWorkingShiftListCallBack=(response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                var dataNew = response.data.dataList;
                var dataOld = this.state.dataSource;
                // dataOld.unshift(dataNew);
                var dataAll = [...dataOld,...dataNew];
                this.setState({
                    dataSource:dataAll,
                    currentPage:response.data.currentPage + 1,
                    totalPage:response.data.totalPage,
                    totalRecord:response.data.totalRecord,
                    refreshing:false
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        }
    
        deleteWorkingShift =(shiftId)=> {
            console.log("=======delete=shiftId", shiftId);
            let url= "/biz/working/shift/delete";
            let requestParams={'shiftId':shiftId};
            httpDelete(url, requestParams, this.deleteCallBack);
        }
    
        // 删除操作的回调操作
        deleteCallBack=(response)=>{
            if (response.code == 200 && response.data) {
                WToast.show({data:"删除完成"});
                this.callBackFunction();
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({data:response.message});
            }
        }
    
        renderRow=(item, index)=>{
            return (
                <View key={item.shiftId} style={styles.innerViewStyle}>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>班次名称：{item.shiftName}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>生产车间：{item.productionLineName}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>所属部门：{item.shiftTypeName}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>排序：{item.shiftSort}</Text>
                    </View>
                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                        <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("WorkingShiftRelStaffMgr", 
                                {
                                    // 传递参数
                                    shiftId:item.shiftId,
                                    // 传递回调函数
                                    refresh: this.callBackFunction 
                                })
                            }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:75,flexDirection:"row",backgroundColor:"#FFB800"}]}>
                                <Image  style={{width:20, height:20,marginRight:3}} source={require('../../assets/icon/iconfont/shift.png')}></Image>
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>排班</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                            Alert.alert('确认','您确定要删除该班次吗？',[
                                {
                                    text:"取消", onPress:()=>{
                                    WToast.show({data:'点击了取消'});
                                    // this在这里可用，传到方法里还有问题
                                    // this.props.navigation.goBack();
                                    }
                                },
                                {
                                    text:"确定", onPress:()=>{
                                        WToast.show({data:'点击了确定'});
                                        this.deleteWorkingShift(item.shiftId)
                                    }
                                }
                            ]);
                        }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:75,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("WorkingShiftMgrAdd", 
                                {
                                    // 传递参数
                                    shiftId:item.shiftId,
                                    productionLineId:item.productionLineId,
                                    shiftType:item.shiftType,
                                    // 传递回调函数
                                    refresh: this.callBackFunction 
                                })
                            }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:75,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                        </TouchableOpacity>
                    </View>
                </View>
            )
        }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("WorkingShiftMgrAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
                {/* <Text style={CommonStyle.headRightText}>新增班次</Text> */}
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='班次管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});
import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image, Modal } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../../component/CommonHeadScreen';
var CommonStyle = require('../../../assets/css/CommonStyle');
import { saveImage } from '../../../utils/CameraRollUtils';
import { uploadMultiImageLibrary } from '../../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class MyAskQuestionsAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            askQuestionsId: "",
            askQuestionsTitle: "",
            askQuestionsContent: "",
            askQuestionsUserId: "",
            askQuestionsUserName: constants.loginUser.userName,
            compressFileList:[],
            pictureIndex:0,
            isShowImage: false,     //  显示弹窗组件
            urls:[
                // {
                //     url:'http://10.162.210.158/image_a/10/2021-12/rn_image_picker_lib_temp_a45ead39-8c25-4fb5-b388-12be9d46cf62_200052052_small.jpg'
                // }
            ]
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { askQuestionsId } = route.params;
            if (askQuestionsId) {
                this.setState({
                    operate: "编辑",
                    askQuestionsId: askQuestionsId
                })
                loadTypeUrl = "/biz/portal/ask/questions/get";
                loadRequest = { 'askQuestionsId': askQuestionsId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditMyAskQuestionsDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
        }
    }

    loadEditMyAskQuestionsDataCallBack = (response) => {
        if (response.code == 200 && response.data) {

            this.setState({
                askQuestionsId:response.data.askQuestionsId,
                askQuestionsTitle: response.data.askQuestionsTitle,
                askQuestionsContent: response.data.askQuestionsContent,
                askQuestionsUserName:response.data.askQuestionsUserName,
                compressFileList:response.data.compressFileList,
            })
            var urls = [];
            if(response.data.compressFileList && response.data.compressFileList.length > 0){
                for(var i=0;i<response.data.compressFileList.length;i++){
                    var url = {
                        url:constants.image_addr + '/' +  response.data.compressFileList[i].compressFile
                    }
                    urls=urls.concat(url)
                    console.log(url)
                }
            }
            this.setState({
                urls:urls
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("MyAskQuestionsList")
            }}>
                <Text style={CommonStyle.headRightText}>我的提问</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    saveMyAskQuestions = () => {
        console.log("=======saveMyAskQuestions");
        let toastOpts;
        if (!this.state.askQuestionsTitle) {
            toastOpts = getFailToastOpts("请填写标题");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.askQuestionsContent) {
            toastOpts = getFailToastOpts("请填写内容");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/portal/ask/questions/add";
        if (this.state.askQuestionsId) {
            console.log("=========Edit===askQuestionsId", this.state.askQuestionsId)
            url = "/biz/portal/ask/questions/modify";
        }
        let requestParams = {
            askQuestionsId: this.state.askQuestionsId,
            askQuestionsTitle: this.state.askQuestionsTitle,
            askQuestionsContent: this.state.askQuestionsContent,
            compressFileList:this.state.compressFileList,
        };
        httpPost(url, requestParams, this.saveMyAskQuestionsCallBack);
    }

    // 保存回调函数
    saveMyAskQuestionsCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '提问'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.formContentViewStyle]}>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>标题</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <TextInput
                            style={CommonStyle.inputRowText}
                            placeholder={'请输入标题'}
                            onChangeText={(text) => this.setState({ askQuestionsTitle: text })}
                        >
                            {this.state.askQuestionsTitle}
                        </TextInput>
                    </View>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>内容</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle, { height: 200 }]}>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText, { height: 200 }]}
                            placeholder={'请输入内容'}
                            onChangeText={(text) => this.setState({ askQuestionsContent: text })}
                        >
                            {this.state.askQuestionsContent}
                        </TextInput>
                    </View>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>附件（最多5张）</Text>
                    </View>
                    <View>
                        {
                            this.state.compressFileList && this.state.compressFileList.length > 0 ?
                            (
                                <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                    {
                                        this.state.compressFileList.map((item,index) =>{
                                            return(
                                                <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex'}]}>
                                                <TouchableOpacity
                                                    style={{position:'absolute',left:110,top:-10,zIndex:1000}}
                                                    onPress={() => {
                                                        console.log("========deletePhoto")
                                                        var urls = this.state.urls;
                                                        var compressFileList = this.state.compressFileList;

                                                        urls.splice(index,1);
                                                        compressFileList.splice(index,1);
                                                        console.log(urls)
                                                        console.log(this.state.compressFileList)

                                                        this.setState({
                                                            urls:urls,
                                                            compressFileList:compressFileList
                                                        })
                                                    }}
                                                >
                                                    <Image style={{ width: 22, height: 22}} source={require('../../../assets/icon/iconfont/deleteRed.png')}></Image>

                                                </TouchableOpacity>
                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        isShowImage:true,
                                                        pictureIndex:index
                                                    })
                                                    // uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                                    //     console.log("========imageUploadResponse", imageUploadResponse)
                                                    //     if (imageUploadResponse.code === 200) {
                                                    //         WToast.show({ data: "上传成功" });
                                                    //         let compressFileList = imageUploadResponse.data
                                                    //         this.setState({
                                                    //             compressFileList: compressFileList
                                                    //         })
                                                    //     }
                                                    //     else {
                                                    //         WToast.show({ data: imageUploadResponse.message });
                                                    //     }
                                                    // });

                                                }}>
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />

                                                </TouchableOpacity>
                                                <Modal visible={this.state.isShowImage} transparent={true}>
                                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}}  index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                        onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls}
                                                        onSave={() => {
                                                        saveImage( this.state.urls[this.state.pictureIndex].url)
                                                        }}/>
                                                </Modal>
                                            </View>
                                            )

                                        })
                                    }

                                    {
                                        this.state.compressFileList.length < 5 ?
                                        (
                                            <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                                <TouchableOpacity onPress={() => {
                                                    uploadMultiImageLibrary(5, "big_size_image_5000", (imageUploadResponse) => {
                                                        console.log("========imageUploadResponse", imageUploadResponse)
                                                        if (imageUploadResponse.code === 200) {
                                                            WToast.show({ data: "上传成功" });
                                                            let compressFileList = imageUploadResponse.data
                                                            this.setState({
                                                                compressFileList: this.state.compressFileList.concat(compressFileList)
                                                            })
                                                            var urls = this.state.urls;
                                                            if(compressFileList && compressFileList.length > 0){
                                                                for(var i=0;i<compressFileList.length;i++){
                                                                    var url = {
                                                                        url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                                    }
                                                                    urls=urls.concat(url)
                                                                    console.log(url)
                                                                }
                                                            }
                                                            this.setState({
                                                                urls:urls
                                                            })
                                                        }
                                                        else {
                                                            WToast.show({ data: imageUploadResponse.message });
                                                        }
                                                    });

                                                }}>
                                                    <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                                        <Image source ={require('../../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                        )
                                        :
                                        <View/>
                                    }                                                                    
                                </View>
                            )
                            :
                            <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                            <TouchableOpacity onPress={() => {
                                    uploadMultiImageLibrary(5, "big_size_image_5000", (imageUploadResponse) => {
                                        console.log("========imageUploadResponse", imageUploadResponse)
                                        if (imageUploadResponse.code === 200) {
                                            WToast.show({ data: "上传成功" });
                                            let compressFileList = imageUploadResponse.data
                                            this.setState({
                                                compressFileList: compressFileList
                                            })
                                            var urls = [];
                                            if(compressFileList && compressFileList.length > 0){
                                                for(var i=0;i<compressFileList.length;i++){
                                                    var url = {
                                                        url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                    }
                                                    urls=urls.concat(url)
                                                    console.log(url)
                                                }
                                            }
                                            this.setState({
                                                urls:urls
                                            })
                                        }
                                        else {
                                            WToast.show({ data: imageUploadResponse.message });
                                        }
                                    });

                                }}>
                                    <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                        <Image source ={require('../../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        }
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>提问人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            editable={false} 
                            style={styles.inputRightText}
                            placeholder={'请输入提问人'}
                            onChangeText={(text) => this.setState({askQuestionsUserName:text})}
                        >
                            {this.state.askQuestionsUserName}
                        </TextInput>
                    </View>

                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveMyAskQuestions.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row', width: 130, height: 40, marginRight: 35, marginTop: 15 }]}>
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width:leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
});
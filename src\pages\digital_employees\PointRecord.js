import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    Image,TextInput,FlatList,RefreshControl,Modal,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class PointRecord extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            //积分总数
            pointTotalValue:0,
            //积分余额
            pointRemainValue:0,
            //提交时间
            getCreacted:0,
            //员工标识
            staffId:0,
            //积分描述
            pointConfigAlias:"",
            //积分增减
            pointValue:0,
            topBlockLayoutHeight:0,
            staffId:constants.loginUser.staffId
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }       

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/point/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "staffId": constants.loginUser.staffId ? constants.loginUser.staffId : this.state.staffId        
            // "configId": constants.loginUser.configId

        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/point/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "staffId": constants.loginUser.staffId ? constants.loginUser.staffId : this.state.staffId      
            // "configId": constants.loginUser.configId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadPointRecord();
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { recordId } = route.params;
            if (recordId) {
                console.log("=============recordId" + recordId + "");
            }
        }
        if (constants.loginUser.staffId) {
            this.loadPointRecord();
            this.loadPortalStaff();
        } else {
            this.loadUserStaffId();
        }
        
    }

    loadUserStaffId=()=>{
        let url= "/biz/portal/user/get";
        let loadRequest={
            "userId": constants.loginUser.userId
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data.staffId) {
                this.setState({ 
                    staffId:response.data.staffId
                })
                this.loadPointRecord();
                this.loadPortalStaff();
            }
        });
    }

    loadPointRecord=()=>{
        let url= "/biz/point/record/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "staffId": constants.loginUser.staffId ? constants.loginUser.staffId : this.state.staffId
            // "pointConfigId":this.pointConfigId,
        };
        httpPost(url, loadRequest, this.loadPointRecordListCallBack);
    }

    loadPortalStaff=()=>{
        let loadUrl = "/biz/portal/staff/get";
        let loadRequest = {
            "staffId":constants.loginUser.staffId ? constants.loginUser.staffId : this.state.staffId
        };
        httpPost(loadUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({ 
                    pointTotalValue:response.data.pointTotalValue
                })
            }
        });
    }

    loadPointRecordListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.recordId} style={[CommonStyle.innerViewStyle]}>
                
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>积分描述：{item.pointConfigId?item.pointConfigAlias:item.pointRewardAlias}</Text>
                </View>
                { 
                    item.pointValue >0 ?
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>获得积分：{item.pointValue}</Text>
                    </View>
                    :    
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>消耗积分：{item.pointValue}</Text>
                    </View>
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>积分余额：{item.pointBalance}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>奖励时间：{item.gmtCreated}</Text>
                </View>
            </View>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }
    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='我的积分'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={[styles.inputRowStyle,{display:'flex',justifyContent:'center',alignItems:'center',backgroundColor:'#91b893'}]}>
                        <Text style={[styles.titleTextStyle,{color:'#FFFFFF',fontWeight:'bold'}]}>我的积分余额：{this.state.pointTotalValue}</Text>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                        onEndReachedThreshold={0.2}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
   //     height:screenHeight - 70,
   //     backgroundColor:'#FFFFFF'
   // },
   inputRowStyle: {
       paddingLeft: 5,
       height: 40,
       flexDirection: 'row',
       borderWidth:1,
       borderColor:"#FFFFFF",
       backgroundColor:"#FFFFFF",
       borderRadius:5,
       marginTop:5
   },

   leftLabView: {
       height: 45,
       flexDirection: 'row',
       alignItems: 'center',
       paddingLeft: 10,
   },
   leftLabNameTextStyle: {
       fontSize: 18,
   },
   searchInputText: {
       width: screenWidth -100,
       borderColor: '#000000',
       // borderBottomWidth: 1,
       marginRight: 5,
       color: '#A0A0A0',
       fontSize: 16,
       marginLeft: 10,
       paddingLeft: 10,
       paddingRight: 10,
       paddingBottom: 0,
       paddingTop:0
   },
   innerViewStyle: {
       // marginTop:10,
       borderColor:"#F4F4F4",
       borderWidth:8,
   },
   titleViewStyle: {
       flexDirection: 'row',
       justifyContent: 'space-between',
       marginLeft: 10,
       marginRight: 10,
       marginBottom: 5,
       marginTop: 5,
   },
   titleTextStyle: {
       fontSize: 16
   },
   itemContentStyle: {
       flexDirection: 'row',
       alignItems: 'center'
   },
   itemContentImageStyle: {
       width: 120,
       height: 120
   },
   itemContentViewStyle: {
       flexDirection: 'row',
       justifyContent: 'space-between',
       marginLeft: 25
   },
   itemContentChildViewStyle: {
       flexDirection: 'column'
   },
   itemContentLeftChildViewStyle:{
       flexDirection:'column',
       // alignContent:'flex-start',
       // justifyContent:'flex-start',
       // alignItems:'flex-start',
       // width:screenWidth - 180,
       marginLeft:20
   },
   itemContentChildTextStyle: {
       marginLeft: 10,
       marginTop: 15,
       fontSize: 16
   },
   btnRowLeftCancelBtnView:{
       flexDirection:'row',
       marginLeft:10,
       marginRight:10,
       marginBottom:5,
       marginTop:5,
       alignItems:'center',
       justifyContent:'center',
       borderWidth:1,
       borderColor:'#a1a1a1',
       borderRadius:5,
       height:40,
   },
});
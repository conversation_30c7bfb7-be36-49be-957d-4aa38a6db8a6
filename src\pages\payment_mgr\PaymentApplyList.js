import React,{Component} from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, TextInput, Clipboard, Linking,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
import <PERSON>Viewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../utils/CameraRollUtils';

export default class PaymentApplyList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:10,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight: 0,
            //userId:"",
            searchKeyWord:"",
            auditState:"",
            paymentApplyStateSource:[],
            selPaymentApplyStateCode:"all",
            attachImage:"",
            attachImageUrl:"",
            isShowImage: false,
            urls:[],
            display:"N",
            pictureIndex:0
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId} = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        let paymentApplyStateSource = [
            {
                stateCode:'all',
                stateName:'全部',
            },
            {
                stateCode:'1',
                stateName:'发起',
            },
            {
                stateCode:'2',
                stateName:'审核中',
            },
            {
                stateCode:'3',
                stateName:'通过',
            },
            {
                stateCode:'4',
                stateName:'驳回',
            }
        ]
        this.setState({
            paymentApplyStateSource:paymentApplyStateSource,
        })
        this.loadPaymentApplyList();
    }

    loadPaymentApplyList=()=>{
        let url= "/biz/payment/apply/audit/paymentApplyList";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "auditState":this.state.selPaymentApplyStateCode === 'all' ? null : this.state.selPaymentApplyStateCode,
            "searchKeyWord":this.state.searchKeyWord,
            "applyUserName":constants.loginUser.userName,
            "userId":constants.loginUser.userId
        };
        httpPost(url, loadRequest, this._loadPaymentApplyListCallBack);
    }
    _loadPaymentApplyListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N" ,pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    deletePaymentApply=(applyAuditId)=>{
        console.log("=======delete=applyAuditId", applyAuditId);
        let url= "/biz/payment/apply/audit/delete";
        let requestParams={'applyAuditId':applyAuditId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }  
    paymentApplyStateRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    let stateCode=item.stateCode
                    this.setState({
                        selPaymentApplyStateCode:stateCode,
                    })
                    console.log("======selPaymentApplyStateCode==", this.state.selPaymentApplyStateCode,",",item.stateName,",",item.stateCode)
                    let url= "/biz/payment/apply/audit/paymentApplyList";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "auditState":item.stateCode === 'all' ? null : item.stateCode,
                        "searchKeyWord":this.state.searchKeyWord,
                        "applyUserName":constants.loginUser.userName,
                        "userId":constants.loginUser.userId
                    };
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View 
                        key={item.stateCode} 
                        style={[item.stateCode===this.state.selPaymentApplyStateCode? [CommonStyle.selectedBlockItemViewStyle,{borderBottomWidth:2,borderBottomColor:"#CB4139"}] : [CommonStyle.blockItemViewStyle,{}],{paddingLeft:8,paddingRight:8}]}
                        >
                        <Text style={[item.stateCode===this.state.selPaymentApplyStateCode? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    } 
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        //此处导致无法刷新
        //if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
        //    console.log("==========不刷新=====");
        //    return;
        //}
        this.setState({
            currentPage:1
        })
        let url= "/biz/payment/apply/audit/paymentApplyList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditState":this.state.selPaymentApplyStateCode === 'all' ? null : this.state.selPaymentApplyStateCode,
            "searchKeyWord":this.state.searchKeyWord,
            "applyUserName":constants.loginUser.userName,
            "userId":constants.loginUser.userId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N" ,pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        console.log("123")
        this.setState({
            refreshing:true
        })
        this.loadPaymentApplyList();
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/payment/apply/audit/paymentApplyList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditState":this.state.selPaymentApplyStateCode === 'all' ? null : this.state.selPaymentApplyStateCode,
            "searchKeyWord":this.state.searchKeyWord,
            "applyUserName":constants.loginUser.userName,
            "userId":constants.loginUser.userId
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PaymentApplyAdd", 
                {
                    // 传递回调函数
                    operate:"新增",
                    refresh: this.callBackFunction 
                })
            }}>
                {/* <Text style={CommonStyle.headRightText}>新增采购</Text> */}
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }
    callBackFunction=()=>{
        let url= "/biz/payment/apply/audit/paymentApplyList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "applyUserName":constants.loginUser.userName,
            "userId":constants.loginUser.userId
            // "classifyId": this.state.classifyId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }
    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    renderRow=(item, index)=>{
        return (
            <View key={item.applyAuditId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>付款名称：{item.paymentApplyName}</Text>
                    {
                        constants.loginUser.userName == item.applyUserName ? 
                        null
                        :
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                            抄送
                        </Text>
                        
                    }       
                </View>
                         
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>支付对象：{item.customerName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>支付类别：{item.paymentClassName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>支付日期：{item.paymentDate}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>付款金额：{item.paymentAmount}元</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>付款事由：{item.paymentReason?item.paymentReason:"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>支付方式：{item.paymentModeName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>提交人：{item.applyUserName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>提交时间：{item.gmtCreated}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>审核状态：{item.auditStateName}</Text>
                </View>
                {
                    item.auditUserName ? 
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>当前审核人：{item.auditUserName}</Text>
                    </View> :
                    <View />
                }
                

                {
                    item.auditPatchAttach ?
                    (
                        <View>
                            {
                                item.pictureDisplay === "N"?
                                    <View style={[styles.titleViewStyle, { justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                                        <Text style={styles.titleTextStyle}>附件：</Text>
                                        <TouchableOpacity onPress={() => {
                                            if (item.auditPatchAttach) {
                                                var urls = [];
                                                var url = {
                                                    url:constants.image_addr + '/' +  item.auditPatchAttach
                                                } 
                                                urls=urls.concat(url)
                                            }
                                            this.setState({
                                                urls:urls
                                            })
                                            let list = this.state.dataSource;
                                            list.map((elem, index) => {
                                                if(elem.applyAuditId == item.applyAuditId){
                                                    elem.pictureDisplay = "Y"
                                                }
                                            })
                                            this.setState({
                                                dataSource:list
                                            })
                                            // console.log("==============",list)
                                        }}>
                                                <Text style={[styles.titleTextStyle,{color:"#CB4139"}]}>点击展开</Text>
                                        </TouchableOpacity>
                                    </View>
                                :
                                <View>
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>附件：</Text>
                                    </View>
                                    <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                        {
                                           
                                                <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex'}]}>

                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        isShowImage:true,
                                                        // pictureIndex:index
                                                    })
                                                }}>
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.auditPatchAttach) }} style={{ height: 150, width:120 }} />                                                    
                                                </TouchableOpacity>                                                
                                                <Modal visible={this.state.isShowImage} transparent={true}>
                                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} index={this.state.pictureIndex} 
                                                    enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}  
                                                    onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} 
                                                    onSave={()=>{
                                                        saveImage( this.state.urls[this.state.pictureIndex].url)
                                                    }}/>
                                                </Modal>
                                            </View>
                                           
                                        }
                                    </View>
                                    <View style={[styles.titleViewStyle,{justifyContent:'center'}]}>
                                        {
                                            item.pictureDisplay === "Y"?
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    urls:[]
                                                })
                                                let list = this.state.dataSource;
                                                list.map((elem, index) => {
                                                    if(elem.applyAuditId == item.applyAuditId){
                                                        elem.pictureDisplay = "N"
                                                    }
                                                })
                                                this.setState({
                                                    dataSource:list
                                                })
                                                // console.log("==============",list)
                                            }}>
                                                    <Text style={[styles.titleTextStyle,{color:"#CB4139",textAlign:'center'}]}>点击收起</Text>
                                            </TouchableOpacity>
                                            :
                                            <View/>
                                        }
                                    </View>
                                </View>
                                
                            }
                            
                        </View>
                    ):
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>附件：无</Text>
                    </View>
                }


                {
                    constants.loginUser.userName == item.applyUserName?
                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                        
                        {
                            item.auditStateName == "发起审核"?
                            <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                <TouchableOpacity onPress={()=>{
                                    this.props.navigation.navigate("MaterialAuditBacklogDetail", 
                                    {
                                        // 传递参数
                                        auditItemId: item.applyAuditId,
                                        // 传递回调函数
                                        refresh: this.callBackFunction,
                                        auditTypeCode:"PAYMENT_AUDIT"    
                                    })
                                }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 75 ,flexDirection:"row"}]}>
                                    <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={()=>{
                                    if (item.auditScore) {
                                        return;
                                    }
                                    Alert.alert('确认','您确定要删除吗？',[
                                        {
                                            text:"取消", onPress:()=>{
                                            WToast.show({data:'点击了取消'});
                                            // this在这里可用，传到方法里还有问题
                                            // this.props.navigation.goBack();
                                            }
                                        },
                                        {
                                            text:"确定", onPress:()=>{
                                                WToast.show({data:'点击了确定'});
                                                this.deletePaymentApply(item.applyAuditId)
                                            }
                                        }
                                    ]);
                                }}>
                                    <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:80,flexDirection:"row"}
                                        ,item.auditScore ? CommonStyle.disableViewStyle : ""]}>
                                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={()=>{
                                    //此处auditSource
                                        if (item.auditScore) {
                                            return;
                                        }
                                        this.props.navigation.navigate("PaymentApplyAdd", 
                                        {
                                            // 传递参数
                                            applyAuditId: item.applyAuditId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                    }}>
                                    <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:"row"}
                                        ,item.auditScore ? CommonStyle.disableViewStyle : ""]}>
                                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                        <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                    </View>
                                </TouchableOpacity>

                            </View>
                            :
                            <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("PaymengApplyDetail", 
                                {
                                    // 传递参数
                                    auditItemId: item.applyAuditId,
                                    // 传递回调函数
                                    refresh: this.callBackFunction,
                                    auditTypeCode:"PAYMENT_AUDIT"  
                                })
                            }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 75 ,flexDirection:"row"}]}>
                                <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                </View>
                            </TouchableOpacity>
                        }
                    </View>
                    :
                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                        <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("PaymengApplyDetail", 
                            {
                                // 传递参数
                                auditItemId: item.applyAuditId,
                                // 传递回调函数
                                refresh: this.callBackFunction,
                                auditTypeCode:"PAYMENT_AUDIT"  
                            })
                        }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 75 ,flexDirection:"row"}]}>
                                <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                                let url= "/biz/audit/cc/record/modify";
                                let loadRequest={
                                    "recordId": item.ccRecordId,
                                    "ccRecordState":item.ccRecordState == '0AA'?"0AB":"0AA" ,
                                };
                                httpPost(url, loadRequest, (response)=>{
                                    if (response.code == 200 && response.data) {
                                        WToast.show({data:response.data.ccRecordState == '0AA'?"成功标为未读":"成功标为已读"});
                                        this.callBackFunction();
                                    }
                                    else if (response.code == 401) {
                                        WToast.show({data:response.message});
                                        this.props.navigation.navigate("LoginView");
                                    }
                                    else {
                                        WToast.show({data:response.message});
                                    }
                                });
                            }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:'row'},item.ccRecordState == '0AA'?{backgroundColor:'#FA353F'}:{backgroundColor:'#FFB800'}]}>
                                {
                                    item.ccRecordState == '0AA'?
                                    <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/unread.png')}></Image>
                                    :
                                    <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/read.png')}></Image>
                                }
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>{item.ccRecordState == '0AA'?"未读":"已读"}</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                }

            </View>
        )
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title='付款申请'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>

                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.paymentApplyStateSource && this.state.paymentApplyStateSource.length > 0)
                                ?
                                this.state.paymentApplyStateSource.map((item, index) => {
                                    return this.paymentApplyStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                        
                    <View style={{}}>
                        <View style={styles.inputOutsideText}>
                            <View style={styles.inputInsideText}>
                            <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'支付对象'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>

                </View>               
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    innerViewStyle: {
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
    },
    inputOutsideText:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },
    inputInsideText:{
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        marginTop:5,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
});
import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class WorkingShiftRelStaffMgr extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            shiftId:"",
            selStaffIdList:[],
            oldselStaffIdList:[], 
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { shiftId } = route.params;
            if (shiftId) {
                console.log("=============shiftId" + shiftId + "");
                this.setState({
                    shiftId:shiftId
                })
                this.loadStaffList(shiftId);
            }
        }
    }

    // 加载员工列表
    loadStaffList=(shiftId)=>{
        let loadTypeUrl;
        let loadRequest;
        loadTypeUrl = "/biz/portal/staff/list";
        loadRequest={
            "currentPage":1,
            "pageSize":1000,
            "shiftId":shiftId
        }
        httpPost(loadTypeUrl, loadRequest, this.loadStaffListCallBack);
    }

    loadStaffListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            console.log(dataAll)
            let staffDTOList = dataAll;
            this.setState({
                dataSource:staffDTOList
            })
            var staffDTO;

            var selStaffIdList = [];
            for(var index = 0; index < staffDTOList.length; index ++) {
                staffDTO = staffDTOList[index];
                console.log("=========init=staffDTO:", staffDTO);
                if (staffDTO && staffDTO.selected === "Y") {
                    selStaffIdList = selStaffIdList.concat(staffDTO.staffId)
                }
            }
            this.setState({
                selStaffIdList:selStaffIdList,
                oldselStaffIdList:this.copyArr(selStaffIdList),
            })
            console.log("=========init=selStaffIdList:", selStaffIdList);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    copyArr=(varArray)=>{
        let res = []
        for (let i = 0; i < varArray.length; i++) {
         res.push(varArray[i])
        }
        return res
    }


    saveStaff=()=>{
        console.log("=======saveOutsourcing");
        let toastOpts;
        if (!this.state.selStaffIdList) {
            toastOpts = getFailToastOpts("请选择员工");
            WToast.show(toastOpts)
            return;
        }

        let url = "/biz/working/shift/staff/add";
        let requestParams = {
            shiftId:this.state.shiftId,
            staffIdList:this.state.selStaffIdList,
            oldStaffIdList:this.state.oldselStaffIdList
        };
        httpPost(url, requestParams, this.saveStaffCallBack);
    }

    saveStaffCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.saveStaff();
            }}>
             <Image style={{width:28, height:28,marginRight:5}} source={require('../../assets/icon/iconfont/save.png')}></Image>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='班次排班'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.95, justifyContent:'flex-start'}]}>
                        {this.state.dataSource.map((item, key)=>{
                            return(
                                <TouchableOpacity onPress={()=>{
                                    var selStaffIdList = this.state.selStaffIdList;
                                    if (item.selected && item.selected == "Y") {
                                        item.selected = "N";
                                        arrayRemoveItem(selStaffIdList, item.staffId);
                                    }
                                    else {
                                        item.selected = "Y";
                                        selStaffIdList = selStaffIdList.concat(item.staffId)
                                    }
                                    this.setState({
                                        selStaffIdList:selStaffIdList,
                                    })
                                    WToast.show({data:'点击了' + item.staffName});
                                    console.log("======selStaffIdList:", selStaffIdList)
                                    console.log("======oldselStaffIdList:", this.state.oldselStaffIdList)
                                }}>
                                    <View style={[{margin:10, borderRadius:4, padding:10, height:40, backgroundColor:'#F5F5F5'}, (item.selected && item.selected === 'Y') ? {backgroundColor:'red'} : ""]}>
                                        <Text style={[styles.titleTextStyle, (item.selected && item.selected === 'Y') ? {color:'#FFFFFF'} : {color:'#000000'}]}>{item.staffName}</Text>
                                    </View>
                                </TouchableOpacity>
                                
                            )
                        })}
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveStaff.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
     // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },

});
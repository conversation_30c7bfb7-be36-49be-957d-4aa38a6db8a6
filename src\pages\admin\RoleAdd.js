import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,Image,FlatList,TouchableOpacity,Dimensions} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class RoleAdd extends Component {
    constructor(){
        super()
        this.state = {
            operate:"",
            roleId:"",
            roleName:"",
            enterpriseList:[],
            selEnterpriseId:null,
            welcomePageList:[],
            selWelcomePage:"Home",
            roleSort:10
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 加载欢迎页面列表
        this.loadWelcomePageList();
        // 加载子公司列表
        this.loadEnterpriseList();
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { roleId } = route.params;
            if (roleId) {
                console.log("========Edit==roleId:", roleId);
                this.setState({
                    roleId:roleId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/role/get";
                loadRequest={'roleId':roleId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditRoleDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    
    loadEditRoleDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                roleId:response.data.roleId,
                roleName:response.data.roleName,
                selEnterpriseId:response.data.enterpriseId,
                roleSort:response.data.roleSort,
                selWelcomePage:response.data.welcomePage?response.data.welcomePage:"Home"
            })
        }
    }

    loadWelcomePageList=()=>{
        let url= "/biz/portal/welcome/page/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadWelcomePageListCallBack);
    }

    loadWelcomePageListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                welcomePageList:dataAll
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEnterpriseList=()=>{
        let url= "/biz/enterprise/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadEnterpriseListCallBack);
    }

    loadEnterpriseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            dataAll.unshift({"enterpriseId":null, "enterpriseName":"集团公司","enterpriseAbbreviation":"集团公司"})
            this.setState({
                enterpriseList:dataAll
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("RoleList")
            }}>
                <Text style={CommonStyle.headRightText}>角色管理</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveRole =()=> {
        console.log("=======saveRole");
        let toastOpts;
        if (!this.state.roleName) {
            toastOpts = getFailToastOpts("请输入角色名称");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/role/add";
        if (this.state.roleId) {
            console.log("=========Edit===roleId", this.state.roleId)
            url= "/biz/role/modify";
        }
        let requestParams={
            "roleId":this.state.roleId,
            "roleName":this.state.roleName,
            "enterpriseId":this.state.selEnterpriseId,
            "roleSort":this.state.roleSort,
            "welcomePage":this.state.selWelcomePage
        };
        httpPost(url, requestParams, this.saveRoleCallBack);
    }
    
    // 保存回调函数
    saveRoleCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    renderWelcomePageRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    selWelcomePage:item.pageComponentName
                }) 
                console.log("========",item.pageComponentName)
            }}>
                <View key={item.pageId} style={item.pageComponentName===this.state.selWelcomePage? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.pageComponentName===this.state.selWelcomePage? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.pageName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    renderEnterpriseRow=(item)=>{
        return (

            <TouchableOpacity onPress={() => { 
                this.setState({
                    selEnterpriseId:item.enterpriseId
                }) 
            }}>
                <View key={item.enterpriseId} style={item.enterpriseId===this.state.selEnterpriseId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.enterpriseId===this.state.selEnterpriseId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.enterpriseAbbreviation?item.enterpriseAbbreviation:item.enterpriseName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }




    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '角色'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>角色名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入角色名称'}
                            onChangeText={(text) => this.setState({roleName:text})}
                        >
                            {this.state.roleName}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({roleSort:text})}
                        >
                            {this.state.roleSort}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>所属公司</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>

                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.enterpriseList && this.state.enterpriseList.length > 0) 
                            ? 
                            this.state.enterpriseList.map((item, index)=>{
                                return this.renderEnterpriseRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>主页面</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>

                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.welcomePageList && this.state.welcomePageList.length > 0) 
                            ? 
                            this.state.welcomePageList.map((item, index)=>{
                                return this.renderWelcomePageRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>

                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>欢迎页面</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>

                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.welcomePageList && this.state.welcomePageList.length > 0) 
                            ? 
                            this.state.welcomePageList.map((item, index)=>{
                                return this.renderWelcomePageRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View> */}
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveRole.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})